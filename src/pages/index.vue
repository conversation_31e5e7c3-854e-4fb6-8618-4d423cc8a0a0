<template>
    <page-meta :page-style="pageStyle" />
    <view class="init-top" />
    <layout class-name="wrap-tab layout-img IndexRouter">
        <!-- ERP工作台 -->
        <div class="erp-dashboard" bg="hex-fff" mt-24px border-rd-16px p-24px>
            <div class="dashboard-header" mb-20px>
                <h2 text-18px font-600 color-333>工作台</h2>
                <div text-12px color-999>{{ formatDate(new Date()) }}</div>
            </div>
            
                <!-- 快捷入口 -->
            <div class="quick-actions" mb-20px>
                <div class="action-grid" grid="~ cols-4 gap-16px">
                    <PermissionGuard
                        v-for="(action, index) in quickActions" 
                        :key="index"
                        :permission="action.permission"
                        :role="action.role"
                    >
                        <div 
                            class="action-item" 
                            p-16px bg-f5f5f5 border-rd-8px text-center
                            @click="handleQuickAction(action)"
                        >
                            <div class="action-icon" text-32px color-primary mb-8px>
                                <div :class="action.icon" />
                            </div>
                            <div text-12px color-666>{{ action.name }}</div>
                        </div>
                    </PermissionGuard>
                </div>
            </div>
            
            <!-- 待办事项 -->
            <div class="todo-section">
                <div class="section-header" flex="~ items-center justify-between" mb-16px>
                    <h3 text-16px font-500 color-333>待办事项</h3>
                    <div class="todo-count" text-12px color-999>({{ todoList.length }})</div>
                </div>
                <div v-if="todoList.length > 0" class="todo-list">
                    <div 
                        v-for="(todo, index) in todoList" 
                        :key="index"
                        class="todo-item" 
                        p-12px bg-f9f9f9 border-rd-6px mb-12px
                        @click="handleTodoClick(todo)"
                    >
                        <div flex="~ items-center justify-between">
                            <div flex-1>
                                <div text-14px color-333 line-1>{{ todo.title }}</div>
                                <div text-12px color-999 mt-4px>{{ todo.time }}</div>
                            </div>
                            <div class="todo-status" 
                                 :class="getTodoStatusClass(todo.status)"
                                 text-12px px-8px py-4px border-rd-4px>
                                {{ todo.status }}
                            </div>
                        </div>
                    </div>
                </div>
                <div v-else class="empty-todo" text-center py-40px color-999>
                    <div class="i-mdi:check-circle-outline" text-48px mb-16px />
                    <div text-14px>暂无待办事项</div>
                </div>
            </div>
        </div>
        
        <empty-popup v-model="popupShow" title="" @close="onPopupClose" />
    </layout>
</template>

<script setup lang="ts">
import type { LayoutDataType } from '~/types'

defineOptions({
    name: 'IndexRouter',
})

useHead({
    title: 'SkyEye ERP',
})

let popupShow = $ref(false)

const pageStyle = computed(() => {
    if (popupShow) {
        return 'overflow: hidden; height: 100%'
    }
    return 'overflow: inherit'
})

// ERP快捷操作
const quickActions = [
    { 
        name: '考勤打卡', 
        icon: 'i-mdi:clock-check-outline', 
        action: 'attendance',
        permission: 'attendance:checkin',
        role: ['employee', 'manager', 'admin']
    },
    { 
        name: '审批流程', 
        icon: 'i-mdi:file-document-check-outline', 
        action: 'approval',
        permission: 'approval:view',
        role: ['manager', 'admin']
    },
    { 
        name: '客户管理', 
        icon: 'i-mdi:account-group-outline', 
        action: 'crm',
        permission: 'crm:view',
        role: ['sales', 'manager', 'admin']
    },
    { 
        name: '库存查询', 
        icon: 'i-mdi:warehouse', 
        action: 'inventory',
        permission: 'inventory:view',
        role: ['warehouse', 'manager', 'admin']
    },
]

// 待办事项数据
const todoList = ref([
    {
        id: 1,
        title: '审批张三的请假申请',
        time: '2025-08-04 09:30',
        status: '待审批',
        type: 'approval'
    },
    {
        id: 2,
        title: '客户合同到期提醒',
        time: '2025-08-04 14:00',
        status: '待处理',
        type: 'crm'
    },
    {
        id: 3,
        title: '库存预警：办公用品不足',
        time: '2025-08-03 16:45',
        status: '已查看',
        type: 'inventory'
    }
])

// 格式化日期
function formatDate(date: Date): string {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
    const weekDay = weekDays[date.getDay()]
    return `${year}-${month}-${day} ${weekDay}`
}

// 快捷操作处理
function handleQuickAction(action: any) {
    showToast(`跳转到${action.name}`, 'success')
    // TODO: 根据action.action跳转到对应页面
    switch (action.action) {
        case 'attendance':
            // 跳转到考勤页面
            break
        case 'approval':
            // 跳转到审批页面
            break
        case 'crm':
            // 跳转到客户管理页面
            break
        case 'inventory':
            // 跳转到库存查询页面
            break
    }
}

// 待办事项点击处理
function handleTodoClick(todo: any) {
    showToast(`处理待办：${todo.title}`, 'success')
    // TODO: 根据todo.type跳转到对应的处理页面
}

// 获取待办状态样式
function getTodoStatusClass(status: string): string {
    switch (status) {
        case '待审批':
            return 'bg-orange-100 color-orange-600'
        case '待处理':
            return 'bg-red-100 color-red-600'
        case '已查看':
            return 'bg-gray-100 color-gray-600'
        default:
            return 'bg-blue-100 color-blue-600'
    }
}

function onPopupClose(payload: boolean) {
    popupShow = payload
}

provide(layoutDataKey, computed<LayoutDataType>(() => ({
    dataIsLoaded: true,
    hasData: true,
    showEmptySlot: false,
    topBarTitle: 'SkyEye ERP',
    ...defaultShowBar,
})))

provide(dataReloadKey, async () => {
    // TODO: 重新加载工作台数据
    showLoading()
    // 模拟数据加载
    await new Promise(resolve => setTimeout(resolve, 1000))
    uni.hideLoading()
})
</script>

<route lang="json">
{
    "layout": "none",
    "style": {
        "navigationStyle": "custom"
    }
}
</route>
