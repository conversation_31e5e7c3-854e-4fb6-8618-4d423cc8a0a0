export interface UserInfo {
    /** 用户ID */
    user_id: number
    /** 用户名 */
    username: string
    /** 手机号（隐藏显示） */
    mobile: string
    /** 用户昵称 */
    nick_name: string
    /** 真实姓名 */
    real_name: string
    /** 头像ID */
    avatar_id: number
    /** 性别 */
    gender: string
    /** 默认收货地址ID */
    address_id: number
    /** 账户余额 */
    balance: string
    /** 可用积分 */
    points: number
    /** 用户总支付的金额 */
    pay_money: string
    /** 实际消费的金额(不含退款) */
    expend_money: string
    /** 会员等级ID */
    grade_id: number
    /** 注册来源客户端 (APP、H5、小程序等) */
    platform: string
    /** 最后登录时间 */
    last_login_time: number
    /** 头像URL */
    avatar_url: string
    /** 会员等级信息 */
    grade: Objable
    /** 部门ID */
    department_id?: number
    /** 部门名称 */
    department_name?: string
    /** 职位 */
    position?: string
    /** 员工编号 */
    employee_no?: string
    /** 入职时间 */
    join_date?: string
    /** 用户状态 */
    status: 'active' | 'inactive' | 'locked'
}

/** 当前用户详情 */
export interface UserData {
    userInfo: UserInfo
    permissions?: string[]
    roles?: string[]
    menus?: MenuData[]
}

/** 登录请求 */
export interface LoginRequest {
    username: string
    password: string
    captcha?: string
    captcha_key?: string
    remember_me?: boolean
}

/** 登录响应 */
export interface LoginResponse {
    token: string
    userInfo: UserInfo
    permissions: string[]
    roles: string[]
    expires_in: number
    refresh_token?: string
    menus?: MenuData[]
}

/** 菜单数据 */
export interface MenuData {
    id: number
    name: string
    path: string
    icon?: string
    parent_id: number | null
    sort: number
    component?: string
    redirect?: string
    hidden: boolean
    children?: MenuData[]
    meta?: {
        title: string
        icon?: string
        roles?: string[]
        permissions?: string[]
        noCache?: boolean
        breadcrumb?: boolean
        affix?: boolean
    }
}

/** 权限定义 */
export interface Permission {
    id: number
    name: string
    code: string
    description?: string
    module: string
    type: 'page' | 'button' | 'api'
}

/** 角色定义 */
export interface Role {
    id: number
    name: string
    code: string
    description?: string
    permissions: Permission[]
}

/** 部门信息 */
export interface Department {
    id: number
    name: string
    parent_id: number | null
    level: number
    sort: number
    manager_id?: number
    manager_name?: string
    children?: Department[]
}

/** 员工信息 */
export interface Employee {
    id: number
    user_id: number
    employee_no: string
    real_name: string
    mobile: string
    email?: string
    department_id: number
    department_name: string
    position: string
    join_date: string
    status: 'active' | 'inactive' | 'resigned'
    avatar_url?: string
}

/** 登录历史 */
export interface LoginHistory {
    id: number
    user_id: number
    username: string
    login_time: string
    login_ip: string
    login_location?: string
    user_agent: string
    platform: string
    status: 'success' | 'failed'
    fail_reason?: string
}
