// 重新导出user.types.ts中的认证相关类型
export type { LoginRequest, LoginResponse } from '~/pages/user.types'

// 微信小程序登录相关类型（保持向后兼容）
export interface LoginMpWx {
    isBindMobile: boolean
    userId: number
    token: string
}

export interface LoginMpWxMobile {
    userId: number
    token: string
}

// 验证码相关类型
export interface CaptchaResponse {
    captcha_key: string
    captcha_image: string // base64图片
}

// 密码重置相关类型
export interface ResetPasswordRequest {
    username: string
    mobile?: string
    email?: string
    captcha: string
    captcha_key: string
}

export interface ResetPasswordResponse {
    message: string
    reset_token?: string
}
