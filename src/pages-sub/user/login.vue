<template>
    <layout class-name="layout-img UserLogin">
        <div class="login-container" flex="~ col items-center justify-center" min-h-screen p-24px>
            <!-- Logo和标题区域 -->
            <div class="login-header" text-center mb-60px>
                <div class="app-logo" w-80px h-80px mx-auto mb-20px>
                    <div class="i-mdi:office-building-outline" text-80px color-primary />
                </div>
                <h1 class="app-title" text-28px font-600 color-333 mb-8px>SkyEye ERP</h1>
                <p class="app-subtitle" text-14px color-666>企业资源规划管理系统</p>
            </div>

            <!-- 登录表单 -->
            <div class="login-form" w-full max-w-320px>
                <wd-form ref="formRef" :model="loginForm" :rules="loginRules" label-position="top">
                    <wd-cell-group>
                        <wd-input 
                            v-model="loginForm.username" 
                            label="用户名" 
                            prop="username"
                            placeholder="请输入用户名"
                            prefix-icon="user"
                            clearable
                        />
                        
                        <wd-input 
                            v-model="loginForm.password" 
                            label="密码" 
                            prop="password"
                            placeholder="请输入密码"
                            type="password"
                            prefix-icon="lock"
                            :show-password="true"
                            clearable
                        />
                    </wd-cell-group>
                    
                    <!-- 记住密码 -->
                    <div class="login-options" flex="~ items-center justify-between" mt-16px mb-32px>
                        <div flex="~ items-center">
                            <wd-checkbox 
                                v-model="loginForm.remember_me" 
                                size="small"
                                inline-label
                            >
                                记住密码
                            </wd-checkbox>
                        </div>
                        <div class="forgot-password" text-12px color-primary @click="handleForgotPassword">
                            忘记密码？
                        </div>
                    </div>
                    
                    <!-- 登录按钮 -->
                    <wd-button 
                        type="primary" 
                        size="large" 
                        block 
                        :loading="userStore.loginLoading"
                        @click="handleLogin"
                    >
                        {{ userStore.loginLoading ? '登录中...' : '登录' }}
                    </wd-button>
                </wd-form>
            </div>
            
            <!-- 其他选项 -->
            <div class="login-footer" text-center mt-40px>
                <div class="divider" flex="~ items-center" mb-20px>
                    <div class="line" flex-1 h-1px bg-e5e5e5 />
                    <span class="text" mx-16px text-12px color-999>其他登录方式</span>
                    <div class="line" flex-1 h-1px bg-e5e5e5 />
                </div>
                
                <div class="other-login" flex="~ items-center justify-center gap-24px">
                    <!-- #ifdef MP-WEIXIN -->
                    <div 
                        class="login-icon wechat"
                        w-48px h-48px border-rd-50% bg-f5f5f5 flex="~ items-center justify-center"
                        @click="handleWechatLogin"
                    >
                        <div class="i-mdi:wechat" text-24px color-green />
                    </div>
                    <!-- #endif -->
                    
                    <div 
                        class="login-icon demo"
                        w-48px h-48px border-rd-50% bg-f5f5f5 flex="~ items-center justify-center"
                        @click="handleDemoLogin"
                    >
                        <div class="i-mdi:account-demo" text-24px color-blue />
                    </div>
                </div>
                
                <div class="demo-hint" text-12px color-999 mt-16px>
                    演示账号：admin / 123456
                </div>
            </div>
        </div>
    </layout>
</template>

<script setup lang="ts">
import type { FormInstance, FormRules } from 'wot-design-uni/components/wd-form/types'
import type { LoginRequest } from './login.types'
import type { LayoutDataType } from '~/types'

defineOptions({
    name: 'UserLogin',
})

useHead({
    title: 'SkyEye ERP - 登录',
})

const userStore = useUserStore()
const formRef = ref<FormInstance>()

// 登录表单数据
const loginForm = reactive<LoginRequest>({
    username: '',
    password: '',
    remember_me: false,
})

// 表单验证规则
const loginRules: FormRules = {
    username: [
        { required: true, message: '请输入用户名', trigger: ['change', 'blur'] },
        { 
            min: 3, 
            max: 20, 
            message: '用户名长度为3-20个字符', 
            trigger: ['change', 'blur'] 
        },
    ],
    password: [
        { required: true, message: '请输入密码', trigger: ['change', 'blur'] },
        { 
            min: 6, 
            max: 20, 
            message: '密码长度为6-20个字符', 
            trigger: ['change', 'blur'] 
        },
    ],
}

// 登录处理
async function handleLogin() {
    const valid = await formRef.value?.validate()
    if (!valid) return
    
    const success = await userStore.login(loginForm)
    if (success) {
        showToast('登录成功', 'success')
        
        // 跳转到首页
        uni.reLaunch({
            url: '/pages/index'
        })
    } else {
        showToast('登录失败，请检查用户名和密码')
    }
}

// 演示登录
async function handleDemoLogin() {
    loginForm.username = 'admin'
    loginForm.password = '123456'
    await handleLogin()
}

// 微信登录
async function handleWechatLogin() {
    // #ifdef MP-WEIXIN
    try {
        uni.showLoading({ title: '正在登录...', mask: true })
        
        const userProfile = await uniAsync.getUserProfile({ desc: '获取头像昵称' })
        const loginResult = await uniAsync.login()
        
        // 调用微信登录API
        const { code, data } = await $api.post('/auth/wechat-login', {
            code: loginResult.code,
            userInfo: userProfile.rawData,
        })
        
        if (code === 200 && data) {
            // 保存登录信息
            userStore.token = data.token
            userStore.userInfo = data.userInfo
            userStore.permissions = data.permissions || []
            userStore.roles = data.roles || []
            userStore.isAuthenticated = true
            
            ls.set('token', data.token)
            ls.set('user-info', data.userInfo)
            ls.set('permissions', data.permissions || [])
            ls.set('roles', data.roles || [])
            
            showToast('登录成功', 'success')
            uni.reLaunch({ url: '/pages/index' })
        } else {
            showToast('微信登录失败')
        }
    } catch (error) {
        console.error('Wechat login error:', error)
        showToast('微信登录失败')
    } finally {
        uni.hideLoading()
    }
    // #endif
}

// 忘记密码
function handleForgotPassword() {
    showToast('请联系管理员重置密码')
    // TODO: 实现找回密码功能
}

// 自动填充记住的密码
onMounted(() => {
    const rememberedUsername = ls.get<string>('remembered_username')
    const rememberedPassword = ls.get<string>('remembered_password')
    
    if (rememberedUsername && rememberedPassword) {
        loginForm.username = rememberedUsername
        loginForm.password = rememberedPassword
        loginForm.remember_me = true
    }
})

// 记住密码处理
watch(() => loginForm.remember_me, (rememberMe) => {
    if (rememberMe) {
        ls.set('remembered_username', loginForm.username)
        ls.set('remembered_password', loginForm.password)
    } else {
        ls.remove('remembered_username')
        ls.remove('remembered_password')
    }
})

provide(layoutDataKey, computed<LayoutDataType>(() => ({
    dataIsLoaded: true,
    hasData: true,
    showEmptySlot: false,
    topBarTitle: '',
    ...defaultHideBar,
})))

provide(dataReloadKey, async () => {
    // 登录页面不需要重新加载数据
})
</script>

<style lang="scss" scoped>
.login-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.login-header {
    .app-logo {
        filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
    }
    
    .app-title {
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        color: white;
    }
    
    .app-subtitle {
        color: rgba(255, 255, 255, 0.8);
    }
}

.login-form {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 32px 24px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.login-options {
    .forgot-password {
        cursor: pointer;
        transition: all 0.2s ease;
        
        &:hover {
            opacity: 0.7;
        }
    }
}

.login-footer {
    .divider {
        .line {
            background: rgba(255, 255, 255, 0.3);
        }
        
        .text {
            color: rgba(255, 255, 255, 0.7);
        }
    }
    
    .login-icon {
        cursor: pointer;
        transition: all 0.2s ease;
        background: rgba(255, 255, 255, 0.9);
        
        &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        &.wechat:hover {
            background: rgba(34, 193, 195, 0.1);
        }
        
        &.demo:hover {
            background: rgba(45, 156, 255, 0.1);
        }
    }
    
    .demo-hint {
        color: rgba(255, 255, 255, 0.7);
    }
}
</style>

<route lang="json">
{
    "style": {
        "navigationStyle": "custom"
    }
}
</route>
