import type { MenuData } from '~/pages/user.types'

/**
 * 权限管理Composable
 * 遵循单一职责原则，专门处理权限相关逻辑
 */
export function usePermission() {
    const { isLoggedIn, userPermissions, userRoles, currentUser } = useAuth()

    /**
     * 检查是否有指定权限
     * @param permission 权限码
     * @returns boolean
     */
    function hasPermission(permission: string | string[]): boolean {
        if (!isLoggedIn.value) return false
        
        // 管理员拥有所有权限
        if (userRoles.value.includes('admin')) return true
        
        if (Array.isArray(permission)) {
            // 检查是否拥有任意一个权限
            return permission.some(p => userPermissions.value.includes(p))
        }
        
        return userPermissions.value.includes(permission)
    }

    /**
     * 检查是否有指定角色
     * @param role 角色码
     * @returns boolean
     */
    function hasRole(role: string | string[]): boolean {
        if (!isLoggedIn.value) return false
        
        if (Array.isArray(role)) {
            return role.some(r => userRoles.value.includes(r))
        }
        
        return userRoles.value.includes(role)
    }

    /**
     * 检查是否有任意一个权限
     * @param permissions 权限码数组
     * @returns boolean
     */
    function hasAnyPermission(permissions: string[]): boolean {
        return hasPermission(permissions)
    }

    /**
     * 检查是否拥有所有权限
     * @param permissions 权限码数组
     * @returns boolean
     */
    function hasAllPermissions(permissions: string[]): boolean {
        if (!isLoggedIn.value) return false
        if (userRoles.value.includes('admin')) return true
        
        return permissions.every(p => userPermissions.value.includes(p))
    }

    /**
     * 检查菜单权限
     * @param menu 菜单项
     * @returns boolean
     */
    function hasMenuAccess(menu: MenuData): boolean {
        if (!isLoggedIn.value) return false
        if (userRoles.value.includes('admin')) return true
        
        // 检查菜单的角色要求
        if (menu.meta?.roles && menu.meta.roles.length > 0) {
            if (!menu.meta.roles.some(role => userRoles.value.includes(role))) {
                return false
            }
        }
        
        // 检查菜单的权限要求
        if (menu.meta?.permissions && menu.meta.permissions.length > 0) {
            if (!menu.meta.permissions.some(permission => userPermissions.value.includes(permission))) {
                return false
            }
        }
        
        return true
    }

    /**
     * 过滤用户可访问的菜单
     * @param menus 菜单列表
     * @returns 过滤后的菜单列表
     */
    function filterMenus(menus: MenuData[]): MenuData[] {
        return menus.filter(menu => {
            if (!hasMenuAccess(menu)) return false
            
            // 递归过滤子菜单
            if (menu.children && menu.children.length > 0) {
                menu.children = filterMenus(menu.children)
            }
            
            return true
        })
    }

    /**
     * 检查页面访问权限
     * @param path 页面路径
     * @param requiredPermissions 需要的权限
     * @param requiredRoles 需要的角色
     * @returns boolean
     */
    function hasPageAccess(
        path: string, 
        requiredPermissions?: string[], 
        requiredRoles?: string[]
    ): boolean {
        if (!isLoggedIn.value) return false
        if (userRoles.value.includes('admin')) return true
        
        // 检查角色要求
        if (requiredRoles && requiredRoles.length > 0) {
            if (!hasAnyRole(requiredRoles)) return false
        }
        
        // 检查权限要求
        if (requiredPermissions && requiredPermissions.length > 0) {
            if (!hasAnyPermission(requiredPermissions)) return false
        }
        
        return true
    }

    /**
     * 检查是否有任意一个角色
     * @param roles 角色数组
     * @returns boolean
     */
    function hasAnyRole(roles: string[]): boolean {
        return roles.some(role => userRoles.value.includes(role))
    }

    /**
     * 获取用户部门权限范围
     * @returns 部门权限范围
     */
    function getDepartmentScope(): 'all' | 'department' | 'self' {
        if (userRoles.value.includes('admin')) return 'all'
        if (userRoles.value.includes('department_manager')) return 'department'
        return 'self'
    }

    /**
     * 检查是否可以访问指定用户的数据
     * @param targetUserId 目标用户ID
     * @returns boolean
     */
    function canAccessUserData(targetUserId: number): boolean {
        if (!isLoggedIn.value || !currentUser.value) return false
        
        const scope = getDepartmentScope()
        
        switch (scope) {
            case 'all':
                return true
            case 'department':
                // TODO: 实现部门权限检查逻辑
                return true
            case 'self':
                return currentUser.value.user_id === targetUserId
            default:
                return false
        }
    }

    return {
        hasPermission,
        hasRole,
        hasAnyPermission,
        hasAllPermissions,
        hasAnyRole,
        hasMenuAccess,
        filterMenus,
        hasPageAccess,
        getDepartmentScope,
        canAccessUserData,
        
        // 计算属性
        isAdmin: computed(() => userRoles.value.includes('admin')),
        isDepartmentManager: computed(() => userRoles.value.includes('department_manager')),
        currentDepartmentId: computed(() => currentUser.value?.department_id),
    }
}