import type { AxiosAdapter, AxiosRequestConfig, AxiosResponse, Canceler } from 'axios'
import axios, { CancelToken } from 'axios'
import { uniAdapter } from 'fant-axios-adapter'

// 请求取消管理
const pendingRequests = new Map<string, Canceler>()

// 基础配置 - 与主系统保持一致
const baseHeaders = {
    'X-Requested-With': 'XMLHttpRequest',
    'Content-Type': 'application/x-www-form-urlencoded',
}

const baseConfig: AxiosRequestConfig = {
    headers: baseHeaders,
    timeout: import.meta.env.VITE_APP_ENV === 'production' ? 300000 : 10000,
    withCredentials: false,
}

// 设置适配器和基础URL
axios.defaults.adapter = uniAdapter as AxiosAdapter

// #ifdef H5
axios.defaults.baseURL = import.meta.env.VITE_APP_API
// #endif
// #ifndef H5
axios.defaults.baseURL = import.meta.env.VITE_APP_MP_API
// #endif

/**
 * 生成请求唯一标识
 */
function generateRequestKey(config: AxiosRequestConfig): string {
    const { method, url, params, data } = config
    return `${method}:${url}:${JSON.stringify(params)}:${JSON.stringify(data)}`
}

/**
 * 添加请求到待处理列表
 */
function addPendingRequest(config: AxiosRequestConfig) {
    const requestKey = generateRequestKey(config)
    config.cancelToken = new CancelToken((cancel) => {
        if (!pendingRequests.has(requestKey)) {
            pendingRequests.set(requestKey, cancel)
        }
    })
}

/**
 * 移除已完成的请求
 */
function removePendingRequest(config: AxiosRequestConfig) {
    const requestKey = generateRequestKey(config)
    if (pendingRequests.has(requestKey)) {
        const cancel = pendingRequests.get(requestKey)
        cancel?.(`请求已取消: ${requestKey}`)
        pendingRequests.delete(requestKey)
    }
}

/**
 * 清除所有待处理请求
 */
export function clearPendingRequests() {
    pendingRequests.forEach((cancel, key) => {
        cancel(`批量取消请求: ${key}`)
    })
    pendingRequests.clear()
}

// 请求拦截器
axios.interceptors.request.use(
    (config) => {
        // 取消重复请求
        removePendingRequest(config)
        addPendingRequest(config)
        
        // 自动添加token - 与主系统保持一致
        const token = ls.get<string>('userToken')
        if (token && config.headers) {
            config.headers['userToken'] = token
        }
        
        // 添加请求ID用于追踪
        config.headers = {
            ...config.headers,
            'X-Request-ID': `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        }
        
        return config
    },
    (error) => {
        console.error('Request interceptor error:', error)
        return Promise.reject(error)
    }
)

// 响应拦截器
axios.interceptors.response.use(
    (response) => {
        // 移除已完成的请求
        removePendingRequest(response.config)
        return response
    },
    async (error) => {
        const config = error.config
        
        // 移除失败的请求
        if (config) {
            removePendingRequest(config)
        }
        
        // 处理取消的请求
        if (axios.isCancel(error)) {
            console.log('Request cancelled:', error.message)
            return Promise.reject(error)
        }
        
        // 处理网络错误
        if (!error.response) {
            const networkError = {
                code: -1,
                message: '网络连接异常，请检查网络设置',
                data: null,
                config: error.config,
                status: -1,
            }
            return Promise.resolve({ data: networkError })
        }
        
        const { status } = error.response
        
        // Token过期，尝试刷新
        if (status === 401 && config && !config._isRetryRequest) {
            try {
                const userStore = useUserStore()
                const refreshSuccess = await userStore.refreshToken()
                
                if (refreshSuccess) {
                    config._isRetryRequest = true
                    const newToken = ls.get<string>('userToken')
                    if (newToken && config.headers) {
                        config.headers['userToken'] = newToken
                    }
                    return axios(config)
                }
            } catch (refreshError) {
                console.error('Token refresh failed:', refreshError)
                // Token刷新失败，跳转登录页
                await handleAuthError()
            }
        }
        
        // 构造统一错误响应
        const errorResponse = createErrorResponse(error)
        return Promise.resolve({ data: errorResponse })
    }
)

/**
 * 处理认证错误
 */
async function handleAuthError() {
    const userStore = useUserStore()
    
    // 清除所有待处理请求
    clearPendingRequests()
    
    // 清除用户状态并跳转登录
    try {
        await userStore.logout()
    } catch (error) {
        console.error('Logout error:', error)
        // 强制清除本地状态 - 与主系统保持一致
        ls.remove('userToken')
        ls.remove('userMation')
        ls.remove('authPoint')
        ls.remove('userMenu')
        uni.reLaunch({ url: '/pages-sub/user/login' })
    }
}

/**
 * 创建统一错误响应
 */
function createErrorResponse(error: any): ResponseData<any> {
    const status = error.response?.status || -1
    const statusText = error.response?.statusText || error.message
    
    let message = '请求失败'
    let code = status
    
    switch (status) {
        case 400:
            message = '请求参数错误'
            break
        case 401:
            message = '登录已过期，请重新登录'
            break
        case 403:
            message = '没有权限访问该资源'
            break
        case 404:
            message = '请求的资源不存在'
            break
        case 408:
            message = '请求超时'
            break
        case 500:
            message = '服务器内部错误'
            break
        case 502:
            message = '网关错误'
            break
        case 503:
            message = '服务暂时不可用'
            break
        case 504:
            message = '网关超时'
            break
        default:
            if (status === -1) {
                message = '网络连接异常'
                code = -1
            } else {
                message = `请求失败 (${status})`
            }
    }
    
    return {
        code,
        status,
        message,
        data: error.response?.data || null,
        info: statusText,
    }
}

/**
 * 检查响应状态 - 与主系统保持一致
 */
function checkStatus(response: AxiosResponse): ResponseData<any> {
    const res = response.data
    
    // 主系统响应格式：returnCode: 0 表示成功
    if (res.returnCode === 0) {
        return res
    } else {
        // 处理主系统的session status
        let sessionstatus = response.headers.sessionstatus
        let msg = res.returnMessage
        
        if (sessionstatus === 'TIMEOUT') {
            handleAuthError()
            throw new Error('登录已过期，请重新登录')
        } else if (sessionstatus === 'NOAUTHPOINT') {
            msg = '没有权限访问'
        } else if (sessionstatus === 'APINOTFOUND') {
            msg = '接口不存在'
        }
        
        throw new Error(msg || '请求失败')
    }
}

/**
 * 统一处理响应码 - 与主系统保持一致
 */
function processResponse(data: ResponseData<any>): ResponseData<any> {
    // 主系统成功响应格式：returnCode: 0
    if (data.returnCode === 0) {
        return {
            ...data,
            code: 200,
            status: 200,
            message: data.returnMessage || 'success'
        }
    }
    
    // 业务错误处理
    const message = data.returnMessage || '请求失败'
    showToast(message)
    
    return {
        ...data,
        code: data.returnCode || -1,
        status: data.returnCode || -1,
        message
    }
}

/**
 * 重试机制装饰器
 */
async function withRetry<T>(
    fn: () => Promise<T>, 
    maxRetries = 3, 
    delay = 1000
): Promise<T> {
    let lastError: any
    
    for (let i = 0; i <= maxRetries; i++) {
        try {
            return await fn()
        } catch (error) {
            lastError = error
            
            if (i === maxRetries) {
                break
            }
            
            // 延迟重试
            await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)))
        }
    }
    
    throw lastError
}

/**
 * 企业级API封装
 */
export const $api: ApiType = {
    /**
     * GET请求
     */
    async get<T>(url: string, params: Objable = {}, headers: Objable = {}, options: RequestOptions = {}) {
        const { checkCode = true, retry = false } = options
        
        const request = async () => {
            const response = await axios({
                ...baseConfig,
                method: 'get',
                url,
                params,
                headers: { ...baseConfig.headers, ...headers },
                timeout: options.timeout || baseConfig.timeout,
            })
            return checkStatus(response)
        }
        
        const result = retry ? await withRetry(request) : await request()
        return checkCode ? processResponse(result) : result
    },
    
    /**
     * POST请求
     */
    async post<T>(url: string, data: Objable = {}, headers: Objable = {}, options: RequestOptions = {}) {
        const { checkCode = true, retry = false } = options
        
        const request = async () => {
            const response = await axios({
                ...baseConfig,
                method: 'post',
                url,
                data,
                headers: { ...baseConfig.headers, ...headers },
                timeout: options.timeout || baseConfig.timeout,
            })
            return checkStatus(response)
        }
        
        const result = retry ? await withRetry(request) : await request()
        return checkCode ? processResponse(result) : result
    },
    
    /**
     * PUT请求
     */
    async put<T>(url: string, data: Objable = {}, headers: Objable = {}, options: RequestOptions = {}) {
        const { checkCode = true, retry = false } = options
        
        const request = async () => {
            const response = await axios({
                ...baseConfig,
                method: 'put',
                url,
                data,
                headers: { ...baseConfig.headers, ...headers },
                timeout: options.timeout || baseConfig.timeout,
            })
            return checkStatus(response)
        }
        
        const result = retry ? await withRetry(request) : await request()
        return checkCode ? processResponse(result) : result
    },
    
    /**
     * DELETE请求
     */
    async delete<T>(url: string, data: Objable = {}, headers: Objable = {}, options: RequestOptions = {}) {
        const { checkCode = true, retry = false } = options
        
        const request = async () => {
            const response = await axios({
                ...baseConfig,
                method: 'delete',
                url,
                data,
                headers: { ...baseConfig.headers, ...headers },
                timeout: options.timeout || baseConfig.timeout,
            })
            return checkStatus(response)
        }
        
        const result = retry ? await withRetry(request) : await request()
        return checkCode ? processResponse(result) : result
    },
    
    /**
     * 文件下载
     */
    async downFile(url: string, method = 'get', data?: Objable) {
        const config: AxiosRequestConfig = {
            ...baseConfig,
            responseType: 'arraybuffer',
            method,
            url,
            timeout: 300000, // 5分钟超时
        }
        
        if (method === 'get') {
            config.params = data
        } else {
            config.data = data
        }
        
        return await axios(config)
    },
    
    /**
     * 上传文件
     */
    async upload<T>(
        url: string, 
        file: File | Blob, 
        options: UploadOptions = {}
    ): Promise<ResponseData<T>> {
        const { 
            onProgress, 
            headers = {}, 
            data = {},
            timeout = 300000 
        } = options
        
        const formData = new FormData()
        formData.append('file', file)
        
        // 添加额外数据
        Object.keys(data).forEach(key => {
            formData.append(key, data[key])
        })
        
        const response = await axios({
            ...baseConfig,
            method: 'post',
            url,
            data: formData,
            headers: {
                ...baseConfig.headers,
                ...headers,
                'Content-Type': 'multipart/form-data',
            },
            timeout,
            onUploadProgress: onProgress,
        })
        
        return processResponse(checkStatus(response))
    },
    
    // 向后兼容的方法
    RESTful: function(url: string, method = 'get', data?: Objable, headers?: Objable, checkCode = true) {
        const options = { checkCode }
        
        switch (method.toLowerCase()) {
            case 'get':
                return this.get(url, data, headers, options)
            case 'post':
                return this.post(url, data, headers, options)
            case 'put':
                return this.put(url, data, headers, options)
            case 'delete':
                return this.delete(url, data, headers, options)
            default:
                throw new Error(`Unsupported method: ${method}`)
        }
    },
    
    $RESTful: function(url: string, method = 'get', data?: Objable, headers?: Objable) {
        return this.RESTful(url, method, data, headers, false)
    }
}

// 请求配置接口
interface RequestOptions {
    checkCode?: boolean
    retry?: boolean
    timeout?: number
}

// 上传配置接口
interface UploadOptions {
    onProgress?: (progressEvent: any) => void
    headers?: Objable
    data?: Objable
    timeout?: number
}

// 全局暴露（调试用）
// #ifdef H5
window.axios = axios
window.$$api = $api
window.clearPendingRequests = clearPendingRequests
// #endif
