import type { UserInfo, LoginRequest, LoginResponse, MenuData } from '~/pages/user.types'
import { defineStore } from 'pinia'

export const useUserStore = defineStore('userInfo', () => {
    // Token与用户信息存储键 - 与主系统保持一致
    const tokenKey = 'userToken'
    const userKey = 'userMation'
    const authPointKey = 'authPoint'
    const menuKey = 'userMenu'

    // 状态管理
    const user = ref<UserInfo | null>(ls.get<UserInfo>(userKey) || null)
    const token = ref<string>(ls.get<string>(tokenKey) || '')
    const isLoggedIn = computed(() => !!token.value && !!user.value)
    const loginLoading = ref(false)

    /**
     * 用户登录 - 与主系统保持一致
     */
    async function login(credentials: LoginRequest): Promise<boolean> {
        try {
            loginLoading.value = true
            
            const response = await $api.post<LoginResponse>('/login', {
                username: credentials.username,
                password: credentials.password,
                loginType: credentials.loginType || 'account'
            })
            
            if (response.returnCode === 0 && response.bean) {
                const userInfo = response.bean
                
                // 与主系统保持一致的存储方式
                ls.set(tokenKey, userInfo.userToken)
                ls.set(userKey, userInfo)
                
                // 存储权限点和菜单信息
                if (userInfo.authPoints) {
                    ls.set(authPointKey, JSON.stringify(userInfo.authPoints))
                }
                if (userInfo.menuList) {
                    ls.set(menuKey, JSON.stringify(userInfo.menuList))
                }
                
                // 更新状态
                user.value = userInfo
                token.value = userInfo.userToken
                
                showToast('登录成功', 'success')
                return true
            } else {
                showToast(response.returnMessage || '登录失败', 'error')
                return false
            }
        } catch (error) {
            console.error('Login error:', error)
            showToast('登录失败', 'error')
            return false
        } finally {
            loginLoading.value = false
        }
    }

    /**
     * 用户登出 - 与主系统保持一致
     */
    async function logout() {
        try {
            // 调用登出API
            await $api.post('/logout')
        } catch (error) {
            console.error('Logout API failed:', error)
        } finally {
            // 清除所有存储的用户相关数据 - 与主系统保持一致
            ls.remove(tokenKey)
            ls.remove(userKey)
            ls.remove(authPointKey)
            ls.remove(menuKey)
            
            // 重置状态
            user.value = null
            token.value = ''
            
            // 跳转到登录页
            uni.reLaunch({
                url: '/pages-sub/user/login'
            })
        }
    }

    /**
     * 刷新Token - 与主系统保持一致
     */
    async function refreshToken(): Promise<boolean> {
        try {
            const response = await $api.post<{ userToken: string }>('/refresh-token')
            if (response.returnCode === 0 && response.bean?.userToken) {
                const newToken = response.bean.userToken
                token.value = newToken
                ls.set(tokenKey, newToken)
                
                // 更新用户信息中的token
                if (user.value) {
                    user.value.userToken = newToken
                    ls.set(userKey, user.value)
                }
                
                return true
            }
            return false
        } catch (error) {
            console.error('Refresh token failed:', error)
            await logout()
            return false
        }
    }

    /**
     * 获取用户信息
     */
    async function getUserInfo(): Promise<boolean> {
        if (!token.value) return false
        
        try {
            const response = await $api.get<UserInfo>('/user/info')
            if (response.returnCode === 0 && response.bean) {
                user.value = response.bean
                ls.set(userKey, response.bean)
                return true
            }
            return false
        } catch (error) {
            console.error('Get user info failed:', error)
            return false
        }
    }

    /**
     * 检查用户权限 - 与主系统保持一致
     */
    function hasPermission(permission: string | string[]): boolean {
        if (!isLoggedIn.value) return false
        
        const authList = ls.get<string>(authPointKey)
        if (!authList) return false
        
        try {
            const authPoints = JSON.parse(authList)
            const permissions = Array.isArray(permission) ? permission : [permission]
            
            return permissions.some(perm => {
                return authPoints.some((item: any) => {
                    // 检查主权限点
                    if (item.menuNum === perm) return true
                    
                    // 检查子权限点
                    if (item.children && item.children.length > 0) {
                        return item.children.some((group: any) => {
                            if (group.children && group.children.length > 0) {
                                return group.children.some((auth: any) => auth.menuNum === perm)
                            }
                            return false
                        })
                    }
                    return false
                })
            })
        } catch (error) {
            console.error('Permission check error:', error)
            return false
        }
    }

    /**
     * 检查用户角色 - 与主系统保持一致
     */
    function hasRole(role: string | string[]): boolean {
        if (!isLoggedIn.value || !user.value) return false
        
        const userRoles = user.value.roles || []
        const rolesArray = Array.isArray(role) ? role : [role]
        
        return rolesArray.some(r => userRoles.some((ur: any) => ur.roleKey === r || ur.roleName === r))
    }

    /**
     * 检查所有权限
     */
    function hasAllPermissions(permissions: string[]): boolean {
        return permissions.every(permission => hasPermission(permission))
    }

    /**
     * 获取用户菜单 - 与主系统保持一致
     */
    function getUserMenus(): MenuData[] {
        const menuData = ls.get<string>(menuKey)
        if (!menuData) return []
        
        try {
            return JSON.parse(menuData)
        } catch (error) {
            console.error('Menu data parse error:', error)
            return []
        }
    }

    /**
     * 获取用户ID
     */
    function getCurrentUserId(): string | number | null {
        return user.value?.id || null
    }

    /**
     * 获取当前用户
     */
    function getCurrentUser(): UserInfo | null {
        return user.value
    }

    /**
     * 获取用户Token
     */
    function getUserToken(): string {
        return token.value
    }

    return {
        // 状态
        user: readonly(user),
        token: readonly(token),
        isLoggedIn,
        loginLoading: readonly(loginLoading),
        
        // 方法
        login,
        logout,
        refreshToken,
        getUserInfo,
        hasPermission,
        hasRole,
        hasAllPermissions,
        getUserMenus,
        getCurrentUserId,
        getCurrentUser,
        getUserToken,
    }
})

// 导出便捷的全局使用方法
export const useAuth = () => {
    const userStore = useUserStore()
    
    return {
        isLoggedIn: userStore.isLoggedIn,
        currentUser: userStore.user,
        login: userStore.login,
        logout: userStore.logout,
        hasPermission: userStore.hasPermission,
        hasRole: userStore.hasRole,
    }
}