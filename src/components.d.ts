/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by vite-plugin-uni-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    EmptyComponents: typeof import('./components/empty-components.vue')['default']
    EmptyPopup: typeof import('./components/empty-popup.vue')['default']
    IsEmpty: typeof import('./components/is-empty.vue')['default']
    Layout: typeof import('./components/layout.vue')['default']
    LcyCheckbox: typeof import('./components/lcy-checkbox.vue')['default']
    PermissionGuard: typeof import('./components/PermissionGuard.vue')['default']
    TnButton: typeof import('@lincy/tnui-vue3-uniapp/components/button/src/button.vue')['default']
    TnCheckbox: typeof import('@lincy/tnui-vue3-uniapp/components/checkbox/src/checkbox.vue')['default']
    TnIcon: typeof import('@lincy/tnui-vue3-uniapp/components/icon/src/icon.vue')['default']
    TnLoading: typeof import('@lincy/tnui-vue3-uniapp/components/loading/src/loading.vue')['default']
    TnPhotoAlbum: typeof import('@lincy/tnui-vue3-uniapp/components/photo-album/src/photo-album.vue')['default']
    TnPopup: typeof import('@lincy/tnui-vue3-uniapp/components/popup/src/popup.vue')['default']
    TnScrollList: typeof import('@lincy/tnui-vue3-uniapp/components/scroll-list/src/scroll-list.vue')['default']
    WdButton: typeof import('wot-design-uni/components/wd-button/wd-button.vue')['default']
    WdCell: typeof import('wot-design-uni/components/wd-cell/wd-cell.vue')['default']
    WdCellGroup: typeof import('wot-design-uni/components/wd-cell-group/wd-cell-group.vue')['default']
    WdForm: typeof import('wot-design-uni/components/wd-form/wd-form.vue')['default']
    WdIcon: typeof import('wot-design-uni/components/wd-icon/wd-icon.vue')['default']
    WdInput: typeof import('wot-design-uni/components/wd-input/wd-input.vue')['default']
    WdLoading: typeof import('wot-design-uni/components/wd-loading/wd-loading.vue')['default']
    WdLoadmore: typeof import('wot-design-uni/components/wd-loadmore/wd-loadmore.vue')['default']
    WdNavbar: typeof import('wot-design-uni/components/wd-navbar/wd-navbar.vue')['default']
    WdNavbarCapsule: typeof import('wot-design-uni/components/wd-navbar-capsule/wd-navbar-capsule.vue')['default']
    WdPopup: typeof import('wot-design-uni/components/wd-popup/wd-popup.vue')['default']
    WdStatusTip: typeof import('wot-design-uni/components/wd-status-tip/wd-status-tip.vue')['default']
    WdSwiper: typeof import('wot-design-uni/components/wd-swiper/wd-swiper.vue')['default']
  }
}
