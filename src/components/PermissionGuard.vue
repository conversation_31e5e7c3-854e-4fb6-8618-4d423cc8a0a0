<template>
    <view v-if="hasAccess">
        <slot />
    </view>
    <view v-else-if="$slots.fallback">
        <slot name="fallback" />
    </view>
</template>

<script setup lang="ts">
interface Props {
    /** 需要的权限码 */
    permission?: string | string[]
    /** 需要的角色 */
    role?: string | string[]
    /** 权限检查模式：'any' 任意一个, 'all' 全部拥有 */
    mode?: 'any' | 'all'
    /** 是否反向检查（没有权限时显示） */
    inverse?: boolean
    /** 部门权限检查 */
    departmentScope?: 'all' | 'department' | 'self'
    /** 目标用户ID（用于用户数据权限检查） */
    targetUserId?: number
}

withDefaults(defineProps<Props>(), {
    mode: 'any',
    inverse: false,
})

interface Emits {
    /** 权限检查结果变化时触发 */
    (e: 'access-change', hasAccess: boolean): void
}

const emit = defineEmits<Emits>()

const props = defineProps<Props>()
const { 
    hasPermission, 
    hasRole, 
    hasAllPermissions, 
    canAccessUserData,
    getDepartmentScope 
} = usePermission()

/**
 * 计算是否有访问权限
 */
const hasAccess = computed(() => {
    let result = true
    
    // 权限检查
    if (props.permission) {
        if (props.mode === 'all' && Array.isArray(props.permission)) {
            result = result && hasAllPermissions(props.permission)
        } else {
            result = result && hasPermission(props.permission)
        }
    }
    
    // 角色检查
    if (props.role) {
        result = result && hasRole(props.role)
    }
    
    // 部门权限检查
    if (props.departmentScope) {
        result = result && (getDepartmentScope() === props.departmentScope || getDepartmentScope() === 'all')
    }
    
    // 用户数据权限检查
    if (props.targetUserId !== undefined) {
        result = result && canAccessUserData(props.targetUserId)
    }
    
    // 反向检查
    return props.inverse ? !result : result
})

// 监听权限变化
watch(hasAccess, (newValue) => {
    emit('access-change', newValue)
}, { immediate: true })
</script>

<script lang="ts">
export default {
    name: 'PermissionGuard',
}
</script>"