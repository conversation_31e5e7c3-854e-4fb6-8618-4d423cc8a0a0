{
    // Place your snippets for html here. Each snippet is defined under a snippet name and has a prefix, body and
    // description. The prefix is what is used to trigger the snippet and the body will be expanded and inserted. Possible variables are:
    // $1, $2 for tab stops, $0 for the final cursor position, and ${1:label}, ${2:another} for placeholders. Placeholders with the
    // same ids are connected.
    // Example:

    "TnButton-按钮": {
        "scope": "html,vue-html",
        "prefix": "tnbutton",
        "body": ["<TnButton type=\"${1|primary,success,warning,danger,info|}\">$2</TnButton>"],
        "description": "更多信息查看 <https://vue3.tuniaokj.com/zh-CN/component/button.html>"
    },
    "TnTag-标签": {
        "scope": "html,vue-html",
        "prefix": "tntag",
        "body": ["<TnTag type=\"${1|primary,success,warning,danger,info|}\">$2</TnTag>"],
        "description": "更多信息查看 <https://vue3.tuniaokj.com/zh-CN/component/tag.html>"
    },
    "TnBadge-徽标": {
        "scope": "html,vue-html",
        "prefix": "tnbadge",
        "body": ["<TnBadge value=\"$2\" type=\"${1|primary,success,warning,danger,info|}\"></TnBadge>"],
        "description": "更多信息查看 <https://vue3.tuniaokj.com/zh-CN/component/badge.html>"
    },
    "TnLoading-加载图标": {
        "scope": "html,vue-html",
        "prefix": "tnloading",
        "body": ["<TnLoading show type=\"${1|primary,success,warning,danger,info|}\" mode=\"${2|circle,flower,semicircle|}\"></TnLoading>"],
        "description": "更多信息查看 <https://vue3.tuniaokj.com/zh-CN/component/loading.html>"
    },
}
