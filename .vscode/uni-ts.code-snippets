{"uni-app APP 平台": {"scope": "javascript,typescript", "prefix": ["platform-app", "app", "platform-app-plus", "app-plus"], "body": ["APP-PLUS"], "description": "uni-app APP 对应的 %PLATFORM 值。更多信息查看 <https://uniapp.dcloud.io/tutorial/platform.html>。"}, "uni-app APP-NVUE 平台": {"scope": "javascript,typescript", "prefix": ["platform-app-nvue", "app-nvue", "platform-app-plus-nvue", "app-plus-nvue"], "body": ["APP-NVUE"], "description": "uni-app APP-NVUE 对应的 %PLATFORM 值。更多信息查看 <https://uniapp.dcloud.io/tutorial/platform.html>。"}, "uni-app H5 平台": {"scope": "javascript,typescript", "prefix": ["platform-h5", "h5"], "body": ["H5"], "description": "uni-app H5 对应的 %PLATFORM 值。更多信息查看 <https://uniapp.dcloud.io/tutorial/platform.html>。"}, "uni-app 小程序平台": {"scope": "javascript,typescript", "prefix": ["platform-mp", "mp", "platform-miniprogram", "miniprogram"], "body": ["MP"], "description": "uni-app 小程序对应的 %PLATFORM 值。更多信息查看 <https://uniapp.dcloud.io/tutorial/platform.html>。"}, "uni-app 微信小程序平台": {"scope": "javascript,typescript", "prefix": ["platform-mp-weixin", "mp-weixin", "platform-weixin", "weixin", "platform-mp-wechat", "mp-wechat", "platform-wechat", "wechat"], "body": ["MP-WEIXIN"], "description": "uni-app 微信小程序对应的 %PLATFORM 值。更多信息查看 <https://uniapp.dcloud.io/tutorial/platform.html>。"}, "uni-app 支付宝小程序平台": {"scope": "javascript,typescript", "prefix": ["platform-mp-alipay", "mp-alipay", "platform-alipay", "alipay", "platform-mp-ali", "mp-ali", "platform-ali", "ali", "platform-mp-my", "mp-my", "platform-my", "my"], "body": ["MP-ALIPAY"], "description": "uni-app 支付宝小程序对应的 %PLATFORM 值。更多信息查看 <https://uniapp.dcloud.io/tutorial/platform.html>。"}, "uni-app 百度小程序平台": {"scope": "javascript,typescript", "prefix": ["platform-mp-baidu", "mp-baidu", "platform-baidu", "baidu", "platform-mp-swan", "mp-swan", "platform-swan", "swan"], "body": ["MP-BAIDU"], "description": "uni-app 百度小程序对应的 %PLATFORM 值。更多信息查看 <https://uniapp.dcloud.io/tutorial/platform.html>。"}, "uni-app 字节跳动小程序平台": {"scope": "javascript,typescript", "prefix": ["platform-mp-to<PERSON><PERSON>", "mp-<PERSON><PERSON><PERSON>", "platform-toutiao", "<PERSON><PERSON><PERSON>", "platform-mp-bytedance", "mp-bytedance", "platform-bytedance", "bytedance"], "body": ["MP-TOUTIAO"], "description": "uni-app 字节跳动小程序对应的 %PLATFORM 值。更多信息查看 <https://uniapp.dcloud.io/tutorial/platform.html>。"}, "uni-app 飞书小程序平台": {"scope": "javascript,typescript", "prefix": ["platform-mp-lark", "mp-lark", "platform-lark", "lark"], "body": ["MP-LARK"], "description": "uni-app 飞书小程序对应的 %PLATFORM 值。更多信息查看 <https://uniapp.dcloud.io/tutorial/platform.html>。"}, "uni-app QQ 小程序平台": {"scope": "javascript,typescript", "prefix": ["platform-mp-qq", "mp-qq", "platform-qq", "qq"], "body": ["MP-Q<PERSON>"], "description": "uni-app QQ 小程序对应的 %PLATFORM 值。更多信息查看 <https://uniapp.dcloud.io/tutorial/platform.html>。"}, "uni-app 快手小程序平台": {"scope": "javascript,typescript", "prefix": ["platform-mp-kua<PERSON>ou", "mp-k<PERSON><PERSON><PERSON>", "platform-kua<PERSON>ou", "<PERSON><PERSON><PERSON><PERSON>"], "body": ["MP-KUAISHOU"], "description": "uni-app 快手小程序对应的 %PLATFORM 值。更多信息查看 <https://uniapp.dcloud.io/tutorial/platform.html>。"}, "uni-app 京东小程序平台": {"scope": "javascript,typescript", "prefix": ["platform-mp-jd", "mp-jd", "platform-jd", "jd"], "body": ["MP-<PERSON><PERSON>"], "description": "uni-app 京东小程序对应的 %PLATFORM 值。更多信息查看 <https://uniapp.dcloud.io/tutorial/platform.html>。"}, "uni-app 360 小程序平台": {"scope": "javascript,typescript", "prefix": ["platform-mp-360", "mp-360", "platform-360", "360"], "body": ["MP-360"], "description": "uni-app 360 小程序对应的 %PLATFORM 值。更多信息查看 <https://uniapp.dcloud.io/tutorial/platform.html>。"}, "uni-app 小红书小程序平台": {"scope": "javascript,typescript", "prefix": ["platform-mp-xhs", "mp-xhs", "platform-xhs", "xhs"], "body": ["MP-XHS"], "description": "uni-app 小红书小程序对应的 %PLATFORM 值。更多信息查看 <https://uniapp.dcloud.io/tutorial/platform.html>。"}, "uni-app 快应用通用平台": {"scope": "javascript,typescript", "prefix": ["platform-quickapp", "quickapp", "platform-quickapp-webview", "quickapp-webview"], "body": ["QUICKAPP-WEBVIEW"], "description": "uni-app 快应用通用对应的 %PLATFORM 值。更多信息查看 <https://uniapp.dcloud.io/tutorial/platform.html>。"}, "uni-app 快应用联盟平台": {"scope": "javascript,typescript", "prefix": ["platform-quickapp-union", "quickapp-union", "platform-quickapp-webview-union", "quickapp-webview-union"], "body": ["QUICKAPP-WEBVIEW-UNION"], "description": "uni-app 快应用联盟对应的 %PLATFORM 值。更多信息查看 <https://uniapp.dcloud.io/tutorial/platform.html>。"}, "uni-app 快应用华为平台": {"scope": "javascript,typescript", "prefix": ["platform-quickapp-huawei", "quickapp-huawei", "platform-quickapp-webview-huawei", "quickapp-webview-huawei"], "body": ["QUICKAPP-WEBVIEW-HUAWEI"], "description": "uni-app 快应用华为对应的 %PLATFORM 值。更多信息查看 <https://uniapp.dcloud.io/tutorial/platform.html>。"}, "uni-app 条件编译，处理某平台": {"scope": "javascript,typescript", "prefix": ["#ifdef", "ifdef"], "body": ["// #ifdef ${1|APP-PLUS,APP-NVUE,H5,MP,MP-WEIXIN,MP-<PERSON>IPA<PERSON>,MP-<PERSON>IDU,MP-TOUTIAO,MP-LARK,MP-QQ,MP-KUAISHOU,MP-360,QUICKAPP-WEBVIEW,QUICKAPP-WEBVIEW-UNION,QUICKAPP-WEBVIEW-HUAWEI|}", "$2", "// #endif"], "description": "uni-app 条件编译，处理某平台。更多信息查看 <https://uniapp.dcloud.io/tutorial/platform.html>。"}, "uni-app 条件编译，排除某平台": {"scope": "javascript,typescript", "prefix": ["#ifndef", "ifndef"], "body": ["// #ifndef ${1|APP-PLUS,APP-NVUE,H5,MP,MP-WEIXIN,MP-<PERSON>IPA<PERSON>,MP-<PERSON>IDU,MP-TOUTIAO,MP-LARK,MP-QQ,MP-KUAISHOU,MP-360,QUICKAPP-WEBVIEW,QUICKAPP-WEBVIEW-UNION,QUICKAPP-WEBVIEW-HUAWEI|}", "$2", "// #endif"], "description": "uni-app 条件编译，排除某平台。更多信息查看 <https://uniapp.dcloud.io/tutorial/platform.html>。"}, "uni-app 结束条件编译": {"scope": "javascript,typescript", "prefix": ["#endif", "endif"], "body": ["// #endif"], "description": "uni-app 结束条件编译。更多信息查看 <https://uniapp.dcloud.io/tutorial/platform.html>。"}, "uni-app 判断是否为开发环境": {"scope": "javascript,typescript", "prefix": ["process.env.NODE_ENV === 'development'"], "body": ["process.env.NODE_ENV === 'development'"], "description": "uni-app 判断是否为开发环境。更多信息查看 <https://cli.vuejs.org/zh/guide/mode-and-env.html>。"}, "uni-app 判断是否不为开发环境": {"scope": "javascript,typescript", "prefix": ["process.env.NODE_ENV !== 'development'"], "body": ["process.env.NODE_ENV !== 'development'"], "description": "uni-app 判断是否不为开发环境。更多信息查看 <https://cli.vuejs.org/zh/guide/mode-and-env.html>。"}, "uni-app 判断是否为生产环境": {"scope": "javascript,typescript", "prefix": ["process.env.NODE_ENV === 'production'"], "body": ["process.env.NODE_ENV === 'production'"], "description": "uni-app 判断是否为生产环境。更多信息查看 <https://cli.vuejs.org/zh/guide/mode-and-env.html>。"}, "uni-app 判断是否不为生产环境": {"scope": "javascript,typescript", "prefix": ["process.env.NODE_ENV !== 'production'"], "body": ["process.env.NODE_ENV !== 'production'"], "description": "uni-app 判断是否不为生产环境。更多信息查看 <https://cli.vuejs.org/zh/guide/mode-and-env.html>。"}, "uni-app base64 转 ArrayBuffer": {"scope": "javascript,typescript", "prefix": ["uni.base64ToArrayBuffer"], "body": ["uni.base64ToArrayBuffer($1)$0"], "description": "uni-app 将 base64 字符串转成 ArrayBuffer。更多信息查看 <https://uniapp.dcloud.io/api/base64ToArrayBuffer>。"}, "uni-app ArrayBuffer 转 base64": {"scope": "javascript,typescript", "prefix": ["uni.arrayBufferToBase64"], "body": ["uni.arrayBufferToBase64($1)$0"], "description": "uni-app 将 ArrayBuffer 字符串转成 base64。更多信息查看 <https://uniapp.dcloud.io/api/arrayBufferToBase64>。"}, "uni-app 应用生命周期 onLaunch": {"scope": "javascript,typescript", "prefix": ["onLaunch"], "body": ["onLaunch() {$1}$0"], "description": "uni-app 应用生命周期，只能在 App.vue 中监听。uni-app 初始化完成时触发，全局只触发一次。更多信息查看 <https://uniapp.dcloud.io/collocation/frame/lifecycle?id=%e5%ba%94%e7%94%a8%e7%94%9f%e5%91%bd%e5%91%a8%e6%9c%9f>。"}, "uni-app 应用生命周期 onShow": {"scope": "javascript,typescript", "prefix": ["onShow"], "body": ["onShow() {$1}$0"], "description": "uni-app 应用生命周期，只能在 App.vue 中监听。uni-app 启动时或从后台进入前台时触发。更多信息查看 <https://uniapp.dcloud.io/collocation/frame/lifecycle?id=%e5%ba%94%e7%94%a8%e7%94%9f%e5%91%bd%e5%91%a8%e6%9c%9f>。"}, "uni-app 应用生命周期 onHide": {"scope": "javascript,typescript", "prefix": ["onHide"], "body": ["onHide() {$1}$0"], "description": "uni-app 应用生命周期，只能在 App.vue 中监听。uni-app 从前台进入后台时触发。更多信息查看 <https://uniapp.dcloud.io/collocation/frame/lifecycle?id=%e5%ba%94%e7%94%a8%e7%94%9f%e5%91%bd%e5%91%a8%e6%9c%9f>。"}, "uni-app 应用生命周期 onError": {"scope": "javascript,typescript", "prefix": ["onError"], "body": ["onError() {$1}$0"], "description": "uni-app 应用生命周期，只能在 App.vue 中监听。uni-app 报错时触发。更多信息查看 <https://uniapp.dcloud.io/collocation/frame/lifecycle?id=%e5%ba%94%e7%94%a8%e7%94%9f%e5%91%bd%e5%91%a8%e6%9c%9f>。"}, "uni-app 应用生命周期 onUniNViewMessage": {"scope": "javascript,typescript", "prefix": ["onUniNViewMessage"], "body": ["onUniNViewMessage() {$1}$0"], "description": "uni-app 应用生命周期，只能在 App.vue 中监听。监听 nvue 页面发送的数据。更多信息查看 <https://uniapp.dcloud.io/collocation/frame/lifecycle?id=%e5%ba%94%e7%94%a8%e7%94%9f%e5%91%bd%e5%91%a8%e6%9c%9f>。"}, "uni-app 应用生命周期 onUnhandledRejection": {"scope": "javascript,typescript", "prefix": ["onUnhandledRejection"], "body": ["onUnhandledRejection() {$1}$0"], "description": "uni-app 应用生命周期，只能在 App.vue 中监听。监听未处理的 Promise 拒绝事件。更多信息查看 <https://uniapp.dcloud.io/collocation/frame/lifecycle?id=%e5%ba%94%e7%94%a8%e7%94%9f%e5%91%bd%e5%91%a8%e6%9c%9f>。"}, "uni-app 应用生命周期 onPageNotFound": {"scope": "javascript,typescript", "prefix": ["onPageNotFound"], "body": ["onPageNotFound() {$1}$0"], "description": "uni-app 应用生命周期，只能在 App.vue 中监听。uni-app 页面不存在时触发。更多信息查看 <https://uniapp.dcloud.io/collocation/frame/lifecycle?id=%e5%ba%94%e7%94%a8%e7%94%9f%e5%91%bd%e5%91%a8%e6%9c%9f>。"}, "uni-app 应用生命周期 onThemeChange": {"scope": "javascript,typescript", "prefix": ["onThemeChange"], "body": ["onThemeChange() {$1}$0"], "description": "uni-app 应用生命周期，只能在 App.vue 中监听。系统主题变化时触发。更多信息查看 <https://uniapp.dcloud.io/collocation/frame/lifecycle?id=%e5%ba%94%e7%94%a8%e7%94%9f%e5%91%bd%e5%91%a8%e6%9c%9f>。"}, "uni-app 页面生命周期 onInit": {"scope": "javascript,typescript", "prefix": ["onInit"], "body": ["onInit(options) {$1}$0"], "description": "uni-app 页面生命周期。页面加载时触发，参数是上个页面传递的数据，类型是 object。触发时机早于 onLoad。更多信息查看 <https://uniapp.dcloud.io/collocation/frame/lifecycle?id=%e9%a1%b5%e9%9d%a2%e7%94%9f%e5%91%bd%e5%91%a8%e6%9c%9f>。"}, "uni-app 页面生命周期 onLoad": {"scope": "javascript,typescript", "prefix": ["onLoad"], "body": ["onLoad(options) {$1}$0"], "description": "uni-app 页面生命周期。页面加载时触发，参数是上个页面传递的数据，类型是 object。更多信息查看 <https://uniapp.dcloud.io/collocation/frame/lifecycle?id=%e9%a1%b5%e9%9d%a2%e7%94%9f%e5%91%bd%e5%91%a8%e6%9c%9f>。"}, "uni-app 页面生命周期 onShow": {"scope": "javascript,typescript", "prefix": ["onShow"], "body": ["onShow() {$1}$0"], "description": "uni-app 页面生命周期。页面在屏幕上显示时触发。更多信息查看 <https://uniapp.dcloud.io/collocation/frame/lifecycle?id=%e9%a1%b5%e9%9d%a2%e7%94%9f%e5%91%bd%e5%91%a8%e6%9c%9f>。"}, "uni-app 页面生命周期 onReady": {"scope": "javascript,typescript", "prefix": ["onReady"], "body": ["onReady() {$1}$0"], "description": "uni-app 页面生命周期。页面初次渲染完成时触发，如果渲染速度快，会在页面进入动画完成前触发。更多信息查看 <https://uniapp.dcloud.io/collocation/frame/lifecycle?id=%e9%a1%b5%e9%9d%a2%e7%94%9f%e5%91%bd%e5%91%a8%e6%9c%9f>。"}, "uni-app 页面生命周期 onHide": {"scope": "javascript,typescript", "prefix": ["onHide"], "body": ["onHide() {$1}$0"], "description": "uni-app 页面生命周期。页面在屏幕上隐藏时触发。更多信息查看 <https://uniapp.dcloud.io/collocation/frame/lifecycle?id=%e9%a1%b5%e9%9d%a2%e7%94%9f%e5%91%bd%e5%91%a8%e6%9c%9f>。"}, "uni-app 页面生命周期 onUnload": {"scope": "javascript,typescript", "prefix": ["onUnload"], "body": ["onUnload() {$1}$0"], "description": "uni-app 页面生命周期。页面卸载时触发。更多信息查看 <https://uniapp.dcloud.io/collocation/frame/lifecycle?id=%e9%a1%b5%e9%9d%a2%e7%94%9f%e5%91%bd%e5%91%a8%e6%9c%9f>。"}, "uni-app 页面生命周期 onResize": {"scope": "javascript,typescript", "prefix": ["onResize"], "body": ["onResize() {$1}$0"], "description": "uni-app 页面生命周期。窗口尺寸变化时触发。更多信息查看 <https://uniapp.dcloud.io/collocation/frame/lifecycle?id=%e9%a1%b5%e9%9d%a2%e7%94%9f%e5%91%bd%e5%91%a8%e6%9c%9f>。"}, "uni-app 页面生命周期 onPullDownRefresh": {"scope": "javascript,typescript", "prefix": ["onPullDownRefresh"], "body": ["onPullDownRefresh() {$1}$0"], "description": "uni-app 页面生命周期。用户下拉时触发，一般用于下拉刷新。更多信息查看 <https://uniapp.dcloud.io/collocation/frame/lifecycle?id=%e9%a1%b5%e9%9d%a2%e7%94%9f%e5%91%bd%e5%91%a8%e6%9c%9f>。"}, "uni-app 页面生命周期 onReachBottom": {"scope": "javascript,typescript", "prefix": ["onReachBottom"], "body": ["onReachBottom() {$1}$0"], "description": "uni-app 页面生命周期。页面滚动到底部时触发，一般用于触底加载数据。更多信息查看 <https://uniapp.dcloud.io/collocation/frame/lifecycle?id=%e9%a1%b5%e9%9d%a2%e7%94%9f%e5%91%bd%e5%91%a8%e6%9c%9f>。"}, "uni-app 页面生命周期 onTabItemTap": {"scope": "javascript,typescript", "prefix": ["onTabItemTap"], "body": ["onTabItemTap({ index, pagePath, text }) {$1}$0"], "description": "uni-app 页面生命周期。点击 Tab 时触发。更多信息查看 <https://uniapp.dcloud.io/collocation/frame/lifecycle?id=%e9%a1%b5%e9%9d%a2%e7%94%9f%e5%91%bd%e5%91%a8%e6%9c%9f>。"}, "uni-app 页面生命周期 onShareAppMessage": {"scope": "javascript,typescript", "prefix": ["onShareAppMessage"], "body": ["onShareAppMessage() {$1}$0"], "description": "uni-app 页面生命周期。点击右上角分享时触发。更多信息查看 <https://uniapp.dcloud.io/collocation/frame/lifecycle?id=%e9%a1%b5%e9%9d%a2%e7%94%9f%e5%91%bd%e5%91%a8%e6%9c%9f>。"}, "uni-app 页面生命周期 onPageScroll": {"scope": "javascript,typescript", "prefix": ["onPageScroll"], "body": ["onPageScroll({ scrollTop }) {$1}$0"], "description": "uni-app 页面生命周期。页面滚动时触发。更多信息查看 <https://uniapp.dcloud.io/collocation/frame/lifecycle?id=%e9%a1%b5%e9%9d%a2%e7%94%9f%e5%91%bd%e5%91%a8%e6%9c%9f>。"}, "uni-app 页面生命周期 onNavigationBarButtonTap": {"scope": "javascript,typescript", "prefix": ["onNavigationBarButtonTap"], "body": ["onNavigationBarButtonTap({ index }) {$1}$0"], "description": "uni-app 页面生命周期。原生标题栏按钮被点击时触发。更多信息查看 <https://uniapp.dcloud.io/collocation/frame/lifecycle?id=%e9%a1%b5%e9%9d%a2%e7%94%9f%e5%91%bd%e5%91%a8%e6%9c%9f>。"}, "uni-app 页面生命周期 onBackPress": {"scope": "javascript,typescript", "prefix": ["onBackPress"], "body": ["onBackPress({ from }) {$1}$0"], "description": "uni-app 页面生命周期。页面返回时触发。更多信息查看 <https://uniapp.dcloud.io/collocation/frame/lifecycle?id=%e9%a1%b5%e9%9d%a2%e7%94%9f%e5%91%bd%e5%91%a8%e6%9c%9f>。"}, "uni-app 页面生命周期 onNavigationBarSearchInputChanged": {"scope": "javascript,typescript", "prefix": ["onNavigationBarSearchInputChanged"], "body": ["onNavigationBarSearchInputChanged() {$1}$0"], "description": "uni-app 页面生命周期。原生标题栏搜索输入框输入时触发。更多信息查看 <https://uniapp.dcloud.io/collocation/frame/lifecycle?id=%e9%a1%b5%e9%9d%a2%e7%94%9f%e5%91%bd%e5%91%a8%e6%9c%9f>。"}, "uni-app 页面生命周期 onNavigationBarSearchInputConfirmed": {"scope": "javascript,typescript", "prefix": ["onNavigationBarSearchInputConfirmed"], "body": ["onNavigationBarSearchInputConfirmed() {$1}$0"], "description": "uni-app 页面生命周期。原生标题栏搜索输入框确认搜索时触发。更多信息查看 <https://uniapp.dcloud.io/collocation/frame/lifecycle?id=%e9%a1%b5%e9%9d%a2%e7%94%9f%e5%91%bd%e5%91%a8%e6%9c%9f>。"}, "uni-app 页面生命周期 onNavigationBarSearchInputClicked": {"scope": "javascript,typescript", "prefix": ["onNavigationBarSearchInputClicked"], "body": ["onNavigationBarSearchInputClicked() {$1}$0"], "description": "uni-app 页面生命周期。原生标题栏点击搜索输入框时触发。更多信息查看 <https://uniapp.dcloud.io/collocation/frame/lifecycle?id=%e9%a1%b5%e9%9d%a2%e7%94%9f%e5%91%bd%e5%91%a8%e6%9c%9f>。"}, "uni-app 页面生命周期 onShareTimeline": {"scope": "javascript,typescript", "prefix": ["onShareTimeline"], "body": ["onShareTimeline() {$1}$0"], "description": "uni-app 页面生命周期。点击右上角转发到朋友圈时触发。更多信息查看 <https://uniapp.dcloud.io/collocation/frame/lifecycle?id=%e9%a1%b5%e9%9d%a2%e7%94%9f%e5%91%bd%e5%91%a8%e6%9c%9f>。"}, "uni-app 页面生命周期 onAddToFavorites": {"scope": "javascript,typescript", "prefix": ["onAddToFavorites"], "body": ["onAddToFavorites() {$1}$0"], "description": "uni-app 页面生命周期。点击右上角收藏时触发。更多信息查看 <https://uniapp.dcloud.io/collocation/frame/lifecycle?id=%e9%a1%b5%e9%9d%a2%e7%94%9f%e5%91%bd%e5%91%a8%e6%9c%9f>。"}, "uni-app 应用级事件 onAppShow": {"scope": "javascript,typescript", "prefix": ["uni.onAppShow"], "body": ["uni.onAppShow($1)$0"], "description": "uni-app 应用级事件。监听启动或从后台进入前台。更多信息查看 <https://uniapp.dcloud.io/api/application?id=onappshow>。"}, "uni-app 应用级事件 offAppShow": {"scope": "javascript,typescript", "prefix": ["uni.offAppShow"], "body": ["uni.offAppShow($1)$0"], "description": "uni-app 应用级事件。取消监听启动或从后台进入前台。更多信息查看 <https://uniapp.dcloud.io/api/application?id=offappshow>。"}, "uni-app 应用级事件 onAppHide": {"scope": "javascript,typescript", "prefix": ["uni.onAppHide"], "body": ["uni.onAppHide($1)$0"], "description": "uni-app 应用级事件。监听从前台进入后台。更多信息查看 <https://uniapp.dcloud.io/api/application?id=onapphide>。"}, "uni-app 应用级事件 offAppHide": {"scope": "javascript,typescript", "prefix": ["uni.offAppHide"], "body": ["uni.offAppHide($1)$0"], "description": "uni-app 应用级事件。取消监听从前台进入后台。更多信息查看 <https://uniapp.dcloud.io/api/application?id=offapphide>。"}, "uni-app 应用级事件 onPageNotFound": {"scope": "javascript,typescript", "prefix": ["uni.onPageNotFound"], "body": ["uni.onPageNotFound($1)$0"], "description": "uni-app 应用级事件。监听页面不存在。更多信息查看 <https://uniapp.dcloud.io/api/application?id=onpagenotfound>。"}, "uni-app 应用级事件 offPageNotFound": {"scope": "javascript,typescript", "prefix": ["uni.offPageNotFound"], "body": ["uni.offPageNotFound($1)$0"], "description": "uni-app 应用级事件。取消监听页面不存在。更多信息查看 <https://uniapp.dcloud.io/api/application?id=offpagenotfound>。"}, "uni-app 应用级事件 onError": {"scope": "javascript,typescript", "prefix": ["uni.onError"], "body": ["uni.onError($1)$0"], "description": "uni-app 应用级事件。监听错误。更多信息查看 <https://uniapp.dcloud.io/api/application?id=onerror>。"}, "uni-app 应用级事件 offError": {"scope": "javascript,typescript", "prefix": ["uni.offError"], "body": ["uni.offError($1)$0"], "description": "uni-app 应用级事件。取消监听错误。更多信息查看 <https://uniapp.dcloud.io/api/application?id=offerror>。"}, "uni-app 添加拦截器": {"scope": "javascript,typescript", "prefix": ["uni.addInterceptor"], "body": ["uni.addInterceptor($1)$0"], "description": "uni-app 添加拦截器。更多信息查看 <https://uniapp.dcloud.io/api/interceptor?id=addinterceptor>。"}, "uni-app 移除拦截器": {"scope": "javascript,typescript", "prefix": ["uni.removeInterceptor"], "body": ["uni.removeInterceptor($1)$0"], "description": "uni-app 移除拦截器。更多信息查看 <https://uniapp.dcloud.io/api/interceptor?id=removeinterceptor>。"}, "uni-app 获取当前应用实例": {"scope": "javascript,typescript", "prefix": ["uni.getApp", "getApp"], "body": ["getApp()$0"], "description": "uni-app 获取当前应用实例，可以进一步获取 globalData。请考虑使用 vuex 实现同样的效果。更多信息查看 <https://uniapp.dcloud.io/collocation/frame/window?id=getapp>。"}, "uni-app 获取当前页面栈实例": {"scope": "javascript,typescript", "prefix": ["uni.getCurrentPages", "getCurrentPages"], "body": ["getCurrentPages()$0"], "description": "uni-app 获取当前页面栈实例，数组形式，第一个元素是首页。更多信息查看 <https://uniapp.dcloud.io/collocation/frame/window?id=getcurrentpages>。"}, "uni-app 触发全局自定义事件": {"scope": "javascript,typescript", "prefix": ["uni.$emit"], "body": ["uni.$emit(${1:eventName}, ${2:{}})$0"], "description": "uni-app 触发全局自定义事件。过多的全局自定义事件容易导致代码混乱且难以调试，请控制全局自定义事件数量。更多信息查看 <https://uniapp.dcloud.io/collocation/frame/communication?id=emit>。"}, "uni-app 监听全局自定义事件": {"scope": "javascript,typescript", "prefix": ["uni.$on"], "body": ["uni.$on(${1:eventName}, (${2:data}) => {$3})$0"], "description": "uni-app 监听全局自定义事件。过多的全局自定义事件容易导致代码混乱且难以调试，请控制全局自定义事件数量。更多信息查看 <https://uniapp.dcloud.io/collocation/frame/communication?id=on>。"}, "uni-app 一次性监听全局自定义事件": {"scope": "javascript,typescript", "prefix": ["uni.$once"], "body": ["uni.$once(${1:eventName}, (${2:data}) => {$3})$0"], "description": "uni-app 一次性监听全局自定义事件。过多的全局自定义事件容易导致代码混乱且难以调试，请控制全局自定义事件数量。更多信息查看 <https://uniapp.dcloud.io/collocation/frame/communication?id=once>。"}, "uni-app 移除全局自定义事件监听": {"scope": "javascript,typescript", "prefix": ["uni.$off"], "body": ["uni.$off(${1:eventName}$0)"], "description": "uni-app 移除全局自定义事件监听。过多的全局自定义事件容易导致代码混乱且难以调试，请控制全局自定义事件数量。更多信息查看 <https://uniapp.dcloud.io/collocation/frame/communication?id=off>。"}, "uni-app 添加全局拦截器": {"scope": "javascript,typescript", "prefix": ["uni.addInterceptor"], "body": ["uni.addInterceptor($1, $2)$0"], "description": "uni-app 添加全局拦截器。过多的全局拦截器容易导致代码混乱且难以调试，请控制全局拦截器数量。更多信息查看 <https://uniapp.dcloud.io/api/interceptor>。"}, "uni-app 移除全局拦截器": {"scope": "javascript,typescript", "prefix": ["uni.removeInterceptor"], "body": ["uni.removeInterceptor($1, $2)$0"], "description": "uni-app 移除全局拦截器。过多的全局拦截器容易导致代码混乱且难以调试，请控制全局拦截器数量。更多信息查看 <https://uniapp.dcloud.io/api/interceptor>。"}, "uni-app 发起网络请求": {"scope": "javascript,typescript", "prefix": ["uni.request"], "body": ["uni.request({", "\turl: '$1',", "\tdata: {$2},", "\theader: {", "\t\tAccept: 'application/json',", "\t\t'Content-Type': 'application/json',", "\t\t'X-Requested-With': 'XMLHttpRequest'", "\t},", "\tmethod: '${3|GET,POST|}',", "\tsslVerify: ${4|true,false|},", "\tsuccess: ({ data, statusCode, header }) => {$5},", "\tfail: (error) => {$6}", "})$0"], "description": "uni-app 发起网络请求。请考虑使用已经封装好的库，如 luch-request。更多信息查看 <https://uniapp.dcloud.io/api/request/request>。"}, "uni-app 上传文件": {"scope": "javascript,typescript", "prefix": ["uni.uploadFile"], "body": ["uni.uploadFile({", "\turl: '$1',", "\tfileType: '${2|image,video,audio|}',", "\tfilePath: ${3:tempFilePaths[0]},", "\tname: '${4:file}',", "\tsuccess: ({ data, statusCode }) => {$5},", "\tfail: (error) => {$6}", "})$0"], "description": "uni-app 上传文件。请考虑使用已经封装好的库，如 luch-request。更多信息查看 <https://uniapp.dcloud.io/api/request/network-file?id=uploadfile>。"}, "uni-app 下载文件": {"scope": "javascript,typescript", "prefix": ["uni.downloadFile"], "body": ["uni.downloadFile({", "\turl: '$1',", "\tsuccess: ({ tempFilePath, statusCode }) => {$2},", "\tfail: (error) => {$3}", "})$0"], "description": "uni-app 下载文件。请考虑使用已经封装好的库，如 luch-request。更多信息查看 <https://uniapp.dcloud.io/api/request/network-file?id=downloadfile>。"}, "uni-app 创建 WebSocket 连接": {"scope": "javascript,typescript", "prefix": ["uni.connectSocket"], "body": ["uni.connectSocket({", "\turl: '$1',", "\tsuccess: (result) => {$2},", "\tfail: (error) => {$3}", "})$0"], "description": "uni-app 创建 WebSocket 连接。更多信息查看 <https://uniapp.dcloud.io/api/request/websocket?id=connectsocket>。"}, "uni-app 监听 WebSocket 连接打开": {"scope": "javascript,typescript", "prefix": ["uni.onSocketOpen"], "body": ["uni.onSocketOpen((result) => {$1})$0"], "description": "uni-app 监听 WebSocket 连接打开。更多信息查看 <https://uniapp.dcloud.io/api/request/websocket?id=onsocketopen>。"}, "uni-app 监听 WebSocket 错误": {"scope": "javascript,typescript", "prefix": ["uni.onSocketError"], "body": ["uni.onSocketError((result) => {$1})$0"], "description": "uni-app 监听 WebSocket 错误。更多信息查看 <https://uniapp.dcloud.io/api/request/websocket?id=onsocketerror>。"}, "uni-app 通过 WebSocket 发送数据": {"scope": "javascript,typescript", "prefix": ["uni.sendSocketMessage"], "body": ["uni.sendSocketMessage({", "\tdata: '$1',", "\tsuccess: (result) => {$2},", "\tfail: (error) => {$3}", "})$0"], "description": "uni-app 通过 WebSocket 发送数据。更多信息查看 <https://uniapp.dcloud.io/api/request/websocket?id=sendsocketmessage>。"}, "uni-app 监听 WebSocket 接收消息": {"scope": "javascript,typescript", "prefix": ["uni.onSocketMessage"], "body": ["uni.onSocketMessage(({ data }) => {$1})$0"], "description": "uni-app 监听 WebSocket 接收消息。更多信息查看 <https://uniapp.dcloud.io/api/request/websocket?id=onsocketmessage>。"}, "uni-app 关闭 WebSocket 连接": {"scope": "javascript,typescript", "prefix": ["uni.closeSocket"], "body": ["uni.closeSocket({", "\tsuccess: (result) => {$1},", "\tfail: (error) => {$2}", "})$0"], "description": "uni-app 关闭 WebSocket 连接。更多信息查看 <https://uniapp.dcloud.io/api/request/websocket?id=closesocket>。"}, "uni-app 监听 WebSocket 关闭": {"scope": "javascript,typescript", "prefix": ["uni.onSocketClose"], "body": ["uni.onSocketClose((result) => {$1})$0"], "description": "uni-app 监听 WebSocket 关闭。更多信息查看 <https://uniapp.dcloud.io/api/request/websocket?id=onsocketclose>。"}, "uni-app 保留当前页面，跳转到某个非 TabBar 页面": {"scope": "javascript,typescript", "prefix": ["uni.navigateTo"], "body": ["uni.navigateTo({ url: '/pages/$1' })$0"], "description": "uni-app 保留当前页面，跳转到某个非 TabBar 页面。更多信息查看 <https://uniapp.dcloud.io/api/router?id=navigateto>。"}, "uni-app 关闭当前页面，跳转到某个非 Tab 页面": {"scope": "javascript,typescript", "prefix": ["uni.redirectTo"], "body": ["uni.redirectTo({ url: '/pages/$1' })$0"], "description": "uni-app 关闭当前页面，跳转到某个非 Tab 页面。更多信息查看 <https://uniapp.dcloud.io/api/router?id=redirectto>。"}, "uni-app 关闭所有页面，打开到某个页面": {"scope": "javascript,typescript", "prefix": ["uni.reLaunch"], "body": ["uni.reLaunch({ url: '/pages/$1' })$0"], "description": "uni-app 关闭所有页面，打开到某个页面。更多信息查看 <https://uniapp.dcloud.io/api/router?id=relaunch>。"}, "uni-app 关闭所有非 TabBar 页面，跳转到 TabBar 页面": {"scope": "javascript,typescript", "prefix": ["uni.switchTab"], "body": ["uni.switchTab({ url: '/pages/$1' })$0"], "description": "uni-app 关闭所有非 TabBar 页面，跳转到 TabBar 页面。更多信息查看 <https://uniapp.dcloud.io/api/router?id=switchtab>。"}, "uni-app 关闭当前页面并返回": {"scope": "javascript,typescript", "prefix": ["uni.navigateBack"], "body": ["uni.navigateBack({ delta: ${1:1} })$0"], "description": "uni-app 关闭当前页面并返回。更多信息查看 <https://uniapp.dcloud.io/api/router?id=navigateback>。"}, "uni-app 异步存储本地数据": {"scope": "javascript,typescript", "prefix": ["uni.setStorage"], "body": ["uni.setStorage({", "\tkey: '${1:key}',", "\tdata: ${2:data},", "\tsuccess: (result) => {$3},", "\tfail: (error) => {$4}", "})$0"], "description": "uni-app 异步存储本地数据。更多信息查看 <https://uniapp.dcloud.io/api/storage/storage?id=setstorage>。"}, "uni-app 同步存储本地数据": {"scope": "javascript,typescript", "prefix": ["uni.setStorageSync"], "body": ["uni.setStorageSync('${1:key}', ${2:data})$0"], "description": "uni-app 同步存储本地数据。更多信息查看 <https://uniapp.dcloud.io/api/storage/storage?id=setstoragesync>。"}, "uni-app 异步读取本地数据": {"scope": "javascript,typescript", "prefix": ["uni.getStorage"], "body": ["uni.getStorage({", "\tkey: '${1:key}',", "\tsuccess: ({ data }) => {$2},", "\tfail: (error) => {$3}", "})$0"], "description": "uni-app 异步读取本地数据。更多信息查看 <https://uniapp.dcloud.io/api/storage/storage?id=getstorage>。"}, "uni-app 同步读取本地数据": {"scope": "javascript,typescript", "prefix": ["uni.getStorageSync"], "body": ["uni.getStorageSync('${1:key}')$0"], "description": "uni-app 同步读取本地数据。更多信息查看 <https://uniapp.dcloud.io/api/storage/storage?id=getstoragesync>。"}, "uni-app 异步读取存储信息": {"scope": "javascript,typescript", "prefix": ["uni.getStorageInfo"], "body": ["uni.getStorageInfo({", "\tsuccess: ({ keys, currentSize, limitSize }) => {$1},", "\tfail: (error) => {$2}", "})$0"], "description": "uni-app 异步读取存储信息。更多信息查看 <https://uniapp.dcloud.io/api/storage/storage?id=getstorageinfo>。"}, "uni-app 同步读取存储信息": {"scope": "javascript,typescript", "prefix": ["uni.getStorageInfoSync"], "body": ["uni.getStorageInfoSync()$0"], "description": "uni-app 同步读取存储信息。更多信息查看 <https://uniapp.dcloud.io/api/storage/storage?id=getstorageinfosync>。"}, "uni-app 异步移除本地数据": {"scope": "javascript,typescript", "prefix": ["uni.removeStorage"], "body": ["uni.removeStorage({", "\tkey: '${1:key}',", "\tsuccess: (result) => {$2},", "\tfail: (error) => {$3}", "})$0"], "description": "uni-app 异步移除本地数据。更多信息查看 <https://uniapp.dcloud.io/api/storage/storage?id=removestorage>。"}, "uni-app 同步移除本地数据": {"scope": "javascript,typescript", "prefix": ["uni.removeStorageSync"], "body": ["uni.removeStorageSync('${1:key}')"], "description": "uni-app 同步移除本地数据。更多信息查看 <https://uniapp.dcloud.io/api/storage/storage?id=removestoragesync>。"}, "uni-app 异步清空本地数据": {"scope": "javascript,typescript", "prefix": ["uni.clearStorage"], "body": ["uni.clearStorage({", "\tsuccess: (result) => {$1},", "\tfail: (error) => {$2}", "})$0"], "description": "uni-app 异步清空本地数据。更多信息查看 <https://uniapp.dcloud.io/api/storage/storage?id=clearstorage>。"}, "uni-app 同步清空本地数据": {"scope": "javascript,typescript", "prefix": ["uni.clearStorageSync"], "body": ["uni.clearStorageSync()$0"], "description": "uni-app 同步清理本地数据。更多信息查看 <https://uniapp.dcloud.io/api/storage/storage?id=clearstoragesync>。"}, "uni-app 获取当前地理位置": {"scope": "javascript,typescript", "prefix": ["uni.getLocation"], "body": ["uni.getLocation({", "\ttype: '${1|gcj02,wgs84|}',", "\tsuccess: ({ longitude, latitude }) => {$2},", "\tfail: (error) => {$3}", "})$0"], "description": "uni-app 获取当前地理位置。如果需要转换坐标到不同坐标系，建议使用已经封装好的库，如 gcoord 和 coordtransform。更多信息查看 <https://uniapp.dcloud.io/api/location/location?id=getlocation>。"}, "uni-app 打开地图选择位置": {"scope": "javascript,typescript", "prefix": ["uni.chooseLocation"], "body": ["uni.chooseLocation({", "\tsuccess: ({ name, address, longitude, latitude }) => {$1},", "\tfail: (error) => {$2}", "})$0"], "description": "uni-app 打开地图选择位置。更多信息查看 <https://uniapp.dcloud.io/api/location/location?id=chooselocation>。"}, "uni-app 使用内置地图查看位置": {"scope": "javascript,typescript", "prefix": ["uni.openLocation"], "body": ["uni.openLocation({", "\tlongitude: ${1:longitude},", "\tlatitude: ${2:latitude},", "\tname: '${3:name}',", "\taddress: '${4:address}'", "})$0"], "description": "uni-app 使用内置地图查看位置。更多信息查看 <https://uniapp.dcloud.io/api/location/open-location>。"}, "uni-app 创建地图上下文对象": {"scope": "javascript,typescript", "prefix": ["uni.createMapContext"], "body": ["uni.createMapContext(${1:mapId}, ${2:this})$0"], "description": "uni-app 创建地图上下文对象。更多信息查看 <https://uniapp.dcloud.io/api/location/map?id=createmapcontext>。"}, "uni-app 选择相册图片或使用相机拍照": {"scope": "javascript,typescript", "prefix": ["uni.chooseImage"], "body": ["uni.chooseImage({", "\tsizeType: ${1:['original', 'compressed']},", "\tsourceType: ${2:['album', 'camera']},", "\tsuccess: ({ tempFilePaths, tempFiles }) => {$3},", "\tfail: (error) => {$4}", "})$0"], "description": "uni-app 选择相册图片或使用相机拍照。更多信息查看 <https://uniapp.dcloud.io/api/media/image?id=chooseimage>。"}, "uni-app 预览图片": {"scope": "javascript,typescript", "prefix": ["uni.previewImage"], "body": ["uni.previewImage({", "\turls: ${1:tempFilePaths},", "\tsuccess: (result) => {$2},", "\tfail: (error) => {$3}", "})$0"], "description": "uni-app 预览图片。更多信息查看 <https://uniapp.dcloud.io/api/media/image?id=unipreviewimageobject>。"}, "uni-app 获取图片信息": {"scope": "javascript,typescript", "prefix": ["uni.getImageInfo"], "body": ["uni.getImageInfo({", "\tsrc: $1,", "\tsuccess: ({ width, height, path, orientation, type }) => {$2},", "\tfail: (error) => {$3}", "})$0"], "description": "uni-app 获取图片信息。更多信息查看 <https://uniapp.dcloud.io/api/media/image?id=getimageinfo>。"}, "uni-app 保存图片到相册": {"scope": "javascript,typescript", "prefix": ["uni.saveImageToPhotosAlbum"], "body": ["uni.saveImageToPhotosAlbum({", "\tfilePath: $1,", "\tsuccess: (result) => {$2},", "\tfail: (error) => {$3}", "})$0"], "description": "uni-app 保存图片到相册。更多信息查看 <https://uniapp.dcloud.io/api/media/image?id=saveimagetophotosalbum>。"}, "uni-app 压缩图片": {"scope": "javascript,typescript", "prefix": ["uni.compressImage"], "body": ["uni.compressImage({", "\tsrc: $1,", "\tquality: ${2:80},", "\tsuccess: ({ tempFilePath }) => {$3},", "\tfail: (error) => {$4}", "})$0"], "description": "uni-app 压缩图片。更多信息查看 <https://uniapp.dcloud.io/api/media/image?id=compressimage>。"}, "uni-app 选择本地文件": {"scope": "javascript,typescript", "prefix": ["uni.chooseFile"], "body": ["uni.chooseFile({", "\ttype: '${1|all,video,image|}',", "\tsuccess: ({ tempFilePaths, tempFiles }) => {$2},", "\tfail: (error) => {$3}", "})$0"], "description": "uni-app 选择本地文件。更多信息查看 <https://uniapp.dcloud.io/api/media/file?id=choosefile>。"}, "uni-app 获取全局唯一录音管理器": {"scope": "javascript,typescript", "prefix": ["uni.getRecorderManager"], "body": ["uni.getRecorderManager()$0"], "description": "uni-app 获取全局唯一录音管理器。更多信息查看 <https://uniapp.dcloud.io/api/media/record-manager?id=getrecordermanager>。"}, "uni-app 获取全局唯一背景音频管理器": {"scope": "javascript,typescript", "prefix": ["uni.getBackgroundAudioManager"], "body": ["uni.getBackgroundAudioManager()$0"], "description": "uni-app 获取全局唯一背景音频管理器。更多信息查看 <https://uniapp.dcloud.io/api/media/background-audio-manager?id=getbackgroundaudiomanager>。"}, "uni-app 创建音频上下文对象": {"scope": "javascript,typescript", "prefix": ["uni.createInnerAudioContext"], "body": ["uni.createInnerAudioContext()$0"], "description": "uni-app 创建音频上下文对象。更多信息查看 <https://uniapp.dcloud.io/api/media/audio-context?id=createinneraudiocontext>。"}, "uni-app 选择相册视频或使用相机录像": {"scope": "javascript,typescript", "prefix": ["uni.chooseVideo"], "body": ["uni.chooseVideo({", "\tsourceType: ${1:['album', 'camera']},", "\tcamera: '${2|back,front|}',", "\tsuccess: ({ tempFilePath }) => {$3},", "\tfail: (error) => {$4}", "})$0"], "description": "uni-app 选择相册视频或使用相机录像。更多信息查看 <https://uniapp.dcloud.io/api/media/video?id=choosevideo>。"}, "uni-app 保存视频到相册": {"scope": "javascript,typescript", "prefix": ["uni.saveVideoToPhotosAlbum"], "body": ["uni.saveVideoToPhotosAlbum({", "\tfilePath: ${1:tempFilePath},", "\tsuccess: ({ errMsg }) => {$2},", "\tfail: (error) => {$3}", "})$0"], "description": "uni-app 保存视频到相册。更多信息查看 <https://uniapp.dcloud.io/api/media/video?id=savevideotophotosalbum>。"}, "uni-app 创建视频上下文对象": {"scope": "javascript,typescript", "prefix": ["uni.createVideoContext"], "body": ["uni.createVideoContext(${1:videoId}, ${2:this})$0"], "description": "uni-app 创建视频上下文对象。更多信息查看 <https://uniapp.dcloud.io/api/media/video-context?id=createvideocontext>。"}, "uni-app 创建相机上下文对象": {"scope": "javascript,typescript", "prefix": ["uni.createCameraContext"], "body": ["uni.createCameraContext()$0"], "description": "uni-app 创建相机上下文对象。更多信息查看 <https://uniapp.dcloud.io/api/media/camera-context?id=createcameracontext>。"}, "uni-app 创建直播拉流上下文对象": {"scope": "javascript,typescript", "prefix": ["uni.createLivePlayerContext"], "body": ["uni.createLivePlayerContext(${1:livePlayerId}, ${2:this})$0"], "description": "uni-app 创建直播拉流上下文对象。更多信息查看 <https://uniapp.dcloud.io/api/media/live-player-context?id=createliveplayercontext>。"}, "uni-app 创建直播推流上下文对象": {"scope": "javascript,typescript", "prefix": ["uni.createLivePusherContext"], "body": ["uni.createLivePusherContext(${1:livePusherId}, ${2:this})$0"], "description": "uni-app 创建直播推流上下文对象。更多信息查看 <https://uniapp.dcloud.io/api/media/live-player-context?id=createlivepushercontext>。"}, "uni-app 异步获取系统信息": {"scope": "javascript,typescript", "prefix": ["uni.getSystemInfo"], "body": ["uni.getSystemInfo({", "\tsuccess: (result) => {$1},", "\tfail: (error) => {$2}", "})$0"], "description": "uni-app 异步获取系统信息。更多信息查看 <https://uniapp.dcloud.io/api/system/info?id=getsysteminfo>。"}, "uni-app 同步获取系统信息": {"scope": "javascript,typescript", "prefix": ["uni.getSystemInfoSync"], "body": ["uni.getSystemInfoSync()$0"], "description": "uni-app 同步获取系统信息。更多信息查看 <https://uniapp.dcloud.io/api/system/info?id=getsysteminfosync>。"}, "uni-app 判断 API、回调、参数、组件等是否在当前版本可用": {"scope": "javascript,typescript", "prefix": ["uni.canIUse"], "body": ["uni.canIUse('$1')$0"], "description": "uni-app 判断 API、回调、参数、组件等是否在当前版本可用。更多信息查看 <https://uniapp.dcloud.io/api/system/info?id=caniuse>。"}, "uni-app 内存不足告警": {"scope": "javascript,typescript", "prefix": ["uni.onMemoryW<PERSON>ning"], "body": ["uni.onMemoryWarning(() => {$1})$0"], "description": "uni-app 内存不足告警时触发。更多信息查看 <https://uniapp.dcloud.io/api/system/memory>。"}, "uni-app 获取网络类型": {"scope": "javascript,typescript", "prefix": ["uni.getNetworkType"], "body": ["uni.getNetworkType({", "\tsuccess: ({ networkType }) => {$1},", "\tfail: (error) => {$2}", "})$0"], "description": "uni-app 获取网络类型。更多信息查看 <https://uniapp.dcloud.io/api/system/network?id=getnetworktype>。"}, "uni-app 监听网络状态变化": {"scope": "javascript,typescript", "prefix": ["uni.onNetworkStatusChange"], "body": ["uni.onNetworkStatusChange($1)$0"], "description": "uni-app 监听网络状态变化。更多信息查看 <https://uniapp.dcloud.io/api/system/network?id=onnetworkstatuschange>。"}, "uni-app 取消监听网络状态变化": {"scope": "javascript,typescript", "prefix": ["uni.offNetworkStatusChange"], "body": ["uni.offNetworkStatusChange($1)$0"], "description": "uni-app 取消监听网络状态变化。更多信息查看 <https://uniapp.dcloud.io/api/system/network?id=offnetworkstatuschange>。"}, "uni-app 监听系统主题状态变化": {"scope": "javascript,typescript", "prefix": ["uni.onThemeChange"], "body": ["uni.onThemeChange($1)$0"], "description": "uni-app 监听系统主题状态变化。更多信息查看 <https://uniapp.dcloud.io/api/system/theme>。"}, "uni-app 监听加速度数据": {"scope": "javascript,typescript", "prefix": ["uni.onAccelerometerChange"], "body": ["uni.onAccelerometerChange($1)$0"], "description": "uni-app 监听加速度数据。更多信息查看 <https://uniapp.dcloud.io/api/system/accelerometer?id=onaccelerometerchange>。"}, "uni-app 取消监听加速度数据": {"scope": "javascript,typescript", "prefix": ["uni.offAccelerometerChange"], "body": ["uni.offAccelerometerChange($1)$0"], "description": "uni-app 取消监听加速度数据。更多信息查看 <https://uniapp.dcloud.io/api/system/accelerometer?id=offaccelerometerchange>。"}, "uni-app 开始监听加速度数据": {"scope": "javascript,typescript", "prefix": ["uni.startAccelerometer"], "body": ["uni.startAccelerometer({", "\tinterval: '${1|normal,game,ui|}',", "\tsuccess: (result) => {$2},", "\tfail: (error) => {$3}", "})$0"], "description": "uni-app 开始监听加速度数据。更多信息查看 <https://uniapp.dcloud.io/api/system/accelerometer?id=startaccelerometer>。"}, "uni-app 停止监听加速度数据": {"scope": "javascript,typescript", "prefix": ["uni.stopAccelerometer"], "body": ["uni.stopAccelerometer({", "\tsuccess: (result) => {$1},", "\tfail: (error) => {$2}", "})$0"], "description": "uni-app 停止监听加速度数据。更多信息查看 <https://uniapp.dcloud.io/api/system/accelerometer?id=stopaccelerometer>。"}, "uni-app 监听罗盘数据": {"scope": "javascript,typescript", "prefix": ["uni.onCompassChange"], "body": ["uni.onCompassChange($1)$0"], "description": "uni-app 监听罗盘数据。更多信息查看 <https://uniapp.dcloud.io/api/system/compass?id=oncompasschange>。"}, "uni-app 取消监听罗盘数据": {"scope": "javascript,typescript", "prefix": ["uni.offCompassChange"], "body": ["uni.offCompassChange($1)$0"], "description": "uni-app 取消监听罗盘数据。更多信息查看 <https://uniapp.dcloud.io/api/system/compass?id=offcompasschange>。"}, "uni-app 开始监听罗盘数据": {"scope": "javascript,typescript", "prefix": ["uni.startCompass"], "body": ["uni.startCompass({", "\tsuccess: (result) => {$1},", "\tfail: (error) => {$2}", "})$0"], "description": "uni-app 开始监听罗盘数据。更多信息查看 <https://uniapp.dcloud.io/api/system/compass?id=startcompass>。"}, "uni-app 停止监听罗盘数据": {"scope": "javascript,typescript", "prefix": ["uni.stopCompass"], "body": ["uni.stopCompass({", "\tsuccess: (result) => {$1},", "\tfail: (error) => {$2}", "})$0"], "description": "uni-app 停止监听罗盘数据。更多信息查看 <https://uniapp.dcloud.io/api/system/compass?id=stopcompass>。"}, "uni-app 监听陀螺仪数据": {"scope": "javascript,typescript", "prefix": ["uni.onGyroscopeChange"], "body": ["uni.onGyroscopeChange($1)$0"], "description": "uni-app 监听陀螺仪数据。更多信息查看 <https://uniapp.dcloud.io/api/system/gyroscope?id=ongyroscopechange>。"}, "uni-app 取消监听陀螺仪数据": {"scope": "javascript,typescript", "prefix": ["uni.offGyroscopeChange"], "body": ["uni.offGyroscopeChange($1)$0"], "description": "uni-app 取消监听陀螺仪数据。更多信息查看 <https://uniapp.dcloud.io/api/system/gyroscope?id=offgyroscopechange>。"}, "uni-app 开始监听陀螺仪数据": {"scope": "javascript,typescript", "prefix": ["uni.startGyroscope"], "body": ["uni.startGyroscope({", "\tsuccess: (result) => {$1},", "\tfail: (error) => {$2}", "})$0"], "description": "uni-app 开始监听陀螺仪数据。更多信息查看 <https://uniapp.dcloud.io/api/system/gyroscope?id=startgyroscope>。"}, "uni-app 停止监听陀螺仪数据": {"scope": "javascript,typescript", "prefix": ["uni.stopGyroscope"], "body": ["uni.stopGyroscope({", "\tsuccess: (result) => {$1},", "\tfail: (error) => {$2}", "})$0"], "description": "uni-app 停止监听陀螺仪数据。更多信息查看 <https://uniapp.dcloud.io/api/system/gyroscope?id=stopgyroscope>。"}, "uni-app 拨打电话": {"scope": "javascript,typescript", "prefix": ["uni.makePhoneCall"], "body": ["uni.makePhoneCall({", "\tphoneNumber: '$1',", "\tsuccess: (result) => {$2},", "\tfail: (error) => {$3}", "})$0"], "description": "uni-app 拨打电话。更多信息查看 <https://uniapp.dcloud.io/api/system/phone>。"}, "uni-app 扫码": {"scope": "javascript,typescript", "prefix": ["uni.scanCode"], "body": ["uni.scanCode({", "\tonlyFromCamera: ${1|false,true|},", "\tscanType: ${2:['qrCode', 'barCode']},", "\tsuccess: ({ result, scanType, charSet, path }) => {$3},", "\tfail: (error) => {$4}", "})$0"], "description": "uni-app 扫码。更多信息查看 <https://uniapp.dcloud.io/api/system/barcode>。"}, "uni-app 设置剪贴板内容": {"scope": "javascript,typescript", "prefix": ["uni.setClipboardData"], "body": ["uni.setClipboardData({", "\tdata: '$1',", "\tsuccess: (result) => {$2},", "\tfail: (error) => {$3}", "})$0"], "description": "uni-app 设置剪贴板内容。更多信息查看 <https://uniapp.dcloud.io/api/system/clipboard>。"}, "uni-app 获取剪贴板内容": {"scope": "javascript,typescript", "prefix": ["uni.getClipboardData"], "body": ["uni.getClipboardData({", "\tsuccess: (result) => {$1},", "\tfail: (error) => {$2}", "})$0"], "description": "uni-app 获取剪贴板内容。更多信息查看 <https://uniapp.dcloud.io/api/system/clipboard>。"}, "uni-app 设置屏幕亮度": {"scope": "javascript,typescript", "prefix": ["uni.setScreenBrightness"], "body": ["uni.setScreenBrightness({", "\tvalue: $1,", "\tsuccess: (result) => {$2},", "\tfail: (error) => {$3}", "})$0"], "description": "uni-app 设置屏幕亮度。更多信息查看 <https://uniapp.dcloud.io/api/system/brightness?id=setscreenbrightness>。"}, "uni-app 获取屏幕亮度": {"scope": "javascript,typescript", "prefix": ["uni.getScreenBrightness"], "body": ["uni.getScreenBrightness({", "\tsuccess: ({ value }) => {$1},", "\tfail: (error) => {$2}", "})$0"], "description": "uni-app 获取屏幕亮度。更多信息查看 <https://uniapp.dcloud.io/api/system/brightness?id=getscreenbrightness>。"}, "uni-app 设置常亮状态": {"scope": "javascript,typescript", "prefix": ["uni.setKeepScreenOn"], "body": ["uni.setKeepScreenOn({", "\tkeepScreenOn: ${1|false,true|},", "\tsuccess: ({ errMsg }) => {$2},", "\tfail: (error) => {$3}", "})$0"], "description": "uni-app 设置屏幕亮度。更多信息查看 <https://uniapp.dcloud.io/api/system/brightness?id=setkeepscreenon>。"}, "uni-app 监听截屏": {"scope": "javascript,typescript", "prefix": ["uni.onUserCaptureScreen"], "body": ["uni.onUserCaptureScreen(() => {$1})$0"], "description": "uni-app 监听截屏。更多信息查看 <https://uniapp.dcloud.io/api/system/capture-screen>。"}, "uni-app 使手机振动": {"scope": "javascript,typescript", "prefix": ["uni.vibrate"], "body": ["uni.vibrate({", "\tsuccess: (result) => {$1},", "\tfail: (error) => {$2}", "})$0"], "description": "uni-app 使手机振动。更多信息查看 <https://uniapp.dcloud.io/api/system/vibrate?id=vibrate>。"}, "uni-app 使手机长振动": {"scope": "javascript,typescript", "prefix": ["uni.vibrateLong"], "body": ["uni.vibrateLong({", "\tsuccess: (result) => {$1},", "\tfail: (error) => {$2}", "})$0"], "description": "uni-app 使手机长振动。更多信息查看 <https://uniapp.dcloud.io/api/system/vibrate?id=vibratelong>。"}, "uni-app 使手机短振动": {"scope": "javascript,typescript", "prefix": ["uni.vibrateShort"], "body": ["uni.vibrateShort({", "\tsuccess: (result) => {$1},", "\tfail: (error) => {$2}", "})$0"], "description": "uni-app 使手机短振动。更多信息查看 <https://uniapp.dcloud.io/api/system/vibrate?id=vibrateshort>。"}, "uni-app 添加手机联系人": {"scope": "javascript,typescript", "prefix": ["uni.addPhoneContact"], "body": ["uni.addPhoneContact({", "\tsuccess: (result) => {$1},", "\tfail: (error) => {$2}", "})$0"], "description": "uni-app 添加手机联系人。更多信息查看 <https://uniapp.dcloud.io/api/system/contact>。"}, "uni-app 初始化蓝牙模块": {"scope": "javascript,typescript", "prefix": ["uni.openBluetoothAdapter"], "body": ["uni.openBluetoothAdapter({", "\tsuccess: (result) => {$1},", "\tfail: (error) => {$2}", "})$0"], "description": "uni-app 初始化蓝牙模块。更多信息查看 <https://uniapp.dcloud.io/api/system/bluetooth>。"}, "uni-app 开始搜寻蓝牙设备": {"scope": "javascript,typescript", "prefix": ["uni.startBluetoothDevicesDiscovery"], "body": ["uni.startBluetoothDevicesDiscovery({", "\tservices: ${1:[]},", "\tsuccess: (result) => {$2},", "\tfail: (error) => {$3}", "})$0"], "description": "uni-app 开始搜寻蓝牙设备。更多信息查看 <https://uniapp.dcloud.io/api/system/bluetooth>。"}, "uni-app 监听搜寻到蓝牙设备": {"scope": "javascript,typescript", "prefix": ["uni.onBluetoothDeviceFound"], "body": ["uni.onBluetoothDeviceFound(({ devices }) => {$1})$0"], "description": "uni-app 监听搜寻到蓝牙设备。更多信息查看 <https://uniapp.dcloud.io/api/system/bluetooth>。"}, "uni-app 停止搜寻蓝牙设备": {"scope": "javascript,typescript", "prefix": ["uni.stopBluetoothDevicesDiscovery"], "body": ["uni.stopBluetoothDevicesDiscovery({", "\tsuccess: (result) => {$1},", "\tfail: (error) => {$2}", "})$0"], "description": "uni-app 停止搜寻蓝牙设备。更多信息查看 <https://uniapp.dcloud.io/api/system/bluetooth>。"}, "uni-app 监听蓝牙适配器状态变化": {"scope": "javascript,typescript", "prefix": ["uni.onBluetoothAdapterStateChange"], "body": ["uni.onBluetoothAdapterStateChange(({ available, discovering }) => {$1})$0"], "description": "uni-app 监听蓝牙适配器状态变化。更多信息查看 <https://uniapp.dcloud.io/api/system/bluetooth>。"}, "uni-app 根据 uuid 获取已经连接的蓝牙设备": {"scope": "javascript,typescript", "prefix": ["uni.getConnectedBluetoothDevices"], "body": ["uni.getConnectedBluetoothDevices({", "\tservices: ${1:[]},", "\tsuccess: ({ devices }) => {$2},", "\tfail: (error) => {$3}", "})$0"], "description": "uni-app 根据 uuid 获取已经连接的蓝牙设备。更多信息查看 <https://uniapp.dcloud.io/api/system/bluetooth>。"}, "uni-app 获取蓝牙模块生效期间所有已发现的蓝牙设备": {"scope": "javascript,typescript", "prefix": ["uni.getBluetoothDevices"], "body": ["uni.getBluetoothDevices({", "\tsuccess: ({ devices }) => {$1},", "\tfail: (error) => {$2}", "})$0"], "description": "uni-app 获取蓝牙模块生效期间所有已发现的蓝牙设备。更多信息查看 <https://uniapp.dcloud.io/api/system/bluetooth>。"}, "uni-app 获取蓝牙适配器状态": {"scope": "javascript,typescript", "prefix": ["uni.getBluetoothAdapterState"], "body": ["uni.getBluetoothAdapterState({", "\tsuccess: ({ available, discovering }) => {$1},", "\tfail: (error) => {$2}", "})$0"], "description": "uni-app 获取蓝牙适配器状态。更多信息查看 <https://uniapp.dcloud.io/api/system/bluetooth>。"}, "uni-app 关闭蓝牙模块": {"scope": "javascript,typescript", "prefix": ["uni.closeBluetoothAdapter"], "body": ["uni.getBluetoothAdapterState({", "\tsuccess: (result) => {$1},", "\tfail: (error) => {$2}", "})$0"], "description": "uni-app 关闭蓝牙模块。更多信息查看 <https://uniapp.dcloud.io/api/system/bluetooth>。"}, "uni-app 设置蓝牙最大传输单元": {"scope": "javascript,typescript", "prefix": ["uni.setBLEMTU"], "body": ["uni.setBLEMTU({", "\tdeviceId: '$1',", "\tmtu: $2,", "\tsuccess: (result) => {$3},", "\tfail: (error) => {$4}", "})$0"], "description": "uni-app 设置蓝牙最大传输单元。更多信息查看 <https://uniapp.dcloud.io/api/system/ble>。"}, "uni-app 向低功耗蓝牙设备特征值写入二进制数据": {"scope": "javascript,typescript", "prefix": ["uni.writeBLECharacteristicValue"], "body": ["uni.writeBLECharacteristicValue({", "\tdeviceId: '$1',", "\tserviceId: '$2',", "\tcharacteristicId: '$3',", "\tvalue: $4,", "\tsuccess: (result) => {$5},", "\tfail: (error) => {$6}", "})$0"], "description": "uni-app 向低功耗蓝牙设备特征值写入二进制数据。更多信息查看 <https://uniapp.dcloud.io/api/system/ble>。"}, "uni-app 读取低功耗蓝牙设备特征值的二进制数据": {"scope": "javascript,typescript", "prefix": ["uni.readBLECharacteristicValue"], "body": ["uni.readBLECharacteristicValue({", "\tdeviceId: '$1',", "\tserviceId: '$2',", "\tcharacteristicId: '$3',", "\tsuccess: (result) => {$4},", "\tfail: (error) => {$5}", "})$0"], "description": "uni-app 读取低功耗蓝牙设备特征值的二进制数据。更多信息查看 <https://uniapp.dcloud.io/api/system/ble>。"}, "uni-app 监听低功耗蓝牙连接状态改变": {"scope": "javascript,typescript", "prefix": ["uni.onBLEConnectionStateChange"], "body": ["uni.onBLEConnectionStateChange(({ deviceId, connected }) => {$1})$0"], "description": "uni-app 监听低功耗蓝牙连接状态改变。更多信息查看 <https://uniapp.dcloud.io/api/system/ble>。"}, "uni-app 监听低功耗蓝牙设备特征值变化": {"scope": "javascript,typescript", "prefix": ["uni.onBLECharacteristicValueChange"], "body": ["uni.onBLECharacteristicValueChange(({", "\tdeviceId,", "\tserviceId,", "\tcharacteristicId,", "\tvalue", "}) => {$1})$0"], "description": "uni-app 监听低功耗蓝牙设备特征值变化。更多信息查看 <https://uniapp.dcloud.io/api/system/ble>。"}, "uni-app 启用低功耗蓝牙设备特征值变化时的 notify 功能": {"scope": "javascript,typescript", "prefix": ["uni.notifyBLECharacteristicValueChange"], "body": ["uni.notifyBLECharacteristicValueChange({", "\tdeviceId: '$1',", "\tserviceId: '$2',", "\tcharacteristicId: '$3',", "\tstate: ${4|true,false|},", "\tsuccess: (result) => {$5},", "\tfail: (error) => {$6}", "})$0"], "description": "uni-app 启用低功耗蓝牙设备特征值变化时的 notify 功能。更多信息查看 <https://uniapp.dcloud.io/api/system/ble>。"}, "uni-app 获取蓝牙设备所有服务": {"scope": "javascript,typescript", "prefix": ["uni.getBLEDeviceServices"], "body": ["uni.getBLEDeviceServices({", "\tdeviceId: '$1',", "\tsuccess: ({ services }) => {$2},", "\tfail: (error) => {$3}", "})$0"], "description": "uni-app 获取蓝牙设备所有服务。更多信息查看 <https://uniapp.dcloud.io/api/system/ble>。"}, "uni-app 获取蓝牙设备信号强度": {"scope": "javascript,typescript", "prefix": ["uni.getBLEDeviceRSSI"], "body": ["uni.getBLEDeviceRSSI({", "\tdeviceId: '$1',", "\tsuccess: ({ RSSI }) => {$2},", "\tfail: (error) => {$3}", "})$0"], "description": "uni-app 获取蓝牙设备信号强度。更多信息查看 <https://uniapp.dcloud.io/api/system/ble>。"}, "uni-app 获取蓝牙设备某个服务所有特征值": {"scope": "javascript,typescript", "prefix": ["uni.getBLEDeviceCharacteristics"], "body": ["uni.getBLEDeviceCharacteristics({", "\tdeviceId: '$1',", "\tserviceId: '$2',", "\tsuccess: ({ characteristics }) => {$3},", "\tfail: (error) => {$4}", "})$0"], "description": "uni-app 获取蓝牙设备某个服务所有特征值。更多信息查看 <https://uniapp.dcloud.io/api/system/ble>。"}, "uni-app 连接低功耗蓝牙设备": {"scope": "javascript,typescript", "prefix": ["uni.createBLEConnection"], "body": ["uni.createBLEConnection({", "\tdeviceId: '$1',", "\tsuccess: (result) => {$2},", "\tfail: (error) => {$3}", "})$0"], "description": "uni-app 连接低功耗蓝牙设备。更多信息查看 <https://uniapp.dcloud.io/api/system/ble>。"}, "uni-app 断开与低功耗蓝牙设备的连接": {"scope": "javascript,typescript", "prefix": ["uni.closeBLEConnection"], "body": ["uni.closeBLEConnection({", "\tdeviceId: '$1',", "\tsuccess: (result) => {$2},", "\tfail: (error) => {$3}", "})$0"], "description": "uni-app 断开与低功耗蓝牙设备的连接。更多信息查看 <https://uniapp.dcloud.io/api/system/ble>。"}, "uni-app 监听 iBeacon 服务状态变化": {"scope": "javascript,typescript", "prefix": ["uni.onBeaconServiceChange"], "body": ["uni.onBeaconServiceChange(({ available, discovering }) => {$1})$0"], "description": "uni-app 监听 iBeacon 服务状态变化。更多信息查看 <https://uniapp.dcloud.io/api/system/ibeacon>。"}, "uni-app 监听 iBeacon 设备更新": {"scope": "javascript,typescript", "prefix": ["uni.onBeaconUpdate"], "body": ["uni.onBeaconUpdate(({ beacons }) => {$1})$0"], "description": "uni-app 监听 iBeacon 设备更新。更多信息查看 <https://uniapp.dcloud.io/api/system/ibeacon>。"}, "uni-app 获取所有已搜索到的 iBeacon 设备": {"scope": "javascript,typescript", "prefix": ["uni.getBeacons"], "body": ["uni.getBeacons({", "\tsuccess: ({ beacons }) => {$1},", "\tfail: (error) => {$2}", "})$0"], "description": "uni-app 获取所有已搜索到的 iBeacon 设备。更多信息查看 <https://uniapp.dcloud.io/api/system/ibeacon>。"}, "uni-app 开始搜索附近 iBeacon 设备": {"scope": "javascript,typescript", "prefix": ["uni.startBeaconDiscovery"], "body": ["uni.startBeaconDiscovery({", "\tuuids: $1,", "\tsuccess: (result) => {$2},", "\tfail: (error) => {$3}", "})$0"], "description": "uni-app 开始搜索附近 iBeacon 设备。更多信息查看 <https://uniapp.dcloud.io/api/system/ibeacon>。"}, "uni-app 停止搜索附近 iBeacon 设备": {"scope": "javascript,typescript", "prefix": ["uni.stopBeaconDiscovery"], "body": ["uni.stopBeaconDiscovery({", "\tsuccess: (result) => {$1},", "\tfail: (error) => {$2}", "})$0"], "description": "uni-app 停止搜索附近 iBeacon 设备。更多信息查看 <https://uniapp.dcloud.io/api/system/ibeacon>。"}, "uni-app 开始 SOTER 生物认证": {"scope": "javascript,typescript", "prefix": ["uni.startSoterAuthentication"], "body": ["uni.startSoterAuthentication({", "\trequestAuthModes: ${1:['fingerPrint', 'facial']},", "\tchallenge: '$2',", "\tauthContent: '$3',", "\tsuccess: ({ authMode, resultJSON, resultJSONSignature, errCode, errMsg }) => {$4},", "\tfail: (error) => {$5}", "})$0"], "description": "uni-app 开始 SOTER 生物认证。更多信息查看 <https://uniapp.dcloud.io/api/system/authentication?id=startsoterauthentication>。"}, "uni-app 获取支持的 SOTER 生物认证方式": {"scope": "javascript,typescript", "prefix": ["uni.checkIsSupportSoterAuthentication"], "body": ["uni.checkIsSupportSoterAuthentication({", "\tsuccess: ({ supportMode }) => {$1},", "\tfail: (error) => {$2}", "})$0"], "description": "uni-app 获取支持的 SOTER 生物认证方式。更多信息查看 <https://uniapp.dcloud.io/api/system/authentication?id=checkissupportsoterauthentication>。"}, "uni-app 获取设备内是否录入生物信息": {"scope": "javascript,typescript", "prefix": ["uni.checkIsSoterEnrolledInDevice"], "body": ["uni.checkIsSoterEnrolledInDevice({", "\tcheckAuthMode: '${1|fingerPrint,facial|}',", "\tsuccess: ({ isEnrolled, errMsg }) => {$2},", "\tfail: (error) => {$3}", "})$0"], "description": "uni-app 获取设备内是否录入生物信息。更多信息查看 <https://uniapp.dcloud.io/api/system/authentication?id=checkissoterenrolledindevice>。"}, "uni-app 隐藏软键盘": {"scope": "javascript,typescript", "prefix": ["uni.hideKeyboard"], "body": ["uni.hideKeyboard()$0"], "description": "uni-app 隐藏软键盘。更多信息查看 <https://uniapp.dcloud.io/api/key?id=hidekeyboard>。"}, "uni-app 监听键盘高度变化": {"scope": "javascript,typescript", "prefix": ["uni.onKeyboardHeightChange"], "body": ["uni.onKeyboardHeightChange($1)$0"], "description": "uni-app 监听键盘高度变化。更多信息查看 <https://uniapp.dcloud.io/api/key?id=onkeyboardheightchange>。"}, "uni-app 取消监听键盘高度变化": {"scope": "javascript,typescript", "prefix": ["uni.offKeyboardHeightChange"], "body": ["uni.offKeyboardHeightChange($1)$0"], "description": "uni-app 取消监听键盘高度变化。更多信息查看 <https://uniapp.dcloud.io/api/key?id=offkeyboardheightchange>。"}, "uni-app 获取 focus 后的输入框光标位置": {"scope": "javascript,typescript", "prefix": ["uni.getSelectedTextRange"], "body": ["uni.getSelectedTextRange({", "\tsuccess: ({ start, end }) => {$1},", "\tfail: (error) => {$2}", "})$0"], "description": "uni-app 监听键盘高度变化。更多信息查看 <https://uniapp.dcloud.io/api/key?id=getselectedtextrange>。"}, "uni-app 显示消息提示框": {"scope": "javascript,typescript", "prefix": ["uni.showToast"], "body": ["uni.showToast({", "\ttitle: '$1',", "\ticon: '${2|success,none|}',", "\tmask: true", "})"], "description": "uni-app 显示消息提示框。这可能与加载提示框冲突，请在测试时以真机测试为准。更多信息查看 <https://uniapp.dcloud.io/api/ui/prompt?id=showtoast>。"}, "uni-app 隐藏消息提示框": {"scope": "javascript,typescript", "prefix": ["uni.hideToast"], "body": ["uni.hideToast()$0"], "description": "uni-app 隐藏消息提示框。更多信息查看 <https://uniapp.dcloud.io/api/ui/prompt?id=hidetoast>。"}, "uni-app 显示加载中提示框": {"scope": "javascript,typescript", "prefix": ["uni.showLoading"], "body": ["uni.showLoading({", "\ttitle: '${1:加载中}',", "\tmask: ${2:true}", "})$0"], "description": "uni-app 显示加载中提示框。这可能与消息提示框冲突，请在测试时以真机测试为准。更多信息查看 <https://uniapp.dcloud.io/api/ui/prompt?id=showloading>。"}, "uni-app 隐藏加载中提示框": {"scope": "javascript,typescript", "prefix": ["uni.hideLoading"], "body": ["uni.hideLoading()$0"], "description": "uni-app 隐藏加载中提示框。更多信息查看 <https://uniapp.dcloud.io/api/ui/prompt?id=hideloading>。"}, "uni-app 显示模态弹窗": {"scope": "javascript,typescript", "prefix": ["uni.showModal"], "body": ["uni.showModal({", "\ttitle: '${1:提示}',", "\tcontent: '$2',", "\tshowCancel: ${3:true},", "\tsuccess: ({ confirm, cancel }) => {$4}", "})$0"], "description": "uni-app 显示模态弹窗。更多信息查看 <https://uniapp.dcloud.io/api/ui/prompt?id=showmodal>。"}, "uni-app 显示操作菜单": {"scope": "javascript,typescript", "prefix": ["uni.showActionSheet"], "body": ["uni.showActionSheet({", "\titemList: [$1],", "\tsuccess: ({ tapIndex }) => {$2},", "\tfail: (error) => {$3}", "})$0"], "description": "uni-app 显示操作菜单。更多信息查看 <https://uniapp.dcloud.io/api/ui/prompt?id=showactionsheet>。"}, "uni-app 动态设置当前页面标题": {"scope": "javascript,typescript", "prefix": ["uni.setNavigationBarTitle"], "body": ["uni.setNavigationBarTitle({ title: '$1' })$0"], "description": "uni-app 动态设置当前页面标题。更多信息查看 <https://uniapp.dcloud.io/api/ui/navigationbar?id=setnavigationbartitle>。"}, "uni-app 设置页面导航条颜色": {"scope": "javascript,typescript", "prefix": ["uni.setNavigationBarColor"], "body": ["uni.setNavigationBarColor({", "\tfrontColor: '${1|#000000,#ffffff|}',", "\tbackgroundColor: '${2:#ff0000}'", "})$0"], "description": "uni-app 设置页面导航条颜色。更多信息查看 <https://uniapp.dcloud.io/api/ui/navigationbar?id=setnavigationbarcolor>。"}, "uni-app 在当前页面显示导航条加载动画": {"scope": "javascript,typescript", "prefix": ["uni.showNavigationBarLoading"], "body": ["uni.showNavigationBarLoading()$0"], "description": "uni-app 在当前页面显示导航条加载动画。更多信息查看 <https://uniapp.dcloud.io/api/ui/navigationbar?id=shownavigationbarloading>。"}, "uni-app 在当前页面隐藏导航条加载动画": {"scope": "javascript,typescript", "prefix": ["uni.hideNavigationBarLoading"], "body": ["uni.hideNavigationBarLoading()$0"], "description": "uni-app 在当前页面隐藏导航条加载动画。更多信息查看 <https://uniapp.dcloud.io/api/ui/navigationbar?id=hidenavigationbarloading>。"}, "uni-app 隐藏返回首页按钮": {"scope": "javascript,typescript", "prefix": ["uni.hideHomeButton"], "body": ["uni.hideHomeButton()$0"], "description": "uni-app 隐藏返回首页按钮。更多信息查看 <https://uniapp.dcloud.io/api/ui/navigationbar?id=hidehomebutton>。"}, "uni-app 动态设置 TabBar 某一项内容": {"scope": "javascript,typescript", "prefix": ["uni.setTabBarItem"], "body": ["uni.setTabBarItem({", "\tindex: $1,", "\ttext: '$2',", "\ticonPath: '/static/$3',", "\tselectedIconPath: '/static/$4'", "})$0"], "description": "uni-app 动态设置 TabBar 某一项内容。更多信息查看 <https://uniapp.dcloud.io/api/ui/tabbar?id=settabbaritem>。"}, "uni-app 动态设置 TabBar 整体样式": {"scope": "javascript,typescript", "prefix": ["uni.setTabBarStyle"], "body": ["uni.setTabBarStyle({", "\tcolor: '${1:#ff0000}',", "\tselectedColor: '${2:#00ff00}',", "\tbackgroundColor: '${3:#0000ff}',", "\tborderStyle: '${4|black,white|}'", "})$0"], "description": "uni-app 动态设置 TabBar 整体样式。更多信息查看 <https://uniapp.dcloud.io/api/ui/tabbar?id=settabbarstyle>。"}, "uni-app 隐藏 TabBar": {"scope": "javascript,typescript", "prefix": ["uni.hideTabBar"], "body": ["uni.hideTabBar()$0"], "description": "uni-app 隐藏 TabBar。更多信息查看 <https://uniapp.dcloud.io/api/ui/tabbar?id=hidetabbar>。"}, "uni-app 显示 TabBar": {"scope": "javascript,typescript", "prefix": ["uni.showTabBar"], "body": ["uni.showTabBar()$0"], "description": "uni-app 显示 TabBar。更多信息查看 <https://uniapp.dcloud.io/api/ui/tabbar?id=showtabbar>。"}, "uni-app 设置 TabBar 某一项徽标": {"scope": "javascript,typescript", "prefix": ["uni.setTabBarBadge"], "body": ["uni.setTabBarBadge({", "\tindex: $1,", "\ttext: '$2'", "})$0"], "description": "uni-app 设置 TabBar 某一项徽标。更多信息查看 <https://uniapp.dcloud.io/api/ui/tabbar?id=settabbarbadge>。"}, "uni-app 移除 TabBar 某一项徽标": {"scope": "javascript,typescript", "prefix": ["uni.removeTabBarBadge"], "body": ["uni.removeTabBarBadge({ index: $1 })$0"], "description": "uni-app 移除 tabBar 某一项徽标。更多信息查看 <https://uniapp.dcloud.io/api/ui/tabbar?id=removetabbarbadge>。"}, "uni-app 设置 TabBar 某一项红点": {"scope": "javascript,typescript", "prefix": ["uni.showTabBarRedDot"], "body": ["uni.showTabBarRedDot({ index: $1 })$0"], "description": "uni-app 设置 TabBar 某一项红点。更多信息查看 <https://uniapp.dcloud.io/api/ui/tabbar?id=showtabbarreddot>。"}, "uni-app 移除 tabBar 某一项红点": {"scope": "javascript,typescript", "prefix": ["uni.hideTabBarRedDot"], "body": ["uni.hideTabBarRedDot({ index: $1 })$0"], "description": "uni-app 移除 tabBar 某一项红点。更多信息查看 <https://uniapp.dcloud.io/api/ui/tabbar?id=hidetabbarreddot>。"}, "uni-app 动态设置窗口背景色": {"scope": "javascript,typescript", "prefix": ["uni.setBackgroundColor"], "body": ["uni.setBackgroundColor({", "\tbackgroundColor: '${1:#ffffff}',", "\tbackgroundColorTop: '${2:#ffffff}',", "\tbackgroundColorBottom: '${3:#ffffff}'", "})$0"], "description": "uni-app 动态设置窗口背景色。更多信息查看 <https://uniapp.dcloud.io/api/ui/bgcolor?id=setbackgroundcolor>。"}, "uni-app 动态设置下拉背景字体、loading 图的样式": {"scope": "javascript,typescript", "prefix": ["uni.setBackgroundTextStyle"], "body": ["uni.setBackgroundTextStyle({ textStyle: '{$1|dark,light|}' })$0"], "description": "uni-app 动态设置下拉背景字体、加载图的样式。更多信息查看 <https://uniapp.dcloud.io/api/ui/bgcolor?id=setbackgroundtextstyle>。"}, "uni-app 创建动画实例": {"scope": "javascript,typescript", "prefix": ["uni.createAnimation"], "body": ["uni.createAnimation({$1})$0"], "description": "uni-app 创建动画实例。更多信息查看 <https://uniapp.dcloud.io/api/ui/animation>。"}, "uni-app 将页面滚动到目标位置": {"scope": "javascript,typescript", "prefix": ["uni.pageScrollTo"], "body": ["uni.pageScrollTo({ scrollTop: ${1:0} })$0"], "description": "uni-app 将页面滚动到目标位置。更多信息查看 <https://uniapp.dcloud.io/api/ui/scroll>。"}, "uni-app 监听窗口尺寸变化": {"scope": "javascript,typescript", "prefix": ["uni.onWindowResize"], "body": ["uni.onWindowResize($1)$0"], "description": "uni-app 监听窗口尺寸变化。更多信息查看 <https://uniapp.dcloud.io/api/ui/window?id=onwindowresize>。"}, "uni-app 取消监听窗口尺寸变化": {"scope": "javascript,typescript", "prefix": ["uni.offWindowResize"], "body": ["uni.offWindowResize($1)$0"], "description": "uni-app 取消监听窗口尺寸变化。更多信息查看 <https://uniapp.dcloud.io/api/ui/window?id=offwindowresize>。"}, "uni-app 获取 topWindow 样式": {"scope": "javascript,typescript", "prefix": ["uni.getTopWindowStyle"], "body": ["uni.getTopWindowStyle()$0"], "description": "uni-app 获取 topWindow 样式。更多信息查看 <https://uniapp.dcloud.io/api/ui/adapt?id=gettopwindowstyle>。"}, "uni-app 获取 leftWindow 样式": {"scope": "javascript,typescript", "prefix": ["uni.getLeftWindowStyle"], "body": ["uni.getLeftWindowStyle()$0"], "description": "uni-app 获取 leftWindow 样式。更多信息查看 <https://uniapp.dcloud.io/api/ui/adapt?id=getleftwindowstyle>。"}, "uni-app 获取 RightWindow 样式": {"scope": "javascript,typescript", "prefix": ["uni.getRightWindowStyle"], "body": ["uni.getRightWindowStyle()$0"], "description": "uni-app 获取 rightWindow 样式。更多信息查看 <https://uniapp.dcloud.io/api/ui/adapt?id=getrightwindowstyle>。"}, "uni-app 设置 topWindow 样式": {"scope": "javascript,typescript", "prefix": ["uni.setTopWindowStyle"], "body": ["uni.setTopWindowStyle({ $1 })$0"], "description": "uni-app 设置 topWindow 样式。更多信息查看 <https://uniapp.dcloud.io/api/ui/adapt?id=settopwindowstyle>。"}, "uni-app 设置 leftWindow 样式": {"scope": "javascript,typescript", "prefix": ["uni.setLeftWindowStyle"], "body": ["uni.setLeftWindowStyle({ $1 })$0"], "description": "uni-app 设置 leftWindow 样式。更多信息查看 <https://uniapp.dcloud.io/api/ui/adapt?id=setleftwindowstyle>。"}, "uni-app 设置 RightWindow 样式": {"scope": "javascript,typescript", "prefix": ["uni.setRightWindowStyle"], "body": ["uni.setRightWindowStyle({ $1 })$0"], "description": "uni-app 设置 rightWindow 样式。更多信息查看 <https://uniapp.dcloud.io/api/ui/adapt?id=setrightwindowstyle>。"}, "uni-app 动态加载网络字体": {"scope": "javascript,typescript", "prefix": ["uni.loadFontFace"], "body": ["uni.loadFontFace({", "\tfamily: '$1',", "\tsource: 'url($2)',", "\tsuccess: (result) => {$3},", "\tfail: (error) => {$4}", "})$0"], "description": "uni-app 动态加载网络字体。更多信息查看 <https://uniapp.dcloud.io/api/ui/font>。"}, "uni-app 开始下拉刷新": {"scope": "javascript,typescript", "prefix": ["uni.startPullDownRefresh"], "body": ["uni.startPullDownRefresh({", "\tsuccess: (result) => {$1},", "\tfail: (error) => {$2}", "})$0"], "description": "uni-app 开始下拉刷新。更多信息查看 <https://uniapp.dcloud.io/api/ui/pulldown?id=startpulldownrefresh>。"}, "uni-app 停止下拉刷新": {"scope": "javascript,typescript", "prefix": ["uni.stopPullDownRefresh"], "body": ["uni.stopPullDownRefresh({", "\tsuccess: (result) => {$1},", "\tfail: (error) => {$2}", "})$0"], "description": "uni-app 停止下拉刷新。更多信息查看 <https://uniapp.dcloud.io/api/ui/pulldown?id=startpulldownrefresh>。"}, "uni-app 创建查询节点信息的实例": {"scope": "javascript,typescript", "prefix": ["uni.createSelectorQuery"], "body": ["uni.createSelectorQuery()$0"], "description": "uni-app 创建查询节点信息的实例。更多信息查看 <https://uniapp.dcloud.io/api/ui/nodes-info>。"}, "uni-app 获取菜单按钮信息": {"scope": "javascript,typescript", "prefix": ["uni.getMenuButtonBoundingClientRect"], "body": ["uni.getMenuButtonBoundingClientRect()$0"], "description": "uni-app 获取菜单按钮信息。更多信息查看 <https://uniapp.dcloud.io/api/ui/menuButton>。"}, "uni-app 保存文件到本地": {"scope": "javascript,typescript", "prefix": ["uni.saveFile"], "body": ["uni.saveFile({", "\ttempFilePath: ${1:tempFilePaths[0]},", "\tsuccess: ({ savedFilePath }) => {$2},", "\tfail: (error) => {$3}", "})$0"], "description": "uni-app 保存文件到本地。更多信息查看 <https://uniapp.dcloud.io/api/file/file?id=savefile>。"}, "uni-app 获取本地已保存的文件列表": {"scope": "javascript,typescript", "prefix": ["uni.getSavedFileList"], "body": ["uni.getSavedFileList({", "\tsuccess: ({ fileList, errMsg }) => {$1}),", "\tfail: (error) => {$2}", "})$0"], "description": "uni-app 获取本地已保存的文件列表。更多信息查看 <https://uniapp.dcloud.io/api/file/file?id=getsavedfilelist>。"}, "uni-app 获取本地文件的文件信息": {"scope": "javascript,typescript", "prefix": ["uni.getSavedFileInfo"], "body": ["uni.getSavedFileInfo({", "\tfilePath: ${1:fileList[0].filePath},", "\tsuccess: ({ size, createTime, errMsg }) => {$2}", "\tfail: (error) => {$3}", "})$0"], "description": "uni-app 获取本地文件的文件信息。更多信息查看 <https://uniapp.dcloud.io/api/file/file?id=getsavedfileinfo>。"}, "uni-app 删除本地存储的文件": {"scope": "javascript,typescript", "prefix": ["uni.removeSavedFile"], "body": ["uni.removeSavedFile({", "\tfilePath: ${1:fileList[0].filePath},", "\tsuccess: (result) => {$2},", "\tfail: (error) => {$3}", "})$0"], "description": "uni-app 删除本地存储的文件。更多信息查看 <https://uniapp.dcloud.io/api/file/file?id=removesavedfile>。"}, "uni-app 获取文件信息": {"scope": "javascript,typescript", "prefix": ["uni.getFileInfo"], "body": ["uni.uni.getFileInfo({", "\tfilePath: ${1:fileList[0].filePath},", "\tsuccess: ({ size, digest, errMsg }) => {$2}", "\tfail: (error) => {$3}", "})$0"], "description": "uni-app 获取文件信息。更多信息查看 <https://uniapp.dcloud.io/api/file/file?id=getfileinfo>。"}, "uni-app 新开页面打开文档": {"scope": "javascript,typescript", "prefix": ["uni.openDocument"], "body": ["uni.openDocument({", "\tfilePath: ${1:tempFilePath},", "\tsuccess: (result) => {$2},", "\tfail: (error) => {$3}", "})$0"], "description": "uni-app 新开页面打开文档。更多信息查看 <https://uniapp.dcloud.io/api/file/file?id=opendocument>。"}, "uni-app 创建画布上下文": {"scope": "javascript,typescript", "prefix": ["uni.createCanvasContext"], "body": ["uni.createCanvasContext({ canvasId: '${1:canvasId}' })$0"], "description": "uni-app 创建画布上下文。更多信息查看 <https://uniapp.dcloud.io/api/canvas/createCanvasContext>。"}, "uni-app 导出画布内容": {"scope": "javascript,typescript", "prefix": ["uni.canvasToTempFilePath"], "body": ["uni.canvasToTempFilePath({ canvasId: '${1:canvasId}' })$0"], "description": "uni-app 导出画布内容。更多信息查看 <https://uniapp.dcloud.io/api/canvas/canvasToTempFilePath>。"}, "uni-app 将像素数据绘制到画布": {"scope": "javascript,typescript", "prefix": ["uni.canvasPutImageData"], "body": ["uni.canvasPutImageData({", "\tcanvasId: '${1:canvasId}',", "\tdata: $2,", "\tx: $3,", "\ty: $4,", "\twidth: $5", "})$0"], "description": "uni-app 将像素数据绘制到画布。更多信息查看 <https://uniapp.dcloud.io/api/canvas/canvasPutImageData>。"}, "uni-app 获取画布的像素数据": {"scope": "javascript,typescript", "prefix": ["uni.canvasGetImageData"], "body": ["uni.canvasGetImageData({", "\tcanvasId: '${1:canvasId}',", "\tx: $2,", "\ty: $3,", "\twidth: $4,", "\theight: $5", "})$0"], "description": "uni-app 获取画布的像素数据。更多信息查看 <https://uniapp.dcloud.io/api/canvas/canvasGetImageData>。"}, "uni-app 获取服务供应商": {"scope": "javascript,typescript", "prefix": ["uni.get<PERSON>rovider"], "body": ["uni.getProvider({", "\tservice: ${1|oauth,share,payment,push},", "\tsuccess: ({ service, provider }) => {$2},", "\tfail: (error) => {$3}", "})$0"], "description": "uni-app 获取服务供应商。更多信息查看 <https://uniapp.dcloud.io/api/plugins/provider>。"}, "uni-app 登录": {"scope": "javascript,typescript", "prefix": ["uni.login"], "body": ["uni.login({", "\tprovider: $1,", "\tsuccess: (result) => {$2},", "\tfail: (error) => {$3}", "})$0"], "description": "uni-app 登录。更多信息查看 <https://uniapp.dcloud.io/api/plugins/login?id=login>。"}, "uni-app 检查登录状态": {"scope": "javascript,typescript", "prefix": ["uni.checkSession"], "body": ["uni.checkSession({", "\tsuccess: (result) => {$1},", "\tfail: (error) => {$2}", "})$0"], "description": "uni-app 检查登录状态。更多信息查看 <https://uniapp.dcloud.io/api/plugins/login?id=unichecksession>。"}, "uni-app 获取用户信息": {"scope": "javascript,typescript", "prefix": ["uni.getUserInfo"], "body": ["uni.getUserInfo({", "\tprovider: $1,", "\twithCredentials: ${2|true,false|},", "\tsuccess: ({ userInfo, rawData, signature, encryptedData, iv, errMsg }) => {$3},", "\tfail: (error) => {$4}", "})$0"], "description": "uni-app 检查登录状态。更多信息查看 <https://uniapp.dcloud.io/api/plugins/login?id=unichecksession>。"}, "uni-app 分享": {"scope": "javascript,typescript", "prefix": ["uni.share"], "body": ["uni.share({", "\tprovider: $1,", "\tsuccess: (result) => {$2},", "\tfail: (error) => {$3}", "})$0"], "description": "uni-app 分享。更多信息查看 <https://uniapp.dcloud.io/api/plugins/share?id=share>。"}, "uni-app 原生菜单显示分享按钮": {"scope": "javascript,typescript", "prefix": ["uni.showShareMenu"], "body": ["uni.showShareMenu({", "\tsuccess: (result) => {$1},", "\tfail: (error) => {$2}", "})$0"], "description": "uni-app 原生菜单显示分享按钮。更多信息查看 <https://uniapp.dcloud.io/api/plugins/share?id=showsharemenu>。"}, "uni-app 原生菜单隐藏分享按钮": {"scope": "javascript,typescript", "prefix": ["uni.hideShareMenu"], "body": ["uni.hideShareMenu({", "\tsuccess: (result) => {$1},", "\tfail: (error) => {$2}", "})$0"], "description": "uni-app 原生菜单隐藏分享按钮。更多信息查看 <https://uniapp.dcloud.io/api/plugins/share?id=hidesharemenu>。"}, "uni-app 支付": {"scope": "javascript,typescript", "prefix": ["uni.requestPayment"], "body": ["uni.requestPayment({", "\tprovider: $1,", "\torderInfo: $2,", "\tsuccess: (result) => {$3},", "\tfail: (error) => {$4}", "})$0"], "description": "uni-app 支付。更多信息查看 <https://uniapp.dcloud.io/api/plugins/payment>。"}, "uni-app 授权": {"scope": "javascript,typescript", "prefix": ["uni.authorize"], "body": ["uni.authorize({", "\tscope: '$1',", "\tsuccess: (result) => {$2},", "\tfail: (error) => {$3}", "})$0"], "description": "uni-app 授权。更多信息查看 <https://uniapp.dcloud.io/api/other/authorize>。"}, "uni-app 打开设置界面": {"scope": "javascript,typescript", "prefix": ["uni.openSetting"], "body": ["uni.getSetting({", "\tsuccess: ({ authSetting }) => {$1},", "\tfail: (error) => {$2}", "})$0"], "description": "uni-app 打开设置界面。更多信息查看 <https://uniapp.dcloud.io/api/other/setting?id=opensetting>。"}, "uni-app 获取当前设置": {"scope": "javascript,typescript", "prefix": ["uni.getSetting"], "body": ["uni.getSetting({", "\tsuccess: ({ authSetting, subscriptionsSetting }) => {$1},", "\tfail: (error) => {$2}", "})$0"], "description": "uni-app 获取当前设置。更多信息查看 <https://uniapp.dcloud.io/api/other/setting?id=getsetting>。"}, "uni-app 获取收货地址": {"scope": "javascript,typescript", "prefix": ["uni.chooseAddress"], "body": ["uni.chooseAddress({", "\tsuccess: ({", "\t\tuserName,", "\t\tpostalCode,", "\t\tprovinceName,", "\t\tcityName,", "\t\tdetailInfo,", "\t\tnationalCode,", "\t\ttelNumber,", "\t\terrMsg", "\t}) => {$1},", "\tfail: (error) => {$2}", "})$0"], "description": "uni-app 获取收货地址。更多信息查看 <https://uniapp.dcloud.io/api/other/choose-address>。"}, "uni-app 选择发票抬头": {"scope": "javascript,typescript", "prefix": ["uni.chooseInvoiceTitle"], "body": ["uni.chooseInvoiceTitle({", "\tsuccess: ({", "\t\ttype,", "\t\ttitle,", "\t\ttaxNumber,", "\t\tcompanyAddress,", "\t\ttelephone,", "\t\tbankName,", "\t\tbankAccount,", "\t\terrMsg", "\t}) => {$1},", "\tfail: (error) => {$2}", "})$0"], "description": "uni-app 选择发票抬头。更多信息查看 <https://uniapp.dcloud.io/api/other/invoice-title>。"}, "uni-app 打开小程序": {"scope": "javascript,typescript", "prefix": ["uni.navigateToMiniProgram"], "body": ["uni.navigateToMiniProgram({", "\tappId: '$1',", "\tpath: '$2',", "\tsuccess: (result) => {$3},", "\tfail: (error) => {$4}", "})$0"], "description": "uni-app 打开小程序。更多信息查看 <https://uniapp.dcloud.io/api/other/open-miniprogram?id=navigatetominiprogram>。"}, "uni-app 从打开的小程序返回": {"scope": "javascript,typescript", "prefix": ["uni.navigateBackMiniProgram"], "body": ["uni.navigateBackMiniProgram({", "\tsuccess: (result) => {$1},", "\tfail: (error) => {$2}", "})$0"], "description": "uni-app 从打开的小程序返回。更多信息查看 <https://uniapp.dcloud.io/api/other/open-miniprogram?id=navigatebackminiprogram>。"}, "uni-app 同步获取账号信息": {"scope": "javascript,typescript", "prefix": ["uni.getAccountInfoSync"], "body": ["uni.getAccountInfoSync()$0"], "description": "uni-app 同步获取账号信息。更多信息查看 <https://uniapp.dcloud.io/api/other/getAccountInfoSync>。"}, "uni-app 统计": {"scope": "javascript,typescript", "prefix": ["uni.report"], "body": ["uni.report({", "\t${1:eventName}", "\t${2|'',{}|}", "})$0"], "description": "uni-app 统计。更多信息查看 <https://uniapp.dcloud.io/api/other/report>。"}, "uni-app 获取全局唯一版本更新管理器": {"scope": "javascript,typescript", "prefix": ["uni.getUpdateManager"], "body": ["uni.getUpdateManager()$0"], "description": "uni-app 获取全局唯一版本更新管理器。更多信息查看 <https://uniapp.dcloud.io/api/other/update>。"}, "uni-app 设置调试开关": {"scope": "javascript,typescript", "prefix": ["uni.setEnableDebug"], "body": ["uni.setEnabledDebug({", "\tenableDebug: ${1|true,false|}", "\tsuccess: (result) => {$2},", "\tfail: (error) => {$3}", "})"], "description": "uni-app 设置调试开关。更多信息查看 <https://uniapp.dcloud.io/api/other/set-enable-debug>。"}}