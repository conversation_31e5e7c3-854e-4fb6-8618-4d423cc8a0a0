# ERP系统多租户实现改动点清单

## 概述

本文档详细列出了在现有ERP系统中实施多租户功能时需要修改的现有模块和文件。虽然我们的设计目标是低侵入性，但为了确保多租户功能的完整性和系统集成，仍需要对一些关键位置进行必要的修改。

---

## 改动分类

### 🔴 必须修改的文件（核心集成点）
### 🟡 建议修改的文件（功能增强点）  
### 🟢 可选修改的文件（优化提升点）

---

## 1. 项目根模块改动

### 1.1 根POM文件修改 🔴

**文件路径**: `pom.xml`

**修改原因**: 添加新的多租户模块到项目依赖中

**具体修改**:
```xml
<!-- 在<modules>标签中添加 -->
<modules>
    <module>skyeye-foundation</module>
    <module>skyeye-infrastructure</module>
    <module>skyeye-business</module>
    <module>skyeye-frontend</module>
    <!-- 新增：多租户模块 -->
    <module>skyeye-tenant</module>
</modules>
```

**影响范围**: 全局
**风险评估**: 低风险，仅添加模块引用

---

## 2. 业务模块改动

### 2.1 业务聚合POM修改 🔴

**文件路径**: `skyeye-business/pom.xml`

**修改原因**: 将多租户模块纳入业务模块管理

**具体修改**:
```xml
<modules>
    <!-- 现有模块保持不变 -->
    <module>skyeye-project</module>
    <module>skyeye-promote</module>
    <!-- ... 其他现有模块 ... -->
    
    <!-- 新增：多租户模块 -->
    <module>skyeye-tenant</module>
</modules>
```

**影响范围**: 业务模块构建流程
**风险评估**: 低风险

### 2.2 各业务模块POM依赖添加 🟡

**影响文件列表**:
- `skyeye-business/skyeye-erp/erp-web/pom.xml`
- `skyeye-business/skyeye-crm/crm-web/pom.xml`
- `skyeye-business/skyeye-promote/skyeye-userauth/pom.xml`
- `skyeye-business/skyeye-school/school-web/pom.xml`
- `skyeye-business/skyeye-adm/adm-web/pom.xml`
- 以及其他所有业务模块的web子模块

**修改原因**: 使各业务模块能够使用多租户功能

**具体修改**:
```xml
<dependencies>
    <!-- 现有依赖保持不变 -->
    
    <!-- 新增：多租户核心依赖 -->
    <dependency>
        <groupId>com.skyeye</groupId>
        <artifactId>tenant-core</artifactId>
        <version>${project.version}</version>
    </dependency>
</dependencies>
```

**影响范围**: 所有业务模块
**风险评估**: 低风险，仅添加依赖

---

## 3. 基础设施模块改动

### 3.1 Zuul网关过滤器修改 🔴

**文件路径**: `skyeye-infrastructure/skyeye-zuul/src/main/java/com/skyeye/filter/SkyeyeZuulFilter.java`

**修改原因**: 在网关层添加租户信息提取和转发逻辑

**具体修改**:
```java
@Component
public class SkyeyeZuulFilter implements WebFilter {
    
    // 注入租户服务
    @Autowired
    private TenantService tenantService;
    
    @Override
    public Mono<Void> filter(ServerWebExchange exchange, WebFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        ServerHttpResponse response = exchange.getResponse();
        
        // === 新增：租户处理逻辑 ===
        // 1. 提取租户信息
        String tenantId = extractTenantId(request);
        if (StrUtil.isNotBlank(tenantId)) {
            // 2. 验证租户有效性
            if (tenantService.isValidTenant(tenantId)) {
                // 3. 添加租户头信息到下游请求
                ServerHttpRequest modifiedRequest = request.mutate()
                    .header("X-Tenant-Id", tenantId)
                    .build();
                exchange = exchange.mutate().request(modifiedRequest).build();
            } else {
                // 租户无效，返回错误
                response.setStatusCode(HttpStatus.FORBIDDEN);
                return response.setComplete();
            }
        }
        
        // 原有逻辑保持不变
        // ...
        
        return chain.filter(exchange);
    }
    
    // 新增方法：提取租户ID
    private String extractTenantId(ServerHttpRequest request) {
        // 从Header中获取
        String tenantId = request.getHeaders().getFirst("X-Tenant-Id");
        if (StrUtil.isNotBlank(tenantId)) {
            return tenantId;
        }
        
        // 从Token中解析
        String token = request.getHeaders().getFirst("Authorization");
        if (StrUtil.isNotBlank(token)) {
            return parseTenan<span class="cursor">|</span>idFromToken(token);
        }
        
        return null;
    }
    
    // 新增方法：从Token解析租户ID
    private String parseTenantIdFromToken(String token) {
        // 实现JWT Token解析逻辑
        // ...
    }
}
```

**影响范围**: 所有通过网关的请求
**风险评估**: 中风险，需要仔细测试确保不影响现有请求

### 3.2 网关配置文件修改 🟡

**文件路径**: `skyeye-infrastructure/skyeye-zuul/src/main/resources/application.yml`

**修改原因**: 添加多租户相关配置

**具体修改**:
```yaml
# 现有配置保持不变

# 新增：多租户配置
skyeye:
  tenant:
    enabled: true
    default-tenant-id: "10000"
    gateway:
      tenant-header: "X-Tenant-Id"
      token-header: "Authorization"
```

**影响范围**: 网关启动配置
**风险评估**: 低风险

---

## 4. 公共基础模块改动

### 4.1 Web拦截器配置修改 🔴

**文件路径**: `skyeye-foundation/skyeye-common-module/skyeye-base/src/main/java/com/skyeye/common/interceptor/MyWebMvcConfigurerAdapter.java`

**修改原因**: 注册租户拦截器到Spring MVC拦截器链

**具体修改**:
```java
@Configuration
@Order(1)
public class MyWebMvcConfigurerAdapter implements WebMvcConfigurer {
    
    // 注入租户拦截器
    @Autowired
    private TenantInterceptor tenantInterceptor;
    
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 现有拦截器保持不变
        registry.addInterceptor(new HandlerInterceptorMain()).addPathPatterns("/post/**");
        
        // === 新增：租户拦截器 ===
        registry.addInterceptor(tenantInterceptor)
                .addPathPatterns("/post/**")  // 拦截所有业务请求
                .excludePathPatterns("/post/login", "/post/logout"); // 排除登录登出
    }
    
    // 其他方法保持不变
    // ...
}
```

**影响范围**: 所有业务请求
**风险评估**: 中风险，需要确保不影响现有业务流程

### 4.2 主拦截器增强 🟡

**文件路径**: `skyeye-foundation/skyeye-common-module/skyeye-base/src/main/java/com/skyeye/common/interceptor/HandlerInterceptorMain.java`

**修改原因**: 在现有拦截器中添加租户上下文处理

**具体修改**:
```java
@Component
public class HandlerInterceptorMain implements HandlerInterceptor {
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
        throws Exception {
        
        // === 新增：租户上下文检查 ===
        String tenantId = TenantContext.getTenantId();
        if (StrUtil.isNotBlank(tenantId)) {
            // 记录租户访问日志
            logger.debug("当前请求租户ID: {}", tenantId);
        }
        
        // 原有逻辑保持不变
        String sessionKey = request.getParameter("sessionKey");
        // ...
        
        return true;
    }
    
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, 
                              Object handler, Exception ex) throws Exception {
        // === 新增：清理租户上下文 ===
        TenantContext.clear();
        
        // 原有逻辑保持不变
        // ...
    }
}
```

**影响范围**: 所有业务请求
**风险评估**: 低风险，主要是添加功能

---

## 5. 业务服务基类改动

### 5.1 业务服务基类增强 🔴

**文件路径**: `skyeye-foundation/skyeye-common-rest/src/main/java/com/skyeye/base/business/service/impl/SkyeyeBusinessServiceImpl.java`

**修改原因**: 在基础服务类中集成多租户支持

**具体修改**:
```java
public abstract class SkyeyeBusinessServiceImpl<M extends BaseMapper<T>, T> 
    extends ServiceImpl<M, T> implements SkyeyeBusinessService<T> {
    
    // === 新增：租户相关方法 ===
    
    /**
     * 获取当前租户ID
     */
    protected String getCurrentTenantId() {
        return TenantContext.getTenantId();
    }
    
    /**
     * 检查租户权限
     */
    protected void checkTenantPermission(String targetTenantId) {
        String currentTenantId = getCurrentTenantId();
        if (!Objects.equals(currentTenantId, targetTenantId)) {
            throw new TenantException("无权访问其他租户数据");
        }
    }
    
    // === 重写部分方法以支持租户过滤 ===
    
    @Override
    public T selectById(Serializable id) {
        T entity = super.getById(id);
        if (entity != null && entity instanceof TenantAware) {
            // 检查租户权限
            checkTenantPermission(((TenantAware) entity).getTenantId());
        }
        return entity;
    }
    
    @Override
    public boolean save(T entity) {
        // 自动设置租户ID
        if (entity instanceof TenantAware) {
            TenantAware tenantEntity = (TenantAware) entity;
            if (StrUtil.isBlank(tenantEntity.getTenantId())) {
                tenantEntity.setTenantId(getCurrentTenantId());
            }
        }
        return super.save(entity);
    }
    
    // 原有方法保持不变
    // ...
}
```

**影响范围**: 所有继承此基类的业务服务
**风险评估**: 中风险，需要确保不破坏现有业务逻辑

---

## 6. 应用启动类改动

### 6.1 各微服务启动类修改 🟡

**影响文件列表**:
- `skyeye-frontend/skyeye-web-layui/web/src/main/java/com/SkyEyeWebApplication.java`
- `skyeye-business/skyeye-adm/adm-web/src/main/java/com/AdmApplication.java`
- `skyeye-business/skyeye-school/school-web/src/main/java/com/SkySchoolApplication.java`
- `skyeye-business/skyeye-wages/wages-web/src/main/java/com/SkyWagesApplication.java`
- 其他各业务模块的启动类

**修改原因**: 启用多租户自动配置

**具体修改**:
```java
@SpringBootApplication
// === 新增：启用多租户配置 ===
@EnableTenantConfiguration
public class SkyEyeWebApplication {
    
    public static void main(String[] args) {
        SpringApplication.run(SkyEyeWebApplication.class, args);
    }
}
```

**影响范围**: 各微服务启动
**风险评估**: 低风险

---

## 7. 前端模块改动

### 7.1 Session过滤器修改 🟡

**文件路径**: `skyeye-frontend/skyeye-web-layui/web/src/main/java/com/skyeye/common/filter/SessionFilter.java`

**修改原因**: 在前端过滤器中添加租户处理逻辑

**具体修改**:
```java
public class SessionFilter implements Filter {
    
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        
        // === 新增：租户处理逻辑 ===
        String tenantId = httpRequest.getHeader("X-Tenant-Id");
        if (StrUtil.isNotBlank(tenantId)) {
            TenantContext.setTenantId(tenantId);
        }
        
        try {
            // 原有逻辑保持不变
            // ...
            
            chain.doFilter(request, response);
        } finally {
            // === 新增：清理租户上下文 ===
            TenantContext.clear();
        }
    }
}
```

**影响范围**: 前端请求处理
**风险评估**: 低风险

---

## 8. 数据库相关改动

### 8.1 现有业务表结构修改 🔴

**影响范围**: 所有需要租户隔离的业务表

**修改原因**: 为现有表添加租户字段

**具体修改**:
```sql
-- 示例：为用户表添加租户字段
ALTER TABLE sys_user ADD COLUMN tenant_id VARCHAR(32) DEFAULT '10000' COMMENT '租户ID';
CREATE INDEX idx_sys_user_tenant_id ON sys_user(tenant_id);

-- 示例：为角色表添加租户字段  
ALTER TABLE sys_role ADD COLUMN tenant_id VARCHAR(32) DEFAULT '10000' COMMENT '租户ID';
CREATE INDEX idx_sys_role_tenant_id ON sys_role(tenant_id);

-- 类似的修改需要应用到所有业务表
-- 包括但不限于：
-- - ERP相关表 (erp_*)
-- - CRM相关表 (crm_*)  
-- - 学校管理表 (school_*)
-- - 人事管理表 (hr_*)
-- - 等等...
```

**影响范围**: 整个数据库结构
**风险评估**: 高风险，需要详细的数据迁移计划

### 8.2 MyBatis配置修改 🟡

**文件路径**: 各模块的MyBatis配置文件

**修改原因**: 注册SQL拦截器

**具体修改**:
```xml
<!-- 在mybatis-config.xml或对应配置中添加 -->
<configuration>
    <plugins>
        <!-- 新增：租户SQL拦截器 -->
        <plugin interceptor="com.skyeye.tenant.core.interceptor.TenantSqlInterceptor">
            <property name="tenantEnabled" value="true"/>
        </plugin>
    </plugins>
</configuration>
```

**影响范围**: 所有SQL执行
**风险评估**: 中风险，需要充分测试

---

## 9. 配置文件改动

### 9.1 应用配置文件修改 🟡

**影响文件列表**:
- `skyeye-frontend/skyeye-web-layui/web/src/main/resources/application.yml`
- `skyeye-business/*/src/main/resources/application.yml`
- 各微服务的配置文件

**修改原因**: 添加多租户相关配置

**具体修改**:
```yaml
# 现有配置保持不变

# 新增：多租户配置
skyeye:
  tenant:
    enabled: true
    default-tenant-id: "10000"
    sql-interceptor:
      enabled: true
      ignore-tables:
        - sys_menu
        - sys_dict
        - tenant_info
        - tenant_package
    security:
      audit-enabled: true
      monitor-enabled: true
```

**影响范围**: 应用启动和运行时配置
**风险评估**: 低风险

---

## 10. 改动优先级和实施建议

### 10.1 改动优先级

#### 第一优先级（必须修改）🔴
1. 项目POM文件修改
2. Zuul网关过滤器修改
3. Web拦截器配置修改
4. 业务服务基类修改
5. 数据库表结构修改

#### 第二优先级（建议修改）🟡
1. 各业务模块POM依赖添加
2. 主拦截器增强
3. 应用启动类修改
4. 配置文件修改
5. MyBatis配置修改

#### 第三优先级（可选修改）🟢
1. Session过滤器修改
2. 性能监控增强
3. 日志记录优化

### 10.2 实施建议

#### 准备阶段
1. **备份策略**: 对所有需要修改的文件进行版本控制备份
2. **环境准备**: 在开发环境先进行改动验证
3. **依赖检查**: 确保所有新增依赖的版本兼容性

#### 实施阶段
1. **渐进式实施**: 按优先级逐步实施，每个阶段都要充分测试
2. **功能开关**: 在关键位置添加功能开关，便于快速回滚
3. **监控告警**: 在改动点添加监控，及时发现问题

#### 测试阶段
1. **单元测试**: 为所有修改的方法编写单元测试
2. **集成测试**: 测试多租户功能与现有功能的集成
3. **回归测试**: 确保现有功能不受影响

#### 部署阶段
1. **灰度发布**: 先在小范围内验证改动效果
2. **监控观察**: 密切监控系统性能和错误率
3. **快速回滚**: 准备快速回滚方案

---

## 11. 风险评估和缓解措施

### 11.1 主要风险点

#### 高风险
- **数据库结构修改**: 可能导致数据不一致或丢失
- **SQL拦截器**: 可能影响查询性能或结果
- **业务服务基类修改**: 可能影响所有业务功能

#### 中风险  
- **网关过滤器修改**: 可能导致请求路由异常
- **拦截器链修改**: 可能影响请求处理流程

#### 低风险
- **配置文件修改**: 主要是新增配置，影响相对较小
- **启动类修改**: 仅添加注解，风险较低

### 11.2 缓解措施

#### 技术缓解
1. **功能开关**: 为所有多租户功能添加开关控制
2. **兼容模式**: 支持单租户兼容模式运行
3. **性能监控**: 添加性能监控，及时发现问题
4. **错误处理**: 完善错误处理和日志记录

#### 流程缓解
1. **充分测试**: 编写全面的测试用例
2. **代码审查**: 对所有改动进行严格代码审查
3. **分阶段实施**: 分阶段实施，逐步验证
4. **回滚预案**: 准备详细的回滚预案

---

## 12. 总结

虽然多租户架构设计强调低侵入性，但为了确保功能的完整性和系统的一致性，仍需要对现有系统进行一些必要的修改。这些修改主要集中在以下几个方面：

1. **架构集成点**: POM文件、启动类等
2. **请求处理链**: 网关、拦截器等
3. **数据访问层**: 服务基类、SQL拦截等
4. **数据存储层**: 数据库表结构等

通过合理的实施计划和风险控制措施，可以最大限度地降低改动风险，确保多租户功能的顺利实施。建议采用渐进式实施策略，先完成核心改动并充分测试，再逐步完善其他功能。

**关键成功因素**:
- 充分的测试覆盖
- 完善的监控体系  
- 详细的回滚预案
- 渐进式的实施策略