<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="../../assets/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="../../assets/lib/winui/css/winui.css" rel="stylesheet" />
</head>
<body>
    <div style="margin: 0 auto; padding: 20px;">
        <form class="layui-form" action="" id="showForm" autocomplete="off">

        </form>
    </div>

    <script type="text/x-handlebars-template" id="beanTemplate">
        {{#bean}}
        <div class="layui-form-item layui-col-xs12">
            <span class="hr-title">基本信息</span>
            <hr>
        </div>
        <div class="layui-form-item layui-col-xs6">
            <label class="layui-form-label">服务名称：</label>
            <div class="layui-input-block ver-center">
                {{serviceBean.name}}
            </div>
        </div>
        <div class="layui-form-item layui-col-xs6">
            <label class="layui-form-label">分组：</label>
            <div class="layui-input-block ver-center">
                {{serviceBean.groupName}}
            </div>
        </div>
        <div class="layui-form-item layui-col-xs12">
            <label class="layui-form-label">服务编码：</label>
            <div class="layui-input-block ver-center">
                {{serviceBean.className}}
            </div>
        </div>
        <div class="layui-form-item layui-col-xs12">
            <label class="layui-form-label">对象编码：</label>
            <div class="layui-input-block ver-center">
                {{serviceBean.entityClassName}}
            </div>
        </div>
        <div class="layui-form-item layui-col-xs6">
            <label class="layui-form-label">所属应用：</label>
            <div class="layui-input-block ver-center">
                {{serviceBean.appId}}
            </div>
        </div>
        <div class="layui-form-item layui-col-xs6">
            <label class="layui-form-label">租户类型：</label>
            <div class="layui-input-block ver-center">
                {{serviceBean.tenantName}}
            </div>
        </div>
        <div class="layui-form-item layui-col-xs6">
            <label class="layui-form-label">是否开启工作流：</label>
            <div class="layui-input-block ver-center">
                {{serviceBean.flowableName}}
            </div>
        </div>
        <div class="layui-form-item layui-col-xs6">
            <label class="layui-form-label">是否开启团队权限：</label>
            <div class="layui-input-block ver-center">
                {{serviceBean.teamAuthName}}
            </div>
        </div>
        <div class="layui-form-item layui-col-xs12">
            <span class="hr-title">高级信息</span>
            <button id="editBean" type="button" class="winui-toolbtn search-table-btn-right"><i class="fa fa-edit" aria-hidden="true"></i>编辑</button>
            <hr>
        </div>
        <div class="layui-form-item layui-col-xs6">
            <label class="layui-form-label">编码：</label>
            <div class="layui-input-block ver-center">
                {{codeRule.name}}
            </div>
        </div>
        {{/bean}}
    </script>

    <script src="../../assets/lib/layui/layui.js"></script>
    <script src="../../assets/lib/layui/custom.js"></script>
    <script type="text/javascript">
        layui.config({base: '../../js/classServer/'}).use('classServerDetails');
    </script>
</body>
</html>