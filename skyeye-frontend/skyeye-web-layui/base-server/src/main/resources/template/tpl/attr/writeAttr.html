<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="../../assets/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="../../assets/lib/winui/css/winui.css" rel="stylesheet" />
</head>
<body>
	<div style="margin: 0 auto; padding: 20px;">
	    <form class="layui-form" action="" id="showForm" autocomplete="off">

	    </form>
	</div>

	<script type="text/x-handlebars-template" id="beanTemplate">
		{{#bean}}
		<div class="layui-form-item layui-col-xs12">
			<span class="hr-title">基本信息</span><hr>
		</div>
		<div class="layui-form-item layui-col-xs6">
			<label class="layui-form-label">属性：</label>
			<div class="layui-input-block ver-center">
				{{attrKey}}
			</div>
		</div>
		<div class="layui-form-item layui-col-xs6">
			<label class="layui-form-label">名称<i class="red">*</i></label>
			<div class="layui-input-block">
				<input type="text" id="name" name="name" win-verify="required" placeholder="请输入名称" class="layui-input" maxlength="100" value="{{name}}"/>
			</div>
		</div>
		<div class="inputParams">
			<div class="layui-form-item layui-col-xs6">
				<label class="layui-form-label">组件</label>
				<div class="layui-input-block">
					<input type="text" id="componentId" name="componentId" placeholder="请选择组件" class="layui-input" readonly="readonly" componentId=""/>
				</div>
			</div>
			<div class="layui-hide" id="linkDataBox">
				<div class="layui-form-item layui-col-xs12">
					<label class="layui-form-label">数据来源<i class="red">*</i></label>
					<div class="layui-input-block">
						<select id="dataType" name="dataType" lay-search="" lay-filter="dataType">

						</select>
					</div>
				</div>
				<div id="dataTypeObjectBox">

				</div>
			</div>
			<div class="layui-form-item layui-col-xs6">
				<label class="layui-form-label">最小长度</label>
				<div class="layui-input-block">
					<input type="text" id="minLength" name="minLength" win-verify="number" placeholder="请输入最小长度" class="layui-input" value="{{minLength}}"/>
				</div>
			</div>
			<div class="layui-form-item layui-col-xs6">
				<label class="layui-form-label">最大长度</label>
				<div class="layui-input-block">
					<input type="text" id="maxLength" name="maxLength" win-verify="number" placeholder="请输入最大长度" class="layui-input" value="{{maxLength}}"/>
				</div>
			</div>
		</div>
		<div class="layui-form-item layui-col-xs12">
			<label class="layui-form-label">备注</label>
			<div class="layui-input-block">
				<textarea id="remark" name="remark" placeholder="请输入备注" class="layui-textarea" style="height: 100px;" maxlength="200">{{remark}}</textarea>
			</div>
		</div>

		<div class="layui-form-item layui-col-xs12">
			<div class="layui-input-block">
				<button class="winui-btn" id="cancle"><language showName="com.skyeye.cancel"></language></button>
				<button class="winui-btn" lay-submit lay-filter="formEditBean"><language showName="com.skyeye.save"></language></button>
			</div>
		</div>
		{{/bean}}
	</script>

	<script src="../../assets/lib/layui/layui.js"></script>
   	<script src="../../assets/lib/layui/custom.js"></script>
   	<script type="text/javascript">
		layui.config({base: '../../js/attr/'}).use('writeAttr');
	</script>
</body>
</html>