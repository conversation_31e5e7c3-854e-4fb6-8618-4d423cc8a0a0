<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="../../assets/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="../../assets/lib/winui/css/winui.css" rel="stylesheet" />
</head>
<body>
	<div style="padding:20px; margin:0 auto;">
	    <form class="layui-form" action="" id="showForm" autocomplete="off">
	        
	    </form>
	</div>

	<script type="text/x-handlebars-template" id="showTemplate">
		{{#bean}}
		<div class="layui-form-item layui-col-xs12" id="teamAuthEdit" style="text-align: right;">
			<a class="layui-btn layui-btn-sm layui-btn-normal" id="edit"><language showName="com.skyeye.editBtn"></language></a>
		</div>
		<div class="layui-form-item layui-col-xs12">
			<span class="hr-title">基本信息</span><hr>
		</div>
		<div class="layui-form-item layui-col-xs6">
			<label class="layui-form-label">团队经理：</label>
			<div class="layui-input-block ver-center">
				{{chargeUserMation.name}}
			</div>
		</div>
		<div class="layui-form-item layui-col-xs12">
			<span class="hr-title">成员信息</span><hr>
		</div>
		<div class="layui-form-item layui-col-xs12">
			<label class="layui-form-label">成员信息：</label>
			<div class="layui-input-block">
				<div class="winui-tip alert-info" id="showInfo">一个用户在一个团队中只能属于一个角色。</div>
				<div style="margin:auto 10px;">
					<table id="messageTable" lay-filter="messageTable"></table>
				</div>
			</div>
		</div>

		<div class="layui-form-item layui-col-xs12">
			<span class="hr-title">权限信息</span><hr>
		</div>
		<div class="layui-form-item layui-col-xs12" id="authList">

		</div>
		{{/bean}}
	</script>

	<script type="text/x-handlebars-template" id="authTableTemplate">
		{{#list}}
		<div class="layui-form-item layui-col-xs12">
			<label class="layui-form-label">{{title}}：</label>
			<div class="layui-input-block">
				<table id="{{id}}" lay-filter="{{id}}"></table>
			</div>
		</div>
		{{/list}}
	</script>

	<script src="../../assets/lib/layui/layui.js"></script>
   	<script src="../../assets/lib/layui/custom.js"></script>
   	<script type="text/javascript">
        layui.config({base: '../../js/teamBusiness/'}).use('teamBusinessDetails');
    </script>
</body>
</html>