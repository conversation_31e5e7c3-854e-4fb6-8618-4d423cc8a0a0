<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="../../assets/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="../../assets/lib/winui/css/winui.css" rel="stylesheet" />
</head>
<body>
	<div style="padding:20px; margin:0 auto;">
		<div class="layui-col-xs12">
   			<span class="hr-title">讨论板信息</span><hr>
    	</div>
    	<div class="layui-col-xs12">
    		<div class="disheader">
    		</div>
    		<div class="disdesc">
    		</div>
    		<div class="discontent">
    		</div>
    	</div>
    	<div class="layui-col-xs12">
   			<span class="hr-title">回复信息</span><hr>
    	</div>
    	<div class="layui-col-xs12">
    		<div class="disreplay" id="disreplay">
    			
    		</div>
    	</div>
    	<div class="layui-col-xs12">
   			<span class="hr-title">我要回复</span><hr>
    	</div>
    	<form class="layui-form" action="" id="showForm" autocomplete="off">
		    <div class="layui-form-item layui-col-xs12">
		        <label class="layui-form-label">内容<i class="red">*</i></label>
		        <div class="layui-input-block">
					<textarea id="content" name="content" placeholder="请输入内容" win-verify="required" class="layui-textarea"></textarea>
				</div>
		    </div>
	        <div class="layui-form-item layui-col-xs12">
	            <div class="layui-input-block">
	                <button class="winui-btn" id="cancle">关闭</button>
	                <button class="winui-btn" lay-submit lay-filter="formAddBean">提交</button>
	            </div>
	        </div>
	    </form>
	</div>
	
	<script type="text/x-handlebars-template" id="replyTemplate">
		{{#each rows}}
			<div class="disreplay-left">
    			<div class="user-info">
    				<div class="user-photo">
    					<img alt="" src="{{createMation.userPhoto}}">
    				</div>
    				<div class="user-name">
   						{{createName}}
   					</div>
   				</div>
   				<div class="replay-content">
					<div class="replay-time">
						回复时间：{{createTime}}
					</div>
    				{{name}}
    			</div>
    			<div class="equal-user-photo"></div>
    		</div>
			{{#each child}}
				<div class="disreplay-right">
					<div class="equal-user-photo"></div>
					<div class="replay-content">
						<div class="replay-time">
							回复时间：{{createTime}}
						</div>
						{{name}}
					</div>
					<div class="user-info">
						<div class="user-photo">
							<img alt="" src="{{createMation.userPhoto}}">
						</div>
						<div class="user-name">
							{{createName}}
						</div>
					</div>
				</div>
			{{/each}}
		{{/each}}
	</script>
	
	<script src="../../assets/lib/layui/layui.js"></script>
   	<script src="../../assets/lib/layui/custom.js"></script>
   	<script type="text/javascript">
        layui.config({base: '../../js/discuss/'}).use('discussDetail');
    </script>
</body>
</html>