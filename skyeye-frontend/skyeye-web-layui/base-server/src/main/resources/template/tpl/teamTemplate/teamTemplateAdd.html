<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="../../assets/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="../../assets/lib/winui/css/winui.css" rel="stylesheet" />
</head>
<body>
	<div style="margin:0 auto;padding:20px;">
	    <form class="layui-form" action="" id="showForm" autocomplete="off">
			<div class="layui-form-item layui-col-xs12">
				<span class="hr-title">基本信息</span><hr>
			</div>
	        <div class="layui-form-item layui-col-xs6">
		        <label class="layui-form-label">团队名称<i class="red">*</i></label>
		        <div class="layui-input-block">
		        	<input type="text" id="name" name="name" win-verify="required" placeholder="请输入团队名称" class="layui-input"/>
					<div class="layui-form-mid layui-word-aux">团队名称不能重复。</div>
		        </div>
		    </div>
			<div class="layui-form-item layui-col-xs6">
				<label class="layui-form-label">团队经理<i class="red">*</i></label>
				<div class="layui-input-block">
					<input type="text" id="chargeUser" name="chargeUser" win-verify="required" placeholder="请选择团队经理" class="layui-input" readonly="readonly"/>
					<i class="fa fa-user-plus input-icon" id="chargeUserSelPeople"></i>
					<div class="layui-form-mid layui-word-aux">团队经理拥有所有的权限。</div>
				</div>
			</div>
			<div class="layui-form-item layui-col-xs6">
				<label class="layui-form-label">适用对象<i class="red">*</i></label>
				<div class="layui-input-block">
					<select lay-filter="objectType" lay-search="" id="objectType" win-verify="required">

					</select>
				</div>
			</div>
			<div class="layui-form-item layui-col-xs12">
				<label class="layui-form-label">状态<i class="red">*</i></label>
				<div class="layui-input-block winui-radio" id="enabled">
				</div>
			</div>
			<div class="layui-form-item layui-col-xs12">
				<label class="layui-form-label">备注</label>
				<div class="layui-input-block">
					<textarea id="remark" name="remark" placeholder="请输入备注" class="layui-textarea" style="height: 100px;" maxlength="200"></textarea>
				</div>
			</div>
			<div class="layui-form-item layui-col-xs12">
				<span class="hr-title">成员信息</span><hr>
			</div>
			<div class="layui-form-item layui-col-xs12">
				<label class="layui-form-label">成员信息：</label>
				<div class="layui-input-block">
					<div class="winui-tip alert-info" id="showInfo">一个用户在一个团队中只能属于一个角色。</div>
					<div class="winui-toolbar">
						<div class="winui-tool" style="text-align: left;">
							<button id="addRole" class="winui-toolbtn" type="button"><i class="fa fa-plus" aria-hidden="true"></i>添加角色</button>
						</div>
					</div>
					<div style="margin:auto 10px;">
						<table id="messageTable" lay-filter="messageTable"></table>
					</div>
				</div>
			</div>

			<div class="layui-form-item layui-col-xs12">
				<span class="hr-title">权限信息</span><hr>
			</div>
			<div class="layui-form-item layui-col-xs12" id="authList">

			</div>
		    <div class="layui-form-item layui-col-xs12">
		        <div class="layui-input-block">
		            <button class="winui-btn" id="cancle"><language showName="com.skyeye.cancel"></language></button>
		            <button class="winui-btn" lay-submit lay-filter="formAddBean"><language showName="com.skyeye.save"></language></button>
		        </div>
		    </div>
	    </form>
	</div>

	<script type="text/html" id="tableBar">
		{{# if(d.pId == '0'){ }}
			<a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="addUser">添加成员</a>
			<a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="removeRole">移除</a>
		{{# } else { }}
			<a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="removeUser">移除</a>
		{{# } }}
	</script>

	<script type="text/x-handlebars-template" id="authTableTemplate">
		{{#list}}
		<div class="layui-form-item layui-col-xs12">
			<label class="layui-form-label">{{title}}<i class="red">*</i></label>
			<div class="layui-input-block">
				<table id="{{id}}" lay-filter="{{id}}"></table>
			</div>
		</div>
		{{/list}}
	</script>

	<script src="../../assets/lib/layui/layui.js"></script>
   	<script src="../../assets/lib/layui/custom.js"></script>
   	<script type="text/javascript">
		layui.config({base: '../../js/teamTemplate/'}).use('teamTemplateAdd');
	</script>
</body>
</html>