<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="../../assets/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="../../assets/lib/winui/css/winui.css" rel="stylesheet" />
</head>
<body>
    <div style="margin: 0 auto; padding: 20px;">
        <form class="layui-form" action="" id="showForm" autocomplete="off">

        </form>
    </div>

    <script type="text/x-handlebars-template" id="beanTemplate">
        {{#bean}}
        <div class="layui-form-item layui-col-xs12">
            <span class="hr-title">高级信息</span>
            <hr>
        </div>
        <div class="layui-form-item layui-col-xs6">
            <label class="layui-form-label">编码<i class="red">*</i></label>
            <div class="layui-input-block">
                <select lay-filter="codeRuleId" lay-search="" win-verify="required" id="codeRuleId" name="codeRuleId">
                </select>
            </div>
        </div>
        <div class="layui-form-item layui-col-xs12">
            <div class="layui-input-block">
                <button class="winui-btn" id="cancle"><language showName="com.skyeye.cancel"></language></button>
                <button class="winui-btn" lay-submit lay-filter="formEditBean"><language showName="com.skyeye.save"></language></button>
            </div>
        </div>
        {{/bean}}
    </script>

    <script src="../../assets/lib/layui/layui.js"></script>
    <script src="../../assets/lib/layui/custom.js"></script>
    <script type="text/javascript">
        layui.config({base: '../../js/classServer/'}).use('classServerEdit');
    </script>
</body>
</html>