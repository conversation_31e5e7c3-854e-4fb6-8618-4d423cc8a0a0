<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="../../assets/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="../../assets/lib/winui/css/winui.css" rel="stylesheet" />
</head>
<body>
	<div style="padding:20px; margin:0 auto;">
	    <form class="layui-form" action="" id="showForm">
			<div class="layui-form-item layui-col-xs12">
				<span class="hr-title">基本信息</span><hr>
			</div>
		    <div class="layui-form-item layui-col-xs12">
		        <label class="layui-form-label">名称<i class="red">*</i></label>
		        <div class="layui-input-block">
		            <input type="text" id="name" name="name" win-verify="required" placeholder="请输入名称" class="layui-input" maxlength="200"/>
		        </div>
		    </div>
		    <div class="layui-form-item layui-col-xs6">
		        <label class="layui-form-label">展示位置<i class="red">*</i></label>
		        <div class="layui-input-block">
					<select id="position" name="position" lay-filter="position" win-verify="required">

					</select>
		        </div>
		    </div>
			<div id="positionChangeBox">

			</div>
		    <div class="layui-form-item layui-col-xs6">
		        <label class="layui-form-label">权限编号</label>
		        <div class="layui-input-block">
		            <input type="text" id="authPointNum" name="authPointNum" placeholder="请输入权限编号" class="layui-input" maxlength="100"/>
		        </div>
		    </div>
		    <div class="layui-form-item layui-col-xs6">
		        <label class="layui-form-label">事件类型<i class="red">*</i></label>
		        <div class="layui-input-block">
					<select id="eventType" name="eventType" lay-filter="eventType" win-verify="required">

					</select>
		        </div>
		    </div>
			<div id="attrSymbolsDesignBox">

			</div>
			<div class="layui-form-item layui-col-xs6">
				<label class="layui-form-label">排序<i class="red">*</i></label>
				<div class="layui-input-block">
					<input type="text" id="orderBy" name="orderBy" win-verify="required|number" placeholder="请输入排序序号" class="layui-input"/>
				</div>
			</div>
			<div class="layui-form-item layui-col-xs12">
				<label class="layui-form-label">备注</label>
				<div class="layui-input-block">
					<textarea id="remark" name="remark" placeholder="请输入备注" class="layui-textarea" style="height: 100px;"></textarea>
				</div>
			</div>
			<div id="eventTypeChangeBox">

			</div>
		    <div class="layui-form-item layui-col-xs12">
		        <div class="layui-input-block">
		            <button class="winui-btn" id="cancle"><language showName="com.skyeye.cancel"></language></button>
		            <button class="winui-btn" lay-submit lay-filter="formWriteBean"><language showName="com.skyeye.save"></language></button>
		        </div>
		    </div>
	    </form>
	</div>
	<script src="../../assets/lib/layui/layui.js"></script>
   	<script src="../../assets/lib/layui/custom.js"></script>
   	<script type="text/javascript">
        layui.config({base: '../../js/operate/'}).use('writeOperate');
    </script>
</body>
</html>