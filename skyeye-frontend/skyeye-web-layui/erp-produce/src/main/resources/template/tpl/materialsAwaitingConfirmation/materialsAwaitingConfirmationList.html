<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <title></title>
    <link href="../../assets/lib/layui/css/layui.css" rel="stylesheet"/>
    <link href="../../assets/lib/winui/css/winui.css" rel="stylesheet"/>
</head>
<body>
<div class="winui-toolbar">
    <div class="winui-tool">
        <button id="reloadTable" class="winui-toolbtn search-table-btn-right"><i class="fa fa-refresh" aria-hidden="true"></i>
            <language showName="com.skyeye.refreshDataBtn"></language>
        </button>
    </div>
</div>
<div style="margin:auto 10px;">
    <table id="messageTable" lay-filter="messageTable"></table>
    <script type="text/html" id="tableBar">
        {{# if (d.state == 'pass') { }}
            {{# if (d.otherState == 1 ||d.otherState == 2) { }}
                {{# if(auth('1721046594407')) { }}
                    <a class="layui-btn layui-btn-xs" lay-event="materialReceipt">物料接收</a>
                {{# } }}
                {{# if(auth('1721046610103')) { }}
                    <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="materialReturn">物料退货</a>
                {{# } }}
            {{# } }}
        {{# } }}
    </script>
</div>
<script src="../../assets/lib/layui/layui.js"></script>
<script src="../../assets/lib/layui/custom.js"></script>
<script type="text/javascript">
    layui.config({base: '../../js/materialsAwaitingConfirmation/'}).use('materialsAwaitingConfirmationList');
</script>
</body>
</html>