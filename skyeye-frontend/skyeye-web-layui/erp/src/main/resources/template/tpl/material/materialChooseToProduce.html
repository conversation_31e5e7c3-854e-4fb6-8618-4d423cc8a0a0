<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="../../assets/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="../../assets/lib/winui/css/winui.css" rel="stylesheet" />
    <link href="../../assets/lib/layui/lay/modules/jqueryStep/css/jquery.step.css" rel="stylesheet" />
    <style>
		.layui-input{
			height:30px;
		}
		.layui-table-cell {
			height: auto;
		    text-overflow: inherit;
		    overflow: visible;
		    white-space: normal;
		    word-wrap: break-word;
		}
	</style>
</head>
<body>
	<div id="step"></div>
	<div id="firstTab">
	    <div class="winui-toolbar">
	        <div class="winui-tool">
	            <button id="reloadTable" class="winui-toolbtn search-table-btn-right"><i class="fa fa-refresh" aria-hidden="true"></i><language showName="com.skyeye.refreshDataBtn"></language></button>
	        </div>
	    </div>
	    <div style="margin:auto 10px;">
	        <div style="width: 200px; float: left;" id="materialCategoryType">

	    	</div>
	    	<div style="width: calc(100% - 200px); float: left;">
		    	<div style="margin:auto 10px;">
			        <table id="messageTable" lay-filter="messageTable"></table>
			    </div>
	    	</div>
	    </div>
	    <button class="layui-btn layui-btn-sm layui-btn-normal" type="button" id="nextTab" style="float: right;">
        	下一步
        </button>
    </div>
    
    <div id="secondTab" style="display: none;">
    	<form class="layui-form" action="" id="showForm" autocomplete="off">
	    	<table class="layui-table" style="margin: 0px 10px; width: calc(100% - 20px);">
	    		<thead>
	    			<tr>
	    				<th style="width: 100px">商品名称</th>
	    				<th>型号</th>
	    				<th>商品来源<i class="red">*</i></th>
						<th>规格选择<i class="red">*</i></th>
						<th>BOM方案选择</th>
	    			</tr>
	    		</thead>
	        	<tbody id="tBody">
	            	
	            </tbody>
			</table>
		</form>
    	<button class="layui-btn layui-btn-sm layui-btn-normal" type="button" id="prevTab" style="float: right;">
        	上一步
        </button>
        <button class="layui-btn layui-btn-sm layui-btn-normal" type="button" id="saveChoose" style="float: right;">
        	<i class="fa fa-save" aria-hidden="true" style="margin-right: 5px"></i>保存
        </button>
    </div>
    
    <script type="text/html" id="tableBody">
		{{#each rows}}
			<tr rowid="{{id}}">
               	<td id="name{{id}}">{{name}}</td>
               	<td id="model{{id}}">{{model}}</td>
               	<td>
					<select lay-filter="typeId" lay-search="" id="type{{id}}" disabled>
						<option value="1">自产</option>
						<option value="2">外购</option>
					</select>
				</td>
				<td>
					<select lay-filter="unitListChoose" lay-filter="unitListChoose" win-verify="required" lay-search="" id="norms{{id}}">
						<option value="">请选择</option>
						{{#each materialNorms}}
						<option value="{{id}}">{{name}}</option>
						{{/each}}
					</select>
				</td>
               	<td>
					<select lay-filter="bomListChoose" lay-search="" id="bom{{id}}">
					</select>
				</td>
            </tr>
		{{/each}}
    </script>
    
    <script src="../../assets/lib/layui/layui.js"></script>
    <script src="../../assets/lib/layui/custom.js"></script>
	<script type="text/javascript" src="../../assets/lib/layui/lay/modules/jquery-min.js"></script>
    <script type="text/javascript" src="../../assets/lib/layui/lay/modules/jqueryStep/js/jquery.step.min.js"></script>
    <script type="text/javascript">
        layui.config({base: '../../js/material/'}).use('materialChooseToProduce');
    </script>
</body>
</html>