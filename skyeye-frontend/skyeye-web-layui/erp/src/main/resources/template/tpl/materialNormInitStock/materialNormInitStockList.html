<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="../../assets/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="../../assets/lib/winui/css/winui.css" rel="stylesheet" />
</head>
<body>
    <div class="winui-toolbar">
        <div class="winui-tool">
            <button id="addBean" class="winui-toolbtn"><i class="fa fa-plus" aria-hidden="true"></i><language showName="com.skyeye.addBtn"></language></button>
        </div>
    </div>
    <div style="margin:auto 10px;">
    	<form class="layui-form" action="" id="showForm" autocomplete="off">
	        <table id="messageTable" lay-filter="messageTable"></table>
	        <script type="text/html" id="tableBar">
            	<a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="edit"><language showName="com.skyeye.editBtn"></language></a>
            	<a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="delet"><language showName="com.skyeye.deleteBtn"></language></a>
        	</script>
		    <div class="layui-form-item layui-col-xs12">
		        <div class="layui-input-block">
		            <button class="winui-btn" type="button" id="cancle"><language showName="com.skyeye.cancel"></language></button>
		            <button class="winui-btn" lay-submit lay-filter="formAddBean"><language showName="com.skyeye.save"></language></button>
		        </div>
			</div>
		</form>
    </div>
    <script src="../../assets/lib/layui/layui.js"></script>
    <script src="../../assets/lib/layui/custom.js"></script>
    <script type="text/javascript">
        layui.config({base: '../../js/materialNormInitStock/'}).use('materialNormInitStockList');
    </script>
</body>
</html>