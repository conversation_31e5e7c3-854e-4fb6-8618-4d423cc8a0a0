<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="../../assets/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="../../assets/lib/winui/css/winui.css" rel="stylesheet" />
</head>
<body>
	<div class="winui-toolbar">
		<div class="winui-tool">
			<button id="reloadTable" class="winui-toolbtn search-table-btn-right"><i class="fa fa-refresh" aria-hidden="true"></i><language showName="com.skyeye.refreshDataBtn"></language></button>
			<button id="addBean" class="winui-toolbtn search-table-btn-right" auth="1571812723211"><i class="fa fa-plus" aria-hidden="true"></i><language showName="com.skyeye.addBtn"></language></button>
		</div>
	</div>
	<div style="margin:auto 10px;">
	    <table id="messageTable" lay-filter="messageTable"></table>
		<script type="text/html" id="tableBar">
			{{# if (d.editRow == 1) { }}
				{{# if (auth('1571812753921')) { }}
					<a class="layui-btn layui-btn-xs" lay-event="subApproval">提交审批</a>
				{{# } }}
				{{# if (auth('1571812723211')) { }}
					<a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="edit"><language showName="com.skyeye.editBtn"></language></a>
				{{# } }}
				{{# if (auth('1571812733324')) { }}
					<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="delete"><language showName="com.skyeye.deleteBtn"></language></a>
				{{# } }}
			{{# } }}
			{{# if (d.editRow == 2) { }}
				{{# if (auth('1629386145750')) { }}
					<a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="revoke">撤销</a>
				{{# } }}
			{{# } }}
			{{# if (d.state == 'pass' || d.state == 'partiallyCompleted') { }}
				{{# if (d.otherState == 1) { }}
					{{# if (auth('1571812784641')) { }}
						<a class="layui-btn layui-btn-xs" lay-event="turnPurchase">转采购入库</a>
					{{# } }}
				{{# } }}
				{{# if (d.otherState == 1) { }}
					{{# if (auth('1721305185714')) { }}
						<a class="layui-btn layui-btn-xs" lay-event="purchaseOrderToReturn">转采购退货</a>
					{{# } }}
				{{# } }}
				{{# if (d.otherState == 2 || d.otherState == 3) { }}
					{{# if (auth('1717984916030')) { }}
						<a class="layui-btn layui-btn-xs" lay-event="transferToWaybill">转到货单</a>
					{{# } }}
				{{# } }}
			{{# } }}
		</script>
	</div>
	<script src="../../assets/lib/layui/layui.js"></script>
	<script src="../../assets/lib/layui/custom.js"></script>
	<script type="text/javascript">
	    layui.config({base: '../../js/purchaseOrder/'}).use('purchaseOrderList');
	</script>
</body>
</html>