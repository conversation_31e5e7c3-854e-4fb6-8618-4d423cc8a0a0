<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="../../assets/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="../../assets/lib/winui/css/winui.css" rel="stylesheet" />
</head>
<body style="background-color: rgb(242, 242, 242);">
	<div class="layui-fluid">
		<form class="layui-form" action="">
			<div class="layui-col-xs12 margin-top-10 layui-col-space15">
				
				<div class="layui-col-sm6 layui-col-md3">
					<div class="layui-card">
			        	<div class="layui-card-header">
			          		本月累计销售
			          		<span class="layui-badge layui-bg-blue header-right-top">月</span>
			        	</div>
			        	<div class="layui-card-body layuiadmin-card-list">
				          	<p class="layuiadmin-big-font" id="salesMoney">0.00</p>
				          	<p>当前月已审核通过的销售订单金额</p>
			        	</div>
			      	</div>
				</div>
				
				<div class="layui-col-sm6 layui-col-md3">
					<div class="layui-card">
			        	<div class="layui-card-header">
			          		本月累计零售
			          		<span class="layui-badge layui-bg-green header-right-top">月</span>
			        	</div>
			        	<div class="layui-card-body layuiadmin-card-list">
				          	<p class="layuiadmin-big-font" id="retailMoney">0.00</p>
				          	<p>当前月已审核通过的零售订单金额</p>
			        	</div>
			      	</div>
				</div>
				
				<div class="layui-col-sm6 layui-col-md3">
					<div class="layui-card">
			        	<div class="layui-card-header">
			          		本月累计采购
			          		<span class="layui-badge layui-bg-orange header-right-top">月</span>
			        	</div>
			        	<div class="layui-card-body layuiadmin-card-list">
				          	<p class="layuiadmin-big-font" id="purchaseMoney">0.00</p>
				          	<p>当前月已审核通过的采购订单金额</p>
			        	</div>
			      	</div>
				</div>
				
				<div class="layui-col-sm6 layui-col-md3">
					<div class="layui-card">
			        	<div class="layui-card-header">
			          		本月利润（已审核通过）
			          		<span class="layui-badge header-right-top">月</span>
			        	</div>
			        	<div class="layui-card-body layuiadmin-card-list">
				          	<p class="layuiadmin-big-font" id="profitMoney">0.00</p>
				          	<p>零售订单金额 + 销售订单金额 - 采购订单金额</p>
			        	</div>
			      	</div>
				</div>
				
		      	<div class="layui-col-xs6">
		      		<div class="layui-card">
						<div class="layui-card-header">采购统计</div>
						<div class="layui-card-body">
							<div id="layer04_chart1" class="layui-row" style="height: 400px;">
							
							</div>
						</div>
					</div>
				</div>
				
				<div class="layui-col-xs6">
		      		<div class="layui-card">
				      	<div class="layui-card-header">销售统计</div>
						<div class="layui-card-body">
							<div id="layer04_chart2" class="layui-row" style="height: 400px;">
							
							</div>
						</div>
					</div>
				</div>
				
				<div class="layui-col-xs12">
		      		<div class="layui-card">
				      	<div class="layui-card-header">利润统计</div>
						<div class="layui-card-body">
							<div id="layer04_chart3" class="layui-row" style="height: 400px;">
							
							</div>
						</div>
					</div>
				</div>
				
			</div>
		</form>
	</div>
	
    <script src="../../assets/lib/layui/layui.js"></script>
    <script src="../../assets/lib/layui/custom.js"></script>
    <script type="text/javascript">
        layui.config({base: '../../js/erpPage/'}).use('erpPage');
    </script>
</body>
</html>