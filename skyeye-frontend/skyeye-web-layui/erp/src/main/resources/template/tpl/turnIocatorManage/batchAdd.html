<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <link href="../../assets/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="../../assets/lib/winui/css/winui.css" rel="stylesheet" />
</head>
<body>
<div style="margin:0 auto;padding:20px;">
    <form class="layui-form" action="" autocomplete="off">
        <div class="layui-form-item layui-col-xs12">
            <span class="hr-title">基本信息</span><hr>
        </div>

        <div class="layui-form-item layui-col-xs6">
            <label class="layui-form-label">类型<i class="red">*</i></label>
                <div class="layui-input-block winui-radio" id="type">
                </div>
        </div>
        <div class="layui-form-item layui-col-xs6">
            <label class="layui-form-label">数量<i class="red">*</i></label>
            <div class="layui-input-block">
                <input type="text" id="number" name="number" win-verify="required|number" placeholder="请输入批量生成数量" class="layui-input"/>
            </div>
        </div>

        <div class="layui-form-item layui-col-xs12">
            <div class="layui-input-block">
                <button class="winui-btn" id="cancle"><language showName="com.skyeye.cancel"></language></button>
                <button class="winui-btn" lay-submit lay-filter="getBean">批量生成</button>
            </div>
        </div>
    </form>
</div>

<script src="../../assets/lib/layui/layui.js"></script>
<script src="../../assets/lib/layui/custom.js"></script>
<script type="text/javascript">
    layui.config({base : '../../js/turnIocatorManage/'}).use('batchAdd');
</script>
</body>
</html>