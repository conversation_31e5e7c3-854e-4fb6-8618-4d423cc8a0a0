<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="../../assets/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="../../assets/lib/winui/css/winui.css" rel="stylesheet" />
</head>
<body style="background-color: rgb(242, 242, 242);">
<div class="layui-fluid">
    <form class="layui-form" action="" id="showForm">
        <div class="layui-col-xs12 margin-top-10 layui-col-space15">
            <div class="layui-col-xs3">
                <div class="layui-card">
                    <div class="layui-card-header">仓库</div>
                    <div class="layui-card-body">
                        <table id="warehouseTable" lay-filter="warehouseTable"></table>
                    </div>
                </div>
            </div>

            <div class="layui-col-xs9">
                <div class="layui-card">
                    <div class="layui-card-header">员工</div>
                    <div class="layui-card-body">
                        <div class="winui-toolbar">
                            <div class="winui-tool">
                                <button id="addBean" class="winui-toolbtn" auth="1721305232197" type="button"><i class="fa fa-plus" aria-hidden="true"></i><language showName="com.skyeye.addBtn"></language></button>
                            </div>
                        </div>
                        <div style="margin:auto 10px;">
                            <table id="messageTable" lay-filter="messageTable"></table>

                            <script type="text/html" id="tableBar">
                                {{# if(auth('1721305310416')){ }}
                                <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="delete"><language showName="com.skyeye.deleteBtn"></language></a>
                                {{# } }}
                            </script>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<script src="../../assets/lib/layui/layui.js"></script>
<script src="../../assets/lib/layui/custom.js"></script>
<script type="text/javascript">
    layui.config({base: '../../js/warehousePersonnel/'}).use('warehousePersonnel');
</script>
</body>
</html>