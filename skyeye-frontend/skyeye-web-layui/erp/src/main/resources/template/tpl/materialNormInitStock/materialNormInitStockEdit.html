<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="../../assets/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="../../assets/lib/winui/css/winui.css" rel="stylesheet" />
</head>
<body>
	<div style="margin:0 auto;padding:20px;">
	    <form class="layui-form" action="" id="showForm" autocomplete="off">
	        <div class="layui-form-item layui-col-xs12">
		        <label class="layui-form-label">所属仓库<i class="red">*</i></label>
		        <div class="layui-input-block ver-center" id="name">
		        </div>
		    </div>
		    <div class="layui-form-item layui-col-xs12">
	            <label class="layui-form-label">初始库存<i class="red">*</i></label>
	            <div class="layui-input-block">
	                <input type="text" id="stock" name="stock" win-verify="required|number" placeholder="请输入初始库存" class="layui-input"/>
	            </div>
	        </div>
		    <div class="layui-form-item layui-col-xs12">
		        <div class="layui-input-block">
		            <button class="winui-btn" id="cancle"><language showName="com.skyeye.cancel"></language></button>
		            <button class="winui-btn" lay-submit lay-filter="formEditBean"><language showName="com.skyeye.save"></language></button>
		        </div>
		    </div>
	    </form>
	</div>
	
	<script src="../../assets/lib/layui/layui.js"></script>
   	<script src="../../assets/lib/layui/custom.js"></script>
   	<script type="text/javascript">
		layui.config({base: '../../js/materialNormInitStock/'}).use('materialNormInitStockEdit');
	</script>
</body>
</html>