<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="../../assets/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="../../assets/lib/winui/css/winui.css" rel="stylesheet" />
    <link href="../../assets/lib/layui/lay/modules/ztree/css/zTreeStyle/zTreeStyle.css" rel="stylesheet" />
    <link href="../../assets/lib/layui/css/file-tree.css" rel="stylesheet" />
    <link href="../../assets/lib/layui/lay/modules/contextMenu/jquery.contextMenu.min.css" rel="stylesheet" />
</head>
<body>
    <div class="winui-toolbar">
        <div class="winui-tool">
            <button id="reloadTable" class="winui-toolbtn search-table-btn-right"><i class="fa fa-refresh" aria-hidden="true"></i><language showName="com.skyeye.refreshDataBtn"></language></button>
            <button id="addBean" class="winui-toolbtn search-table-btn-right" auth="1720752315835"><i class="fa fa-plus" aria-hidden="true"></i><language showName="com.skyeye.addBtn">新增仓库级别的值</language></button>
            <button id="batchAdd" class="winui-toolbtn search-table-btn-right" auth="1721184331706"><i class="fa fa-plus" aria-hidden="true"></i><language showName="com.skyeye.addBtn">批量新增</language></button>
        </div>
    </div>

    <div class="layui-row" style="height: calc(100% - 50px);">
        <div style="width: 240px; float: left; height: 100%; overflow: auto; padding: 5px;">
            <input type="text" id="name" name="name" placeholder="请输入要搜索的节点" class="layui-input" />
            <ul id="treeDemo" class="ztree fsTree" method="get" isRoot="1"
                treeIdKey="id" inputs="parentId" treePIdKey="parentId" clickCallbackInputs="parentId:$id" treeName="name"></ul>
        </div>
        <div style="width: calc(100% - 250px); float: left;">
            <div style="margin:auto 10px;">
                <table id="messageTable" lay-filter="messageTable"></table>
            </div>
        </div>
    </div>
    <script type="text/html" id="tableBar">
        {{# if(auth('1720752315835')){ }}
            <a class="layui-btn layui-btn-xs" lay-event="add">新增子节点</a>
            <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="edit"><language showName="com.skyeye.editBtn"></language></a>
        {{# } }}
        {{# if(auth('1721184331706')){ }}
            <a class="layui-btn layui-btn-xs" lay-event="batchAddChildNodes">批量新增子节点</a>
        {{# } }}
        {{# if(auth('1720752347259')){ }}
            <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="del"><language showName="com.skyeye.deleteBtn"></language></a>
        {{# } }}
    </script>

    <ul class="layui-dropdown-menu" id="treeRight">
    </ul>

    <script src="../../assets/lib/layui/layui.js"></script>
    <script src="../../assets/lib/layui/custom.js"></script>
    <script type="text/javascript" src="../../assets/lib/layui/lay/modules/jquery-min.js"></script>
    <script type="text/javascript" src="../../assets/lib/layui/lay/modules/contextMenu/jquery.contextMenu.min.js"></script>
    <script type="text/javascript" src="../../assets/lib/layui/lay/modules/ztree/js/jquery.ztree.all.min.js"></script>
    <script type="text/javascript" src="../../assets/lib/layui/lay/modules/ztree/js/jquery.ztree.exhide.min.js"></script>
    <script type="text/javascript" src="../../assets/lib/layui/lay/modules/ztree/js/fuzzysearch.js"></script>
    <script src="../../js/commonFile/fileConsoleListCommon.js"></script>
    <script type="text/javascript">
        layui.config({base: '../../js/turnIocatorManage/'}).use('turnIocatorManage');
    </script>
</body>
</html>