
server:
  port: 8080

spring:
  application:
    name: skyeye-web-${spring.profiles.active} # 服务名
  profiles:
    active: dev
  cloud:
    nacos:
      discovery:
        server-addr: 192.168.31.71:8848 # 配置服务注册nacos地址
        namespace: ${spring.profiles.active} # 配置命名空间
        service: ${spring.application.name} # 配置服务名
      config:
        # 指定nacos server的地址
        server-addr: 192.168.31.71:8848
        file-extension: yml
        namespace: ${spring.profiles.active} # 配置命名空间
        group: DEFAULT_GROUP # 配置分组