version: '1.0'
name: branch-pipeline
displayName: BranchPipeline
triggers:
  trigger: auto
  push:
    branches:
      include:
        - .*
      exclude:
        - master
stages:
  - name: compile
    displayName: 编译
    strategy: naturally
    trigger: auto
    steps:
      - step: build@maven
        name: build_maven
        displayName: Maven 构建
        jdkVersion: 8
        mavenVersion: 3.3.9
        commands:
          - mvn -B clean package -Dmaven.test.skip=true
        artifacts:
          - name: BUILD_ARTIFACT
            path:
              - ./web/target
      - step: publish@general_artifacts
        name: publish_general_artifacts
        displayName: 上传制品
        dependArtifact: BUILD_ARTIFACT
        artifactName: output
        dependsOn: build_maven
  - name: release
    displayName: 发布
    strategy: naturally
    trigger: auto
    steps:
      - step: publish@release_artifacts
        name: publish_release_artifacts
        displayName: 发布
        dependArtifact: output
        version: *******
        autoIncrement: true
