<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="../../assets/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="../../assets/lib/winui/css/winui.css" rel="stylesheet" />
</head>
<body>
	<div style="padding:20px; margin:0 auto;">
	    <form class="layui-form" action="" id="showForm" autocomplete="off">
	        <div class="layui-form-item layui-col-xs12">
	            <label class="layui-form-label">项目名称<i class="red">*</i></label>
	            <div class="layui-input-block">
                	<input type="text" id="projectName" name="projectName" win-verify="required" placeholder="请输入项目名称" class="layui-input" maxlength="50"/>
                </div>
	        </div>
	        <div class="layui-form-item layui-col-xs12">
	            <label class="layui-form-label">是否共享<i class="red">*</i></label>
	            <div class="layui-input-block winui-switch">
	                <input id="isShare" name="isShare" lay-filter="isShare" type="checkbox" lay-skin="switch" lay-text="是|否" value="false" />
	            </div>
	        </div>
	        <div class="layui-form-item layui-col-xs12">
	            <label class="layui-form-label">项目简介</label>
	            <div class="layui-input-block">
					<div id="content" name="content" type="text/plain"></div>
                </div>
	        </div>
	        <div class="layui-form-item layui-col-xs12">
	            <div class="layui-input-block">
	                <button class="winui-btn" id="cancle"><language showName="com.skyeye.cancel"></language></button>
	                <button class="winui-btn" lay-submit lay-filter="formAddBean"><language showName="com.skyeye.save"></language></button>
	            </div>
	        </div>
	    </form>
	</div>
	<script src="../../assets/lib/layui/layui.js"></script>
   	<script src="../../assets/lib/layui/custom.js"></script>
	<script type="text/javascript" src="../../assets/lib/layui/lay/modules/ueditor/ueditor.config.js"></script>
	<script type="text/javascript" src="../../assets/lib/layui/lay/modules/ueditor/ueditor.all.js"></script>
	<script type="text/javascript" src="../../assets/lib/layui/lay/modules/ueditor/lang/zh-cn/zh-cn.js"></script>
   	<script type="text/javascript">
        layui.config({base: '../../js/planProject/'}).use('planProjectAdd');
    </script>
</body>
</html>