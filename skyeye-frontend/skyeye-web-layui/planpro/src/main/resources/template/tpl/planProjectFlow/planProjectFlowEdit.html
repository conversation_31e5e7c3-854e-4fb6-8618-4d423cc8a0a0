<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="../../assets/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="../../assets/lib/winui/css/winui.css" rel="stylesheet" />
</head>
<body>
	<div style="margin: 0 auto; padding: 20px;">
	    <form class="layui-form" action="" id="showForm" autocomplete="off">
	        
	    </form>
	</div>

	<script type="text/x-handlebars-template" id="beanTemplate">
		{{#bean}}
		<div class="layui-form-item layui-col-xs12">
			<label class="layui-form-label">所属项目</label>
			<div class="layui-input-block ver-center" id="projectName">

			</div>
		</div>
		<div class="layui-form-item layui-col-xs12">
			<label class="layui-form-label">名称<i class="red">*</i></label>
			<div class="layui-input-block">
				<input type="text" id="title" name="title" win-verify="required" placeholder="请输入名称" class="layui-input" maxlength="50" value="{{title}}"/>
				<div class="layui-form-mid layui-word-aux">该名称为目录名称或者流程图名称</div>
			</div>
		</div>
		<div class="layui-form-item layui-col-xs12">
			<label class="layui-form-label">是否共享<i class="red">*</i></label>
			<div class="layui-input-block winui-switch">
				<input id="isShare" name="isShare" lay-filter="isShare" type="checkbox" lay-skin="switch" lay-text="是|否" {{#compare2 isShare}}{{/compare2}} value="{{#compare3 isShare}}{{/compare3}}" />
			</div>
		</div>
		<div class="layui-form-item layui-col-xs12">
			<div class="layui-input-block">
				<button class="winui-btn" id="cancle"><language showName="com.skyeye.cancel"></language></button>
				<button class="winui-btn" lay-submit lay-filter="formEditBean"><language showName="com.skyeye.save"></language></button>
			</div>
		</div>
		{{/bean}}
	</script>

	<script src="../../assets/lib/layui/layui.js"></script>
   	<script src="../../assets/lib/layui/custom.js"></script>
   	<script type="text/javascript">
        layui.config({base: '../../js/planProjectFlow/'}).use('planProjectFlowEdit');
    </script>
</body>
</html>