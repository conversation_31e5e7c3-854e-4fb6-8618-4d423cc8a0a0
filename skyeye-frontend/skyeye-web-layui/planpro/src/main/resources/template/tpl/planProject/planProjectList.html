<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="../../assets/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="../../assets/lib/winui/css/winui.css" rel="stylesheet" />
</head>
<body>
	<div class="txtcenter" style="margin:0 auto; padding-top:10px;">
	    <form class="layui-form layui-form-pane" action="" autocomplete="off">
	        <div class="layui-form-item">
	            <div class="layui-inline">
	                <label class="layui-form-label">项目名称</label>
	                <div class="layui-input-inline">
	                    <input id="projectName" name="projectName" class="layui-input" />
	                </div>
	                <button type="reset" class="layui-btn layui-btn-primary list-form-search"><language showName="com.skyeye.reset"></language></button>
	                <button class="layui-btn list-form-search" lay-submit lay-filter="formSearch"><language showName="com.skyeye.search2"></language></button>
	            </div>
	        </div>
	    </form>
	</div>
    <div class="winui-toolbar">
        <div class="winui-tool">
            <button id="reloadTable" class="winui-toolbtn"><i class="fa fa-refresh" aria-hidden="true"></i><language showName="com.skyeye.refreshDataBtn"></language></button>
            <button id="addBean" class="winui-toolbtn" auth="1561899197930"><i class="fa fa-plus" aria-hidden="true"></i><language showName="com.skyeye.addBtn"></language></button>
        </div>
    </div>
    <div style="margin:auto 10px;">
        <table id="messageTable" lay-filter="messageTable"></table>
        <script type="text/html" id="tableBar">
			{{# if(auth('1561900044693')){ }}
				<a class="layui-btn layui-btn-xs" lay-event="flowChart">流程图设计</a>
			{{# } }}
			{{# if(auth('1561899224669')){ }}
            	<a class="layui-btn layui-btn-xs" lay-event="edit"><language showName="com.skyeye.editBtn"></language></a>
			{{# } }}
			{{# if(d.childsNum == 0){ }}
				{{# if(auth('1561899207436')){ }}
          			<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del"><language showName="com.skyeye.deleteBtn"></language></a>
				{{# } }}
			{{# } }}
        </script>
    </div>
    <script src="../../assets/lib/layui/layui.js"></script>
    <script src="../../assets/lib/layui/custom.js"></script>
    <script type="text/javascript">
        layui.config({base: '../../js/planProject/'}).use('planProjectList');
    </script>
</body>
</html>