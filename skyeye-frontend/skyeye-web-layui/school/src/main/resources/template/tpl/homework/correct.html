<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title></title>
    <link href="../../assets/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="../../assets/lib/winui/css/winui.css" rel="stylesheet" />
</head>
<body>
<div style="margin:0 auto;padding:20px;">
    <form class="layui-form" action="" id="showForm" autocomplete="off">

    </form>
</div>

<script type="text/x-handlebars-template" id="beanTemplate">
    {{#bean}}
    <div class="layui-form-item layui-col-xs12">
        <span class="hr-title">基本信息</span><hr>
    </div>
    <div class="layui-form-item layui-col-xs6">
        <label class="layui-form-label">总分：</label>
        <div class="layui-input-block ver-center">
            {{fullMarks}}
        </div>
    </div>
    <div class="layui-form-item layui-col-xs12">
        <label class="layui-form-label">分数<i class="red">*</i></label>
        <div class="layui-input-block">
            <input type="text" id="score" name="score"  win-verify="required|number" placeholder="请输入分数(小于总分)" class="layui-input" />
        </div>
    </div>
    <div class="layui-form-item layui-col-xs12">
        <label class="layui-form-label">评语</label>
        <div class="layui-input-block">
            <textarea id="comment" name="comment"  placeholder="请输入评语" class="layui-textarea"></textarea>
        </div>
    </div>
    <div class="layui-form-item layui-col-xs12">
        <div class="layui-input-block">
            <button class="winui-btn" id="cancle"><language showName="com.skyeye.cancel"></language></button>
            <button class="winui-btn" lay-submit lay-filter="formAddBean">保存</button>
        </div>
    </div>
    {{/bean}}
</script>

<script src="../../assets/lib/layui/layui.js"></script>
<script src="../../assets/lib/layui/custom.js"></script>
<script type="text/javascript">
    layui.config({base: '../../js/homework/'}).use('correct');
</script>
</body>
</html>
