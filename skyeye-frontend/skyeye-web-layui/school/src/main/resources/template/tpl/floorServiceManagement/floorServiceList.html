<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="../../assets/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="../../assets/lib/winui/css/winui.css" rel="stylesheet" />
    <link href="../../assets/lib/layui/lay/modules/ztree/css/zTreeStyle/zTreeStyle.css" rel="stylesheet" />
    <link href="../../assets/lib/winui/css/customer/ztree/common-tree.css" rel="stylesheet" />
    <style type="text/css">
        .layui-table-body {
            height: 700px !important;
        }

        .layui-table-fixed .layui-table-body {
            height: 690px !important;
        }
    </style>
</head>
<body>
    <div class="winui-toolbar">
        <div class="winui-tool">
            <button id="reloadTable" class="winui-toolbtn search-table-btn-right"><i class="fa fa-refresh" aria-hidden="true"></i><language showName="com.skyeye.refreshDataBtn"></language></button>
            <button id="addBean" class="winui-toolbtn search-table-btn-right" auth="1731659909121"><i class="fa fa-plus" aria-hidden="true"></i><language showName="com.skyeye.addBtn"></language></button>
        </div>
    </div>

    <div class="layui-row" style="height: calc(100% - 50px);">
        <div style="width: 240px; float: left; height: 100%; overflow: auto; padding: 5px;">
            <form class="layui-form" action="" id="showForm" autocomplete="off">
                <div class="layui-input-block" style="margin-left: 0px; width: 100%;">
                    <select id="schoolId" name="schoolId" lay-filter="schoolId" lay-search=""></select>
                </div>
            </form>
            <input type="text" id="name" name="name" placeholder="请输入要搜索的地点" class="layui-input" />
            <ul id="treeDemo" class="ztree fsTree" method="get" isRoot="0"
                treeIdKey="id" inputs="parentId" treePIdKey="pId" clickCallbackInputs="parentId:$id" treeName="name"></ul>
        </div>




        <div style="width: calc(100% - 250px); float: left;">
            <div style="margin:auto 10px;">
                <table id="messageTable" lay-filter="messageTable"></table>
            </div>
        </div>
    </div>

    <script type="text/html" id="tableBar">
        {{# if(auth('1731659909121') &&( d.level == 1 || d.level == 2)){ }}
        <a class="layui-btn layui-btn-xs" lay-event="add">新增子节点</a>
        {{# } }}
        {{# if(auth('1731659909121')){ }}
        <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="edit"><language showName="com.skyeye.editBtn"></language></a>
        {{# } }}
        {{# if(auth('1731812925049')){ }}
        <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="del"><language showName="com.skyeye.deleteBtn"></language></a>
        {{# } }}
    </script>



    <script src="../../assets/lib/layui/layui.js"></script>
    <script src="../../assets/lib/layui/custom.js"></script>
    <script type="text/javascript" src="../../assets/lib/layui/lay/modules/jquery-min.js"></script>
    <script type="text/javascript" src="../../assets/lib/layui/lay/modules/ztree/js/jquery.ztree.all.min.js"></script>
    <script type="text/javascript" src="../../assets/lib/layui/lay/modules/ztree/js/jquery.ztree.exhide.min.js"></script>
    <script type="text/javascript" src="../../assets/lib/layui/lay/modules/ztree/js/fuzzysearch.js"></script>
    <script type="text/javascript">
        layui.config({base: '../../js/floorServiceManagement/'}).use('floorServiceList');
    </script>
</body>
</html>