<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="../../assets/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="../../assets/lib/winui/css/winui.css" rel="stylesheet" />
</head>
<body>
	<div class="txtcenter" style="margin:0 auto;padding-top:10px;">
	    <form class="layui-form layui-form-pane" action="" autocomplete="off">
	        <div class="layui-form-item">
	            <div class="layui-inline">
	            	<label class="layui-form-label">学校</label>
	                <div class="layui-input-inline">
	                    <select id="schoolId" lay-filter="schoolId" lay-search="">
							
	                    </select>
	                </div>
<!--	                <label class="layui-form-label">年级</label>-->
<!--	                <div class="layui-input-inline">-->
<!--	                    <select id="gradeId" lay-filter="gradeId" lay-search="">-->
<!--		            		<option value="">请选择</option>-->
<!--		            	</select>-->
<!--	                </div>-->
					<label class="layui-form-label">院系</label>
					<div class="layui-input-inline">
						<select id="facultyId" lay-filter="facultyId" lay-search="">
							<option value="">请选择</option>
						</select>
					</div>
					<label class="layui-form-label">专业</label>
					<div class="layui-input-inline">
						<select id="majorId" lay-filter="majorId" lay-search="">
							<option value="">请选择</option>
						</select>
					</div>
		            <label class="layui-form-label">姓名</label>
		            <div class="layui-input-inline">
		            	<input type="text" id="studentName" name="studentName" class="layui-input" placeholder="请输入学生姓名" />
		            </div>
		            <label class="layui-form-label">学号</label>
		            <div class="layui-input-inline">
		            	<input type="text" id="studentNo" name="studentNo" class="layui-input" placeholder="请输入学生学号" />
		            </div>
		            <label class="layui-form-label">试卷名称</label>
		            <div class="layui-input-inline">
		            	<input type="text" id="surveyName" name="surveyName" class="layui-input" placeholder="请输入试卷名称" />
		            </div>
	                <button type="reset" class="layui-btn layui-btn-primary list-form-search"><language showName="com.skyeye.reset"></language></button>
	                <button class="layui-btn list-form-search" lay-submit lay-filter="formSearch"><language showName="com.skyeye.search2"></language></button>
	            </div>
	        </div>
	    </form>
	</div>
    <div class="winui-toolbar">
        <div class="winui-tool">
            <button id="reloadTable" class="winui-toolbtn"><i class="fa fa-refresh" aria-hidden="true"></i><language showName="com.skyeye.refreshDataBtn"></language></button>
        </div>
    </div>
    <div style="margin:auto 10px;">
        <table id="messageTable" lay-filter="messageTable"></table>
        <script type="text/html" id="tableBar">
			{{# if(auth('1588569697793')){ }}
            	<a class="layui-btn layui-btn-xs" lay-event="examMarkingDetail">查看</a>
			{{# } }}
        </script>
    </div>
    <script src="../../assets/lib/layui/layui.js"></script>
    <script src="../../assets/lib/layui/custom.js"></script>
    <script type="text/javascript">
        layui.config({base: '../../js/myschooltask/'}).use('myWaitMarkingList');
    </script>
</body>
</html>