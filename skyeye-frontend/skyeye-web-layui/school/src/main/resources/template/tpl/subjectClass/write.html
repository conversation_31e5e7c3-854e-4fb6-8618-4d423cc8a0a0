<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="../../assets/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="../../assets/lib/winui/css/winui.css" rel="stylesheet" />
</head>
<body>
<div style="margin:0 auto;padding:20px;">
    <form class="layui-form" action="" id="showForm" autocomplete="off">
        <div class="layui-form-item layui-col-xs12">
            <span class="hr-title">基本信息</span><hr>
        </div>
        <div class="layui-form-item layui-col-xs6">
            <label class="layui-form-label">校区<i class="red">*</i></label>
            <div class="layui-input-block">
                <select id="schoolId" lay-filter="schoolId" lay-search="" win-verify="required">
                    <option value="">请选择</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item layui-col-xs6">
            <label class="layui-form-label">学院<i class="red">*</i></label>
            <div class="layui-input-block">
                <select id="facultyId" lay-filter="facultyId" lay-search="" win-verify="required">
                    <option value="">请选择</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item layui-col-xs6">
            <label class="layui-form-label">专业<i class="red">*</i></label>
            <div class="layui-input-block">
                <select id="majorId" lay-filter="majorId" lay-search="" win-verify="required">
                    <option value="">请选择</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item layui-col-xs6">
            <label class="layui-form-label">班级<i class="red">*</i></label>
            <div class="layui-input-block">
                <select id="classesId" lay-filter="classesId" lay-search=""  win-verify="required">
                    <option value="">请选择</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item layui-col-xs6">
            <label class="layui-form-label">学期<i class="red">*</i></label>
            <div class="layui-input-block">
                <select id="semesterId" lay-filter="semesterId" lay-search=""  win-verify="required">
                    <option value="">请选择</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item layui-col-xs6">
            <label class="layui-form-label">是否允许加入<i class="red">*</i></label>
            <div class="layui-input-block winui-radio" id="enabled">
            </div>
        </div>
        <div class="layui-form-item layui-col-xs6">
            <label class="layui-form-label">是否允许退出<i class="red">*</i></label>
            <div class="layui-input-block winui-radio" id="quit">
            </div>
        </div>
        <div class="layui-form-item layui-col-xs12">
            <div class="layui-input-block">
                <button class="winui-btn" id="cancle"><language showName="com.skyeye.cancel"></language></button>
                <button class="winui-btn" lay-submit lay-filter="formAddBean">保存</button>
            </div>
        </div>
    </form>
</div>

<script src="../../assets/lib/layui/layui.js"></script>
<script src="../../assets/lib/layui/custom.js"></script>
<script type="text/javascript">
    layui.config({base: '../../js/subjectClass/'}).use('write');
</script>

</body>
</html>