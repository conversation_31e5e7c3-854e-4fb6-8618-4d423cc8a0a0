<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title></title>
    <link href="../../assets/lib/layui/css/layui.css" rel="stylesheet" />
    <link href="../../assets/lib/winui/css/winui.css" rel="stylesheet" />
    <style>
        .layui-table-cell {
            height: auto;
            text-overflow: inherit;
            overflow: visible;
            white-space: normal;
            word-wrap: break-word;
        }

        .layui-table-cell .layui-anim {
            height: 180px;
        }

        .place-holder {
            height: 200px;
        }
    </style>
</head>
<body>
    <div class="winui-toolbar">
        <div class="winui-tool">
            <form class="layui-form layui-form-pane" action="" autocomplete="off">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">届</label>
                        <div class="layui-input-inline">
                            <input type="text" id="year" name="year" placeholder="请选择届" class="layui-input" />
                        </div>
                        <label class="layui-form-label">学校</label>
                        <div class="layui-input-inline">
                            <select id="schoolId" lay-filter="schoolId" lay-search="" lay-filter="schoolId" win-verify="required">
                                <option value="">请选择</option>
                            </select>
                        </div>
                        <label class="layui-form-label">院系</label>
                        <div class="layui-input-inline">
                            <select id="facultyId" lay-filter="facultyId" lay-search="" lay-filter="facultyId" win-verify="required">
                                <option value="">请选择</option>
                            </select>
                        </div>
                        <label class="layui-form-label">专业</label>
                        <div class="layui-input-inline">
                            <select id="majorId" lay-filter="majorId" lay-search="" win-verify="required">
                                <option value="">请选择</option>
                            </select>
                        </div>
                    </div>
                </div>
                <button class="layui-btn list-form-search" type="button" id="formSearch"><language showName="com.skyeye.search2"></language></button>
            </form>
        </div>
    </div>
    <div style="margin:0 auto; padding:20px;">
        <form class="layui-form" action="" id="showForm" autocomplete="off">
            <div class="layui-form-item layui-col-xs6">
                <label class="layui-form-label">届：</label>
                <div class="layui-input-block ver-center" id="yearShow">

                </div>
            </div>
            <div class="layui-form-item layui-col-xs6">
                <label class="layui-form-label">专业：</label>
                <div class="layui-input-block ver-center" id="majorShow">

                </div>
            </div>
            <div class="layui-form-item layui-col-xs12">
                <label class="layui-form-label">学年科目信息</label>
                <div class="layui-input-block">
                    <table class="layui-table" id="messageTable">

                    </table>
                </div>
            </div>

            <div class="layui-form-item layui-col-xs12">
                <div class="layui-input-block">
                    <button class="winui-btn" lay-submit lay-filter="formAddBean">保存</button>
                </div>
            </div>
        </form>
    </div>

    <script src="../../assets/lib/layui/layui.js"></script>
    <script src="../../assets/lib/layui/custom.js"></script>
    <script type="text/javascript">
        layui.config({base: '../../js/yearSubject/'}).use('saveYearSubject');
    </script>
</body>
</html>