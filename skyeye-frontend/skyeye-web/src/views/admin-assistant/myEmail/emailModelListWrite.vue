<template>
    <div class="email-model-write">
        <SkForm v-model="formData" :rules="rules" @submit="handleSubmit" @reset="handleReset">
            <a-row>
                <a-col :span="24">
                    <a-form-item label="收件人" name="toPeople">
                        <SkEmail v-model="formData.toPeople" v-model:mationValue="formData.toPeopleMation"
                            placeholder="请选择收件人" :isEdit="isEdit" :objectId="id" />
                    </a-form-item>
                </a-col>

                <a-col :span="24">
                    <a-form-item label="抄送" name="toCc">
                        <SkEmail v-model="formData.toCc" v-model:mationValue="formData.toCcMation" placeholder="请选择抄送人"
                            :isEdit="isEdit" :objectId="id" />
                    </a-form-item>
                </a-col>

                <a-col :span="24">
                    <a-form-item label="密送" name="toBcc">
                        <SkEmail v-model="formData.toBcc" v-model:mationValue="formData.toBccMation"
                            placeholder="请选择密送人" :isEdit="isEdit" :objectId="id" />
                    </a-form-item>
                </a-col>

                <a-col :span="24">
                    <a-form-item label="主题" name="title">
                        <SkInput v-model="formData.title" placeholder="请填写邮件主题" :maxlength="50" :isEdit="isEdit" />
                    </a-form-item>
                </a-col>
            </a-row>
        </SkForm>
    </div>
</template>

<script setup>
import { reactive, onMounted, getCurrentInstance, ref } from 'vue'
import SkForm from '@/components/SkForm/index.vue'
import SkEmail from '@/components/SkEmail/index.vue'
import { SkMessage } from '@/components/SkMessage/index.vue'
import SkInput from '@/components/SkInput/index.vue'

const { proxy } = getCurrentInstance()

const props = defineProps({
    id: {
        type: [String],
        default: '',
        required: false
    }
})

const emit = defineEmits(['submit'])

// 表单数据
const formData = reactive({
    toPeople: '',
    toCc: '',
    toBcc: '',
    title: '',
})

// 表单校验规则
const rules = {
    toPeople: [
        { required: true, message: '请选择收件人', trigger: 'blur' },
    ],
    title: [
        { required: true, message: '请填写邮件主题', trigger: 'blur' },
    ],
}

// 添加 isEdit 变量
const isEdit = ref(proxy.$config.formEditType.isEdit)

// 获取详情
const getDetail = async () => {
    console.log("props.id", props.id)
    try {
        const res = await proxy.$http.get(
            proxy.$config.getConfig().emailBasePath + 'queryEmailSendById',
            { id: props.id }
        );

        // 更新表单数据
        formData.toPeople = res.bean.toPeople;
        formData.toCc = res.bean.toCc;
        formData.toBcc = res.bean.toBcc;
        formData.title = res.bean.title;

        // 转换邮箱字符串为用户对象数组
        const convertEmailsToUsers = (emails) => {
            if (!emails) return [];
            return emails.split(',').map((email, index) => ({
                id: `${props.id}_${index}_${email}`,
                email: email,
                name: email.split('@')[0]
            }));
        };

        // 设置 mationValue 数据
        formData.toPeopleMation = convertEmailsToUsers(res.bean.toPeople);
        formData.toCcMation = convertEmailsToUsers(res.bean.toCc);
        formData.toBccMation = convertEmailsToUsers(res.bean.toBcc);

    } catch (error) {
        SkMessage.error('获取详情失败')
    }
}

// 提交函数
const handleSubmit = async () => {
    try {
        const params = {
            toPeople: formData.toPeopleIds || formData.toPeople, // 优先使用ID
            toCc: formData.toCcIds || formData.toCc || [],
            toBcc: formData.toBccIds || formData.toBcc || [],
            title: formData.title,
            id: props.id || undefined,
        }

        // 新增和编辑使用同一个接口，不需要区分
        await proxy.$http.post(
            proxy.$config.getConfig().emailBasePath + 'writeEmailSendModel',
            params
        )

        SkMessage.success('保存成功')
        emit('submit', true)
    } catch (error) {
        SkMessage.error(error.message || '保存失败')
    }
}

// 取消
const handleReset = () => {
    emit('submit', false)
}

onMounted(() => {
    if (props.id) {
        getDetail()
    }
})
</script>

<style></style>
