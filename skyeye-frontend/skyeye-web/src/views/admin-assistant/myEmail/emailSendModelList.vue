<template>
    <div class="container-manage">
        <SkCard ref="cardRef" :bordered="false">
            <div class="table-search">
                <SkForm layout="inline" v-model="searchForm" @submit="handleSearch" @reset="handleReset"
                    :submitText="$t('common.search')" :resetText="$t('common.reset')" :isFormSubmit="false">
                    <template #submitIcon>
                        <search-outlined />
                    </template>
                    <a-form-item name="keyword">
                        <SkInput v-model="searchForm.keyword" placeholder="请输入主题" allowClear
                            @pressEnter="handleSearch" />
                    </a-form-item>
                </SkForm>
            </div>
            <div class="table-operations">
                <SkSpace>
                    <SkButton v-if="$config.auth('1636170287505')" type="primary" @click.prevent="handleAdd">
                        <template #icon><plus-outlined /></template>
                        {{ $t("common.add") }}
                    </SkButton>
                    <SkButton type="primary" @click.prevent="fetchData">
                        <template #icon>
                            <ReloadOutlined />
                        </template>
                        {{ $t("common.refresh") }}
                    </SkButton>
                </SkSpace>
            </div>
            <SkTable :columns="columns" :data-source="tableData" :loading="loading" :pagination="pagination"
                :ready="tableReady" @change="handleTableChange">
                <template #bodyCell="{ column, record }">
                    <template v-if="column.key === 'action'">
                        <SkSpace>
                            <!-- 编辑 -->
                            <a v-if="$config.auth('1636170287505')" @click="handleEdit(record)">
                                {{ $t("common.edit") }}
                            </a>
                            <SkDivider v-if="$config.auth('1636170287505')" type="vertical" />
                            <!-- 删除 -->
                            <SkPopconfirm v-if="$config.auth('1636170297232')" :title="$t('common.deleteConfirm')"
                                @confirm="handleDelete(record)" :okText="$t('common.delete')"
                                :cancelText="$t('common.cancel')">
                                <a class="danger-link">{{ $t('common.delete') }}</a>
                            </SkPopconfirm>
                        </SkSpace>
                    </template>
                </template>
            </SkTable>

            <SkModal v-model="modalVisible" :width="modalWidth" :title="modalTitle">
                <EmailModelListWrite :id="operatorParams.params?.id" @submit="handleModalClick" />
            </SkModal>
        </SkCard>
    </div>
</template>
<script setup>
import { reactive, ref, getCurrentInstance, onMounted, nextTick } from "vue";
import { useI18n } from "vue-i18n";
import SkForm from "@/components/SkForm/index.vue";
import SkInput from "@/components/SkInput/index.vue";
import SkCard from "@/components/SkCard/index.vue";
import SkButton from "@/components/SkButton/index.vue";
import SkSpace from "@/components/SkSpace/index.vue";
import SkTable from "@/components/SkTable/index.vue";
import SkDivider from "@/components/SkDivider/index.vue";
import SkPopconfirm from "@/components/SkPopconfirm/index.vue";
import { SkMessage } from '@/components/SkMessage/index.vue';
import SkModal from "@/components/SkModal/index.vue";
import EmailModelListWrite from './emailModelListWrite.vue'

const { proxy } = getCurrentInstance();
const { t } = useI18n();

// 搜索表单数据
const searchForm = reactive({
    keyword: "",
});

// 表格列配置
const columns = ref([
    {
        title: t("common.serialNum"),
        dataIndex: "index",
        width: 80,
        align: "center",
        fixed: "left",
        customRender: ({ index }) => {
            // 计算实际序号 = (当前页码 - 1) * 每页条数 + 当前行索引 + 1
            return (pagination.current - 1) * pagination.pageSize + index + 1;
        },
    },
    {
        title: '主题',
        dataIndex: 'title',
        width: 140
    },
    {
        title: "创建人",
        dataIndex: "createName",
        width: 140,
    },
    {
        title: "创建时间",
        dataIndex: "createTime",
        width: 150,
        align: "center",
    },
    {
        title: "最后修改人",
        dataIndex: "lastUpdateName",
        width: 140,
    },
    {
        title: "最后修改时间",
        dataIndex: "lastUpdateTime",
        width: 150,
        align: "center",
    },
    {
        title: "操作",
        key: "action",
        width: 220,
        align: "center",
        fixed: "right",
    },
]);

const tableData = ref([]); // 表格数据
const loading = ref(false); // 加载状态
const tableReady = ref(false);

// 分页配置
const pagination = reactive(proxy.$config.pagination());

// 获取数据
const fetchData = async () => {
    loading.value = true;
    try {
        // 构建查询参数，添加 keyword
        const params = {
            page: Number(pagination.current || 1),
            limit: Number(pagination.pageSize || 10),
            keyword: searchForm.keyword?.trim() || "",
        };

        // 发送查询请求
        const res = await proxy.$http.post(
            proxy.$config.getConfig().emailBasePath + "emailsendmodel001",
            params
        );

        tableData.value = res.rows || [];
        pagination.total = res.total || 0;
    } catch (error) {
        SkMessage.error("获取数据失败");
        tableData.value = [];
        pagination.total = 0;
    } finally {
        loading.value = false;
    }
};

// 事件处理 搜索
const handleSearch = () => {
    pagination.current = 1; // 重置到第一页
    fetchData();
};

// 添加重置处理函数
const handleReset = () => {
    searchForm.keyword = ""; // 清空搜索关键字
    pagination.current = 1; // 重置到第一页
    fetchData();
};

// 表格变化处理
const handleTableChange = (pag) => {
    if (pag) {
        pagination.current = Number(pag.current || 1);
        pagination.pageSize = Number(pag.pageSize || 10);
    }
    fetchData();
};

const modalVisible = ref(false)
const modalWidth = ref("70%")
const operatorParams = reactive({
    params: {}
})
const modalTitle = ref('新增邮件模板');

// 新增
const handleAdd = async () => {
    modalTitle.value = '新增'
    operatorParams.params = {}
    modalVisible.value = true
}

// 编辑
const handleEdit = (record) => {
    modalTitle.value = '编辑'
    operatorParams.params = { id: record.id }
    modalVisible.value = true
}

// 删除
const handleDelete = async (record) => {
    try {
        const params = {
            id: record.id,
        }
        await proxy.$http.delete(
            proxy.$config.getConfig().emailBasePath + 'deleteEmailSendById',
            params
        )
        SkMessage.success('删除成功')
        fetchData(); // 刷新数据
    } catch (error) {
        SkMessage.error('删除失败')
    }
}

// 弹窗关闭
const handleModalClick = (isSubmit) => {
    modalVisible.value = false;
    if (isSubmit) {
        fetchData(); // 刷新表格数据
    }
}

// 初始化
onMounted(async () => {
    await nextTick();
    requestAnimationFrame(() => {
        tableReady.value = true;
    });
    fetchData();
});
</script>
<style scoped></style>