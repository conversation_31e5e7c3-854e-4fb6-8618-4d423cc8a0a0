<template>
    <div class="container-manage">
        <SkCard ref="cardRef" :bordered="false">
            <!-- 搜索表单 -->
            <div class="table-search">
                <SkForm layout="inline" v-model="searchForm" @submit="handleSearch" @reset="handleReset"
                    :submitText="$t('common.search')" :resetText="$t('common.reset')" :isFormSubmit="false">
                    <template #submitIcon>
                        <search-outlined />
                    </template>
                    <a-form-item name="keyword">
                        <SkInput v-model="searchForm.keyword" placeholder="请输入单号、标题" allowClear
                            @pressEnter="handleSearch" />
                    </a-form-item>
                </SkForm>
            </div>
            <!-- 操作按钮 -->
            <div class="table-operations">
                <SkSpace>
                    <SkButton v-if="$config.auth('1720237812642')" type="primary" @click.prevent="handleAdd">
                        <template #icon>
                            <plus-outlined />
                        </template>
                        {{ $t('common.add') }}
                    </SkButton>
                    <SkButton type="primary" @click.prevent="fetchData">
                        <template #icon>
                            <ReloadOutlined />
                        </template>
                        {{ $t("common.refresh") }}
                    </SkButton>
                </SkSpace>
            </div>

            <SkTable :columns="columns" :data-source="tableData" :loading="loading" :pagination="pagination"
                :ready="tableReady" @change="handleTableChange">
                <template #bodyCell="{ column, record }">
                    <template v-if="column.dataIndex === 'oddNumber'">
                        <a @click="handleDetail(record)">
                            {{ record.oddNumber }}
                            <template v-if="!$util.isNull(record.fromId)">
                                <SkTag :color="$util.getTagColor(column.dataIndex)">
                                    转
                                </SkTag>
                            </template>
                        </a>
                    </template>
                    <template v-if="column.dataIndex === 'processInstanceId'">
                        <ProcessDetail :processInstanceId="record.processInstanceId" />
                    </template>
                    <template v-if="column.dataIndex === 'state'">
                        <div v-html="$util.skyeyeClassEnumUtil.getEnumDataNameByCodeAndKeyStatic(
                            initEnumData['flowableStateEnum'], 'id', record.state, 'name')">
                        </div>
                    </template>
                    <template v-if="column.key === 'action'">
                        <SkSpace>
                            <template v-if="record.editRow == 1">
                                <!-- 提交审批 -->
                                <a v-if="$config.auth('1720237791114')" @click="handleConfirmOk(record)">
                                    {{ $t("common.submitApproval") }}
                                </a>
                                <SkDivider v-if="$config.auth('1720237791114')" type="vertical" />
                                <!-- 编辑 -->
                                <a v-if="$config.auth('1720237812642')" @click="handleEdit(record, 'edit')">
                                    {{ $t("common.edit") }}
                                </a>
                                <SkDivider v-if="$config.auth('1720237812642')" type="vertical" />
                                <!-- 删除 -->
                                <SkPopconfirm v-if="$config.auth('1720237827794')" :title="$t('common.deleteConfirm')"
                                    @confirm="handleDelete(record)" :okText="$t('common.delete')"
                                    :cancelText="$t('common.cancel')">
                                    <a class="danger-link">{{ $t('common.delete') }}</a>
                                </SkPopconfirm>
                            </template>
                            <template v-if="record.editRow == 2">
                                <!-- 撤销 -->
                                <SkPopconfirm v-if="$config.auth('1720237765209')" :title="$t('common.revokeConfirm')"
                                    @confirm="handleRevoke(record)" :okText="$t('common.confirm')"
                                    :cancelText="$t('common.cancel')">
                                    <a class="danger-link">{{ $t("common.revoke") }}</a>
                                </SkPopconfirm>
                            </template>
                        </SkSpace>
                    </template>
                </template>
            </SkTable>
            <!-- 弹窗 -->
            <SkModal v-model="modalVisible" :title="modalTitle">
                <ShowIndex ref="showIndexRef" @loaded="handleShowIndexLoaded"
                    v-if="modalType === 'add' || modalType === 'edit' || modalType === 'details' || modalType === 'transfer' || modalType === 'return'"
                    :pageId="operatorParams.pageId" :params="operatorParams.params" @close="handleModalClick"
                    @cell-change="handleCellChange" @afterDataLoaded="handleAfterDataLoaded">
                    <!-- 传递自定义单元格组件  资产选择-->
                    <template #cell-chooseInput-assetId="slotProps">
                        <a-form-item-rest>
                            <SkAssetSelect v-model="slotProps.record[slotProps.column.dataIndex]"
                                :formData="slotProps.record"
                                :isEdit="slotProps.column.getConfig(slotProps.record).isEdit"
                                :attrKey="slotProps.column.dataIndex"
                                @change="(material) => handleMaterialChange(material, slotProps.record, slotProps.column, slotProps.formData)" />
                        </a-form-item-rest>
                    </template>
                </ShowIndex>
                <!-- 审批人弹窗 -->
                <ApprovalPersonSelect v-if="modalType === 'approval'" :actKey="currentRecord?.serviceClassName"
                    :businessData="null" @submit="handleApprovalPersonSubmit" @cancel="handleModalCancel" />
            </SkModal>
        </SkCard>
    </div>
</template>

<script setup>
import { useI18n } from "vue-i18n";
import SkTable from "@/components/SkTable/index.vue";
import SkForm from "@/components/SkForm/index.vue";
import SkSpace from "@/components/SkSpace/index.vue";
import ProcessDetail from '@/views/dsForm/process/detail.vue'
import SkButton from "@/components/SkButton/index.vue";
import { SearchOutlined, PlusOutlined, ReloadOutlined, } from "@ant-design/icons-vue";
import { ref, onMounted, getCurrentInstance, reactive, nextTick } from "vue";
import SkInput from "@/components/SkInput/index.vue";
import SkCard from "@/components/SkCard/index.vue";
import { SkMessage } from "@/components/SkMessage/index.vue";
import SkModal from "@/components/SkModal/index.vue";
import SkDivider from "@/components/SkDivider/index.vue";
import SkPopconfirm from "@/components/SkPopconfirm/index.vue";
import ShowIndex from "@/views/dsForm/show/index.vue";
import SkAssetSelect from '@/components/SkAssetSelect/index.vue'
import ApprovalPersonSelect from "@/views/system/submitApproval/approvalPersonSelect.vue";
import SkTag from '@/components/SkTag/index.vue'


const { proxy } = getCurrentInstance();
const { t } = useI18n();

// 表格列配置
const columns = ref([
    {
        title: t("common.serialNum"),
        dataIndex: "index",
        width: 80,
        align: "center",
        fixed: "left",
        customRender: ({ index }) => {
            // 计算实际序号 = (当前页码 - 1) * 每页条数 + 当前行索引 + 1
            return (pagination.current - 1) * pagination.pageSize + index + 1;
        },
    },
    {
        title: "单号",
        dataIndex: "oddNumber",
        width: 200,
        align: "center",
    },
    {
        title: "标题",
        dataIndex: "title",
        width: 240,
    },
    {
        title: "流程ID",
        dataIndex: "processInstanceId",
        width: 240,
        align: "center",
    },
    {
        title: "状态",
        dataIndex: "state",
        width: 120,
    },
    {
        title: "创建人",
        dataIndex: "createName",
        width: 140,
    },
    {
        title: "创建时间",
        dataIndex: "createTime",
        width: 150,
        align: "center",
    },
    {
        title: "最后修改人",
        dataIndex: "lastUpdateName",
        width: 140,
    },
    {
        title: "最后修改时间",
        dataIndex: "lastUpdateTime",
        width: 150,
        align: "center",
    },
    {
        title: "操作",
        key: "action",
        width: 220,
        align: "center",
        fixed: "right",
    },
]);

const showIndexRef = ref(null)

const tableData = ref([]); // 表格数据
const loading = ref(false); // 加载状态
const tableReady = ref(false);

// 分页配置
const pagination = reactive(proxy.$config.pagination());

// 初始化枚举数据
const initEnumData = ref({});
const getInitData = async () => {
    let enumResult = await proxy.$util.getEnumListMapByCode([
        "flowableStateEnum",
    ]);
    initEnumData.value = enumResult;
};

const fetchData = async () => {
    loading.value = true;
    try {
        // 构建查询参数，添加 keyword
        const params = {
            page: Number(pagination.current || 1),
            limit: Number(pagination.pageSize || 10),
            keyword: searchForm.keyword?.trim() || "",
        };

        // 发送查询请求
        const res = await proxy.$http.post(
            proxy.$config.getConfig().admBasePath + "queryAssetPurchasePutList",
            params
        );

        tableData.value = res.rows || [];
        pagination.total = res.total || 0;
    } catch (error) {
        SkMessage.error("获取数据失败");
        tableData.value = [];
        pagination.total = 0;
    } finally {
        loading.value = false;
    }
};

// 添加重置处理函数
const handleReset = () => {
    searchForm.keyword = "";
    pagination.current = 1;
    fetchData();
};

// 事件处理 搜索
const handleSearch = () => {
    pagination.current = 1;
    fetchData();
};

// 表格变化处理
const handleTableChange = (pag) => {
    if (pag) {
        pagination.current = Number(pag.current || 1);
        pagination.pageSize = Number(pag.pageSize || 10);
    }
    fetchData();
};

const modalVisible = ref(false);
const modalTitle = ref("资产采购入库申请");
const modalType = ref("edit");
const operatorParams = ref({});

//编辑
const handleEdit = (record) => {
    modalTitle.value = "编辑";
    modalType.value = "edit";
    modalVisible.value = true;
    operatorParams.value = {
        pageId: "FP2024070500002",
        params: {
            id: record.id,
        },
    };
};

// 新增
const handleAdd = () => {
    modalTitle.value = "新增";
    modalType.value = "add";
    modalVisible.value = true;
    operatorParams.value = {
        pageId: "FP2024070500001",
        params: {},
    };
};

// 详情
const handleDetail = (record) => {
    modalTitle.value = "详情";
    modalType.value = "details";
    modalVisible.value = true;
    operatorParams.value = {
        pageId: "FP2024070500003",
        params: {
            id: record.id,
        },
    };
};

// 当前行记录
const currentRecord = ref(null);

// 弹窗关闭
const handleModalClick = (isSubmit) => {
    modalVisible.value = false;
    if (isSubmit) {
        fetchData();
    }
};

// 修改提交审批处理方法
const handleConfirmOk = async (record) => {
    try {
        await nextTick();
        modalTitle.value = "提交审批";
        modalType.value = "approval";
        // 打开审批人选择弹窗
        currentRecord.value = record;
        modalVisible.value = true;
    } catch (error) {
        SkMessage.error("操作失败");
    }
};

// 处理弹窗取消
const handleModalCancel = async () => {
    await nextTick();
    modalVisible.value = false;
};

// 处理审批人选择提交
const handleApprovalPersonSubmit = async (person) => {
    try {
        // 构建参数，与旧系统保持一致
        const params = {
            id: currentRecord.value.id,
            approvalId: person.id,
        };

        // 发送提交请求
        await proxy.$http.post(
            proxy.$config.getConfig().admBasePath + "submitAssetPurchasePut",
            params
        );

        // 提交成功
        SkMessage.success("提交成功");
        // 关闭弹窗
        modalVisible.value = false;
        // 刷新数据
        await fetchData();
    } catch (error) {
        SkMessage.error("提交失败");
    }
};

// 处理组件加载完成
const handleShowIndexLoaded = (formData) => {
    const pageId = operatorParams.value.pageId
    // 在组件加载完成后执行需要的操作
    if (pageId == 'FP2024070500002' && !proxy.$util.isNull(formData.fromId)) {
        showIndexRef.value?.writeComponentRef?.updateShowAdd("purchaseLinks", false)
    }
}

// 处理物料选择变化
const handleMaterialChange = async (material, record, column, formData) => {
    const dataIndex = column.dataIndex
    const mationKey = proxy.$util.getKeyIdToMation(dataIndex)

    if (material) {
        // 更新其他相关字段
        record[mationKey] = material
        // 设置单价为参考价
        record["unitPrice"] = material.readPrice || 0

        // 等待组件挂载完成
        await nextTick()

        // 触发 cell-change 事件
        handleCellChange({
            record,
            dataIndex: column.dataIndex,
            value: material,
            column: column
        }, formData)
    } else {
        // 清空相关字段
        delete record[mationKey]
        record["unitPrice"] = 0
        record["allPrice"] = 0
        record["purchaseNum"] = 1  // 重置数量为1
    }
    // 重新计算总金额
    calcMoney(formData)
}

// 处理单元格变化
const handleCellChange = ({ record }, formData) => {
    // 计算单行总金额，单价乘以数量
    const result = {
        amountOfMoney: proxy.$util.calculationUtil.multiplication(record.unitPrice || 0, record.purchaseNum || 0)
    };
    Object.assign(record, result);
    calcMoney(formData);
};

const calcMoney = (formData) => {
    // 处理金额计算
    const assetOrderItemList = formData?.purchaseLinks || [];
    let totalPrice = 0;

    if (!proxy.$util.isNull(assetOrderItemList)) {
        assetOrderItemList.forEach(item => {
            totalPrice = proxy.$util.calculationUtil.sum(totalPrice, item.amountOfMoney);
        });
    }
    // 更新表单数据
    formData.allPrice = totalPrice;
};

// 处理数据回显  
const handleAfterDataLoaded = (formData) => {
    const notEdit = proxy.$config.formEditType.notEdit

    if (!proxy.$util.isNull(formData.assetPurchasePutFromType)) {
        formData.assetPurchasePutFromType.forEach(item => {
            // 下拉框的特殊配置  
            item['normsId_config'] = {
                dataType: 1,
                defaultData: item.materialMation?.materialNorms || []
            }

            // 当有单据来源时设置禁用  
            if (!proxy.$util.isNull(formData.fromId)) {
                const disabledFields = [
                    'assetId',
                    'fromId',
                    'unitPrice'
                ]

                setDisabledFields(item, notEdit, disabledFields);
            }
        })
    }
}

// 提取设置禁用字段的函数  
function setDisabledFields(item, isEdit, fields) {
    fields.forEach(field => {
        item[`${field}_config`] = { isEdit }
    })
}

// 撤销处理
const handleRevoke = async (record) => {
    try {
        const params = {
            processInstanceId: record.processInstanceId,
        };

        await proxy.$http.put(
            proxy.$config.getConfig().admBasePath + "revokeAssetPurchasePut",
            params
        );
        SkMessage.success("撤销成功");
        await fetchData(); // 刷新数据
    } catch (error) {
        SkMessage.error("撤销失败");
    }
};

// 删除
const handleDelete = async (record) => {
    try {
        const params = {
            id: record.id,
            serviceClassName: record.serviceClassName
        }
        await proxy.$http.delete(
            proxy.$config.getConfig().admBasePath + 'deleteAssetPurchasePutById',
            params
        )
        SkMessage.success('删除成功')
        fetchData() // 刷新数据
    } catch (error) {
        SkMessage.error('删除失败')
    }
}

// 搜索表单
const searchForm = reactive({
    keyword: "",
});

// 初始化
onMounted(async () => {
    await getInitData();
    await fetchData();
    tableReady.value = true;
});
</script>

<style lang="less" scoped></style>
