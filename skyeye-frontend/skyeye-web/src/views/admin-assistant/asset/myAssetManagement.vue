<template>
    <div class="container-manage">
        <SkCard :bordered="false">
            <div class="table-search">
                <SkForm layout="inline" v-model="searchForm" @submit="handleSearch" @reset="handleReset"
                    :submitText="$t('common.search')" :resetText="$t('common.reset')" :isFormSubmit="false">
                    <template #submitIcon>
                        <search-outlined />
                    </template>
                    <a-form-item name="keyword">
                        <SkInput v-model="searchForm.keyword" placeholder="请输入资产编号" allowClear
                            @pressEnter="handleSearch" />
                    </a-form-item>
                </SkForm>
            </div>

            <!-- 工具栏 -->
            <div class="table-operations">
                <SkSpace>
                    <SkButton type="primary" @click.prevent="fetchData">
                        <template #icon>
                            <ReloadOutlined />
                        </template>
                        {{ $t('common.refresh') }}
                    </SkButton>
                </SkSpace>
            </div>

            <!-- 表格 -->
            <SkTable :columns="columns" :data-source="tableData" :loading="loading" :pagination="pagination"
                :ready="tableReady" @change="handleTableChange">
                <!-- 图片列自定义渲染 -->
                <template #bodyCell="{ column, record }">
                    <template v-if="column.dataIndex === 'assetImg'">
                        <SkTableImg :imgPath="record.assetMation?.assetImg" />
                    </template>
                    <template v-if="column.dataIndex === 'typeId'">
                        {{ initDictData['ADM_ASSET_TYPE'][record.assetMation?.typeId] }}
                    </template>
                </template>
            </SkTable>
        </SkCard>
    </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance, nextTick, reactive } from 'vue'
import { useI18n } from 'vue-i18n'
import { ReloadOutlined } from '@ant-design/icons-vue'
import SkCard from '@/components/SkCard/index.vue'
import SkButton from '@/components/SkButton/index.vue'
import SkSpace from '@/components/SkSpace/index.vue'
import SkTable from '@/components/SkTable/index.vue'
import { SkMessage } from '@/components/SkMessage/index.vue'
import SkTableImg from '@/components/SkTableImg/index.vue'
import SkForm from "@/components/SkForm/index.vue";
import SkInput from "@/components/SkInput/index.vue";

const { proxy } = getCurrentInstance()
const { t } = useI18n()

// 搜索表单数据
const searchForm = reactive({
    keyword: "",
});

// 表格相关
const tableData = ref([])
const loading = ref(false)
const tableReady = ref(false)
const pagination = reactive(proxy.$config.pagination())

// 初始化数据字典数据
const initDictData = ref({})

// 表格列配置
const columns = [
    {
        title: t('common.serialNum'),
        type: 'index',
        width: 80,
        align: 'center',
        fixed: 'left',
        customRender: ({ index }) => {
            // 计算实际序号 = (当前页码 - 1) * 每页条数 + 当前行索引 + 1
            return (pagination.current - 1) * pagination.pageSize + index + 1
        }
    },
    {
        title: '资产编号',
        dataIndex: 'assetNum',
        width: 200,
        align: 'left'
    },
    {
        title: '资产名称',
        dataIndex: 'name',
        width: 120,
        customRender: ({ record }) => record.assetMation?.name
    },
    {
        title: '图片',
        dataIndex: 'assetImg',
        width: 60,
        align: 'center'
    },
    {
        title: '资产类型',
        dataIndex: 'typeId',
        width: 120
    },
    {
        title: '管理员',
        dataIndex: 'assetAdminMation',
        width: 120,
        customRender: ({ record }) => record.assetAdminMation?.name
    },
    {
        title: '申领时间',
        dataIndex: 'createTime',
        width: 150,
        align: 'center'
    }
]

// 获取数据字典
const getInitData = async () => {
    let dictResult = await proxy.$util.getDictListMapByCode(['ADM_ASSET_TYPE'])
    initDictData.value = dictResult
}

// 获取表格数据
const fetchData = async () => {
    loading.value = true
    try {
        const params = {
            page: pagination.current,
            limit: pagination.pageSize,
            keyword: searchForm.keyword?.trim() || "",
            state: 'myUse'
        }

        const res = await proxy.$http.post(
            proxy.$config.getConfig().admBasePath + 'queryAssetReportList',
            params
        )

        tableData.value = res.rows || []
        pagination.total = res.total || 0
    } catch (error) {
        SkMessage.error('获取数据失败')
        tableData.value = []
        pagination.total = 0
    } finally {
        loading.value = false
    }
}

// 事件处理 搜索
const handleSearch = () => {
    pagination.current = 1; // 重置到第一页
    fetchData();
};

// 添加重置处理函数
const handleReset = () => {
    searchForm.keyword = ""; // 清空搜索关键字
    pagination.current = 1; // 重置到第一页
    fetchData();
};

// 表格变化处理
const handleTableChange = (pag) => {
    if (pag) {
        pagination.current = pag.current
        pagination.pageSize = pag.pageSize
    }
    fetchData()
}

// 初始化
onMounted(async () => {
    await nextTick()
    requestAnimationFrame(() => {
        tableReady.value = true
    })
    await getInitData()
    await fetchData()
})
</script>

<style lang="less" scoped></style>