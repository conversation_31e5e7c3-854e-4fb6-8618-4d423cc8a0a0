<template>
    <div class="container-manage">
        <SkCard ref="cardRef" :bordered="false">
            <div class="table-search">
                <SkForm layout="inline" v-model="searchForm" @submit="handleSearch" @reset="handleReset"
                    :submitText="$t('common.search')" :resetText="$t('common.reset')" :isFormSubmit="false">
                    <template #submitIcon>
                        <search-outlined />
                    </template>
                    <a-form-item name="keyword">
                        <SkInput v-model="searchForm.keyword" placeholder="请输入单号、标题" allowClear
                            @pressEnter="handleSearch" />
                    </a-form-item>
                </SkForm>
            </div>
            <div class="table-operations">
                <SkSpace>
                    <SkButton v-if="$config.auth('1714142882792')" type="primary" @click.prevent="handleAdd">
                        <template #icon><plus-outlined /></template>
                        {{ $t("common.add") }}
                    </SkButton>
                    <SkButton type="primary" @click.prevent="fetchData">
                        <template #icon>
                            <ReloadOutlined />
                        </template>
                        {{ $t("common.refresh") }}
                    </SkButton>
                </SkSpace>
            </div>
            <SkTable :columns="columns" :data-source="tableData" :loading="loading" :pagination="pagination"
                :ready="tableReady" @change="handleTableChange">
                <template #bodyCell="{ column, record }">
                    <template v-if="column.dataIndex === 'oddNumber'">
                        <a @click="handleDetail(record)">
                            {{ record.oddNumber }}
                        </a>
                    </template>
                    <template v-if="column.dataIndex === 'processInstanceId'">
                        <ProcessDetail :processInstanceId="record.processInstanceId" />
                    </template>

                    <template v-if="column.dataIndex === 'state'">
                        <div v-html="$util.skyeyeClassEnumUtil.getEnumDataNameByCodeAndKeyStatic(
                            initEnumData['flowableStateEnum'],
                            'id',
                            record.state,
                            'name'
                        )
                            "></div>
                    </template>
                    <template v-if="column.key === 'action'">
                        <SkSpace>
                            <template v-if="record.editRow == 1">
                                <!-- 提交审批 -->
                                <a v-if="$config.auth('1596947135323')" @click="handleConfirmOk(record)">
                                    {{ $t("common.submitApproval") }}
                                </a>
                                <SkDivider v-if="$config.auth('1596947135323')" type="vertical" />

                                <!-- 编辑 -->
                                <a v-if="$config.auth('1596947245794')" @click="handleEdit(record, 'edit')">
                                    {{ $t("common.edit") }}
                                </a>
                                <SkDivider v-if="$config.auth('1596947245794')" type="vertical" />

                                <!-- 作废 -->
                                <SkPopconfirm v-if="$config.auth('1596947118305')"
                                    :title="$t('common.abandonedConfirm')" @confirm="handleAbandoned(record)"
                                    :okText="$t('common.abandoned')" :cancelText="$t('common.cancel')">
                                    <a class="danger-link">{{ $t("common.abandoned") }}</a>
                                </SkPopconfirm>
                            </template>

                            <template v-if="record.editRow == 2">
                                <!-- 撤销 -->
                                <SkPopconfirm v-if="$config.auth('1596947060572')" :title="$t('common.revokeConfirm')"
                                    @confirm="handleRevoke(record)" :okText="$t('common.confirm')"
                                    :cancelText="$t('common.cancel')">
                                    <a class="danger-link">{{ $t("common.revoke") }}</a>
                                </SkPopconfirm>
                            </template>
                        </SkSpace>
                    </template>
                </template>
            </SkTable>

            <SkModal v-model="modalVisible" :width="modalWidth" :title="modalTitle">
                <ShowIndex ref="showIndexRef" v-if="
                    modalType === 'add' ||
                    modalType === 'edit' ||
                    modalType === 'details'
                " :pageId="operatorParams.pageId" :params="operatorParams.params" @close="handleModalClick">
                </ShowIndex>
                <ApprovalPersonSelect v-if="modalType === 'approval'" :actKey="currentRecord?.serviceClassName"
                    :businessData="null" @submit="handleApprovalPersonSubmit" @cancel="handleModalCancel" />
            </SkModal>
        </SkCard>
    </div>
</template>
<script setup>
import { reactive, ref, getCurrentInstance, onMounted, nextTick } from "vue";
import { useI18n } from "vue-i18n";
import SkForm from "@/components/SkForm/index.vue";
import SkInput from "@/components/SkInput/index.vue";
import SkCard from "@/components/SkCard/index.vue";
import SkButton from "@/components/SkButton/index.vue";
import SkSpace from "@/components/SkSpace/index.vue";
import SkTable from "@/components/SkTable/index.vue";
import SkDivider from "@/components/SkDivider/index.vue";
import SkPopconfirm from "@/components/SkPopconfirm/index.vue";
import { SkMessage } from "@/components/SkMessage/index.vue";
import SkModal from "@/components/SkModal/index.vue";
import ShowIndex from "@/views/dsForm/show/index.vue";
import ApprovalPersonSelect from "@/views/system/submitApproval/approvalPersonSelect.vue";
import ProcessDetail from "@/views/dsForm/process/detail.vue";

const { proxy } = getCurrentInstance();
const { t } = useI18n();

// 搜索表单数据
const searchForm = reactive({
    keyword: "",
});

// 表格列配置
const columns = ref([
    {
        title: t("common.serialNum"),
        dataIndex: "index",
        width: 80,
        align: "center",
        fixed: "left",
        customRender: ({ index }) => {
            // 计算实际序号 = (当前页码 - 1) * 每页条数 + 当前行索引 + 1
            return (pagination.current - 1) * pagination.pageSize + index + 1;
        },
    },
    {
        title: "单号",
        dataIndex: "oddNumber",
        width: 200,
        align: "center",
    },
    {
        title: "标题",
        dataIndex: "title",
        width: 220,
    },
    {
        title: "流程ID",
        dataIndex: "processInstanceId",
        width: 200,
        align: "center",
    },
    {
        title: "状态",
        dataIndex: "state",
        width: 120,
    },
    {
        title: "创建人",
        dataIndex: "createName",
        width: 140,
    },
    {
        title: "创建时间",
        dataIndex: "createTime",
        width: 150,
        align: "center",
    },
    {
        title: "最后修改人",
        dataIndex: "lastUpdateName",
        width: 140,
    },
    {
        title: "最后修改时间",
        dataIndex: "lastUpdateTime",
        width: 150,
        align: "center",
    },
    {
        title: "操作",
        key: "action",
        width: 220,
        align: "center",
        fixed: "right",
    },
]);

const tableData = ref([]); // 表格数据
const loading = ref(false); // 加载状态
const tableReady = ref(false);

// 分页配置
const pagination = reactive(proxy.$config.pagination());

// 初始化枚举数据
const initEnumData = ref({});
const getInitData = async () => {
    let result = await proxy.$util.getEnumListMapByCode(["flowableStateEnum"]);
    initEnumData.value = result;
};

// 获取数据
const fetchData = async () => {
    loading.value = true;
    try {
        // 构建查询参数，添加 keyword
        const params = {
            page: Number(pagination.current || 1),
            limit: Number(pagination.pageSize || 10),
            keyword: searchForm.keyword?.trim() || "",
        };

        // 发送查询请求
        const res = await proxy.$http.post(
            proxy.$config.getConfig().admBasePath + "conferenceroomreserve001",
            params
        );

        tableData.value = res.rows || [];
        pagination.total = res.total || 0;
    } catch (error) {
        SkMessage.error("获取数据失败");
        tableData.value = [];
        pagination.total = 0;
    } finally {
        loading.value = false;
    }
};

// 事件处理 搜索
const handleSearch = () => {
    pagination.current = 1; // 重置到第一页
    fetchData();
};

// 添加重置处理函数
const handleReset = () => {
    searchForm.keyword = ""; // 清空搜索关键字
    pagination.current = 1; // 重置到第一页
    fetchData();
};

// 表格变化处理
const handleTableChange = (pag, filters, sorter) => {
    if (pag) {
        pagination.current = Number(pag.current || 1);
        pagination.pageSize = Number(pag.pageSize || 10);
    }
    fetchData();
};

const modalVisible = ref(false);
const modalWidth = ref("70%");
const modalTitle = ref("审批人选择");
const modalType = ref("approval");
const whetherCustomerData = ref(false);
const operatorParams = ref({});

// 添加
const handleAdd = () => {
    modalTitle.value = "新增";
    modalType.value = "add";
    whetherCustomerData.value = false;
    modalVisible.value = true;
    operatorParams.value = {
        pageId: "FP2023062100001",
        params: {},
    };
};

// 编辑
const handleEdit = (record) => {
    modalTitle.value = "编辑";
    modalType.value = "edit";
    whetherCustomerData.value = false;
    modalVisible.value = true;
    operatorParams.value = {
        pageId: "FP2023062100002",
        params: {
            id: record.id,
        },
    };
};

// 详情
const handleDetail = (record) => {
    modalTitle.value = "详情";
    modalType.value = "details";
    whetherCustomerData.value = false;
    modalVisible.value = true;
    operatorParams.value = {
        pageId: "FP2023062100003",
        params: {
            id: record.id,
        },
    };
};

// 弹窗关闭
const handleModalClick = (isSubmit) => {
    modalVisible.value = false;
    if (isSubmit) {
        fetchData(); // 刷新表格数据
    }
};

// 作废
const handleAbandoned = async (record) => {
    try {
        const params = {
            id: record.id,
        };
        await proxy.$http.post(
            proxy.$config.getConfig().admBasePath + "conferenceroomreserve007",
            params
        );
        SkMessage.success("作废成功");
        fetchData(); // 刷新数据
    } catch (error) {
        SkMessage.error("作废失败");
    }
};

// 当前行记录
const currentRecord = ref(null);

// 修改提交审批处理方法
const handleConfirmOk = async (record) => {
    try {
        await nextTick();
        modalTitle.value = "提交审批";
        modalType.value = "approval";
        // 打开审批人选择弹窗
        currentRecord.value = record;
        modalVisible.value = true;
    } catch (error) {
        SkMessage.error("操作失败");
    }
};

// 处理弹窗取消
const handleModalCancel = async () => {
    await nextTick();
    modalVisible.value = false;
};

// 处理审批人选择提交
const handleApprovalPersonSubmit = async (person) => {
    try {
        // 构建参数，与旧系统保持一致
        const params = {
            id: currentRecord.value.id,
            approvalId: person.id,
        };

        // 发送提交请求
        await proxy.$http.post(
            proxy.$config.getConfig().admBasePath + "conferenceroomreserve006",
            params
        );

        // 提交成功
        SkMessage.success("提交成功");
        // 关闭弹窗
        modalVisible.value = false;
        // 刷新数据
        await fetchData();
    } catch (error) {
        SkMessage.error("提交失败");
    }
};

// 撤销处理
const handleRevoke = async (record) => {
    try {
        const params = {
            processInstanceId: record.processInstanceId,
        };

        await proxy.$http.put(
            proxy.$config.getConfig().admBasePath + "conferenceroomreserve010",
            params
        );

        SkMessage.success("撤销成功");
        fetchData(); // 刷新数据
    } catch (error) {
        SkMessage.error("撤销失败");
    }
};

// 初始化
onMounted(async () => {
    await nextTick();
    requestAnimationFrame(() => {
        tableReady.value = true;
    });
    await getInitData();
    fetchData();
});
</script>
<style scoped></style>