<template>
    <div class="conference-container">
        <!-- 快捷键提示 -->
        <a-tooltip placement="right">
            <template #title>
                <div>
                    <p>快捷键：</p>
                    <p>Alt + M：静音/取消静音</p>
                    <p>Alt + V：开启/关闭视频</p>
                    <p>Alt + R：开始/停止录制</p>
                    <p>Alt + S：共享屏幕</p>
                    <p>Alt + L：离开会议</p>
                </div>
            </template>
            <QuestionCircleOutlined class="help-icon" />
        </a-tooltip>

        <!-- 状态监控 -->
        <div class="stats-panel" v-if="showStats">
            <SkConferenceStats :stats="conferenceStats" />
        </div>

        <!-- 左侧视频区域 -->
        <div class="video-section">
            <div class="main-video">
                <video ref="localVideo" autoplay muted playsinline></video>
                <div class="video-info">
                    <span>{{ userName }} (我)</span>
                </div>
            </div>
            <div class="remote-videos" v-if="remoteStreams.length">
                <div v-for="stream in remoteStreams" :key="stream.id" class="remote-video">
                    <video :ref="'remoteVideo_' + stream.id" autoplay playsinline></video>
                    <div class="video-info">
                        <span>{{ stream.userName }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧聊天区域 -->
        <div class="chat-section">
            <div class="chat-messages" ref="chatMessages">
                <div v-for="(msg, index) in chatMessages" :key="index"
                    :class="['message', msg.userId === userId ? 'my-message' : '']">
                    <div class="message-info">
                        <span class="user-name">{{ msg.userName }}</span>
                        <span class="time">{{ $util.formatTimeZhCn(msg.time) }}</span>
                    </div>
                    <div class="message-content">{{ msg.content }}</div>
                </div>
            </div>
            <div class="chat-input">
                <SkInput v-model="messageInput" placeholder="请输入消息" @keyup.enter="sendMessage" />
                <SkButton type="primary" @click.prevent="sendMessage">发送</SkButton>
            </div>
        </div>

        <!-- 底部控制栏 -->
        <div class="control-bar">
            <SkSpace>
                <SkButton :type="isAudioEnabled ? 'primary' : 'default'" @click.prevent="toggleAudio">
                    <template #icon>
                        <AudioOutlined v-if="isAudioEnabled" />
                        <AudioMutedOutlined v-else />
                    </template>
                    {{ isAudioEnabled ? '静音' : '取消静音' }}
                </SkButton>
                <SkButton :type="isVideoEnabled ? 'primary' : 'default'" @click.prevent="toggleVideo">
                    <template #icon>
                        <VideoCameraOutlined v-if="isVideoEnabled" />
                        <VideoCameraAddOutlined v-else />
                    </template>
                    {{ isVideoEnabled ? '关闭视频' : '开启视频' }}
                </SkButton>
                <SkButton :type="isRecording ? 'danger' : 'primary'" @click.prevent="toggleRecording">
                    <template #icon>
                        <PlayCircleOutlined :class="{ 'recording': isRecording }" />
                    </template>
                    {{ isRecording ? '停止录制' : '开始录制' }}
                </SkButton>
                <SkButton type="primary" @click.prevent="shareScreen">
                    <template #icon>
                        <DesktopOutlined />
                    </template>
                    共享屏幕
                </SkButton>
                <SkButton danger @click.prevent="leaveConference">
                    <template #icon>
                        <PoweroffOutlined />
                    </template>
                    离开会议
                </SkButton>
                <SkButton @click.prevent="toggleStats">
                    <template #icon>
                        <LineChartOutlined />
                    </template>
                    {{ showStats ? '隐藏统计' : '显示统计' }}
                </SkButton>
            </SkSpace>
        </div>

        <!-- 录制文件下载弹窗 -->
        <SkModal v-model="showDownloadModal" title="录制文件下载" @ok="handleDownload">
            <div class="download-list">
                <div v-for="(file, index) in recordedFiles" :key="index" class="download-item">
                    <span>{{ file.name }}</span>
                    <span>{{ $util.formatFileSize(file.size) }}</span>
                    <SkButton type="link" @click="downloadFile(file)">下载</SkButton>
                </div>
            </div>
        </SkModal>
    </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch } from 'vue'
import {
    AudioOutlined, AudioMutedOutlined, VideoCameraOutlined,
    VideoCameraAddOutlined, PlayCircleOutlined, DesktopOutlined,
    PoweroffOutlined, LineChartOutlined,
    QuestionCircleOutlined
} from '@ant-design/icons-vue'
import { Conference } from '@/plugins/conference'
import { SkMessage } from '@/components/SkMessage/index.vue'
import SkConferenceStats from '@/components/SkConferenceStats/index.vue'
import SkInput from '@/components/SkInput/index.vue'
import SkButton from '@/components/SkButton/index.vue'
import SkSpace from '@/components/SkSpace/index.vue'
import SkModal from '@/components/SkModal/index.vue'

const conference = new Conference()
const localVideo = ref(null)
const remoteStreams = ref([])
const chatMessages = ref([])
const messageInput = ref('')
const isAudioEnabled = ref(true)
const isVideoEnabled = ref(true)
const isRecording = ref(false)
const showDownloadModal = ref(false)
const recordedFiles = ref([])
const statsTimer = ref(null)

const userId = ref('')
const userName = ref('')

const showStats = ref(false)
const conferenceStats = ref({})

// 键盘快捷键处理
const handleKeyPress = (e) => {
    if (e.altKey) {
        switch (e.key.toLowerCase()) {
            case 'm':
                toggleAudio()
                break
            case 'v':
                toggleVideo()
                break
            case 'r':
                toggleRecording()
                break
            case 's':
                shareScreen()
                break
            case 'l':
                leaveConference()
                break
        }
    }
}

// 初始化会议
onMounted(async () => {
    try {
        await conference.init({
            localVideo: localVideo.value,
            onRemoteStream: handleRemoteStream,
            onMessage: handleMessage,
            onRecordComplete: handleRecordComplete
        })

        userId.value = conference.userId
        userName.value = conference.userName
    } catch (error) {
        SkMessage.error('初始化会议失败')
    }

    // 添加键盘事件监听
    window.addEventListener('keydown', handleKeyPress)

    // 定时更新统计信息
    statsTimer.value = setInterval(async () => {
        await conference.collectMediaStats()
        conferenceStats.value = conference.stats
    }, 1000)
})

// 处理远程流
const handleRemoteStream = (stream) => {
    remoteStreams.value.push(stream)
}

// 处理消息
const handleMessage = (message) => {
    chatMessages.value.push(message)
    scrollToBottom()
}

// 发送消息
const sendMessage = () => {
    if (!messageInput.value.trim()) return

    conference.sendMessage({
        content: messageInput.value,
        userId: userId.value,
        userName: userName.value,
        time: Date.now()
    })
    messageInput.value = ''
}

// 切换音频
const toggleAudio = () => {
    isAudioEnabled.value = !isAudioEnabled.value
    conference.toggleAudio(isAudioEnabled.value)
}

// 切换视频
const toggleVideo = () => {
    isVideoEnabled.value = !isVideoEnabled.value
    conference.toggleVideo(isVideoEnabled.value)
}

// 切换录制
const toggleRecording = async () => {
    try {
        if (isRecording.value) {
            const files = await conference.stopRecording()
            recordedFiles.value = files
            showDownloadModal.value = true
        } else {
            await conference.startRecording()
            isRecording.value = true
        }
    } catch (error) {
        SkMessage.error('录制操作失败')
        isRecording.value = false
    }
}

// 处理录制完成
const handleRecordComplete = (file) => {
    recordedFiles.value.push(file)
    showDownloadModal.value = true
}

// 处理下载
const handleDownload = () => {
    try {
        recordedFiles.value.forEach(file => {
            downloadFile(file)
        })
        showDownloadModal.value = false
    } catch (error) {
        SkMessage.error('下载失败')
    }
}

// 下载单个文件
const downloadFile = (file) => {
    try {
        const url = URL.createObjectURL(file)
        const a = document.createElement('a')
        a.href = url
        a.download = file.name
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        URL.revokeObjectURL(url)
    } catch (error) {
        SkMessage.error('文件下载失败')
    }
}

// 监听录制状态变化
watch(isRecording, (newValue) => {
    if (!newValue) {
        recordedFiles.value = []
    }
})

// 共享屏幕
const shareScreen = async () => {
    try {
        await conference.shareScreen()
        SkMessage.success('屏幕共享已开启')
    } catch (error) {
        SkMessage.error(error.message)
    }
}

// 离开会议
const leaveConference = async () => {
    try {
        // 如果正在录制，先停止录制
        if (isRecording.value) {
            try {
                await conference.stopRecording()
                isRecording.value = false
            } catch (error) {
                console.warn('停止录制失败:', error)
            }
        }

        // 清理定时器
        if (statsTimer.value) {
            clearInterval(statsTimer.value)
            statsTimer.value = null
        }

        await conference.leave()
        // 处理离开后的逻辑，比如跳转路由
        router.push('/admin-assistant')
    } catch (error) {
        console.error('离开会议失败:', error)
        SkMessage.error('离开会议失败')
    }
}

// 聊天区域滚动到底部
const scrollToBottom = () => {
    const chatArea = chatMessages.value
    if (chatArea) {
        chatArea.scrollTop = chatArea.scrollHeight
    }
}

// 监听效果设置变化
const toggleStats = () => {
    showStats.value = !showStats.value
}

onBeforeUnmount(() => {
    try {
        // 如果正在录制，先停止录制
        if (isRecording.value) {
            try {
                conference.stopRecording()
                isRecording.value = false
            } catch (error) {
                console.warn('停止录制失败:', error)
            }
        }

        // 清理定时器
        if (statsTimer.value) {
            clearInterval(statsTimer.value)
            statsTimer.value = null
        }

        // 清理会议资源
        conference.cleanup()
    } catch (error) {
        console.error('清理资源失败:', error)
    }

    // 移除键盘事件监听
    window.removeEventListener('keydown', handleKeyPress)
})
</script>

<style lang="less" scoped>
.conference-container {
    display: flex;
    height: calc(100% - 40px);
    padding: 20px;
    background: #f0f2f5;

    @media (max-width: 768px) {
        flex-direction: column;
        padding: 10px;

        .video-section {
            margin-right: 0;
            margin-bottom: 20px;
        }

        .chat-section {
            width: 100%;
            height: 300px;
        }

        .control-bar {
            padding: 10px;

            .ant-space {
                flex-wrap: wrap;
                justify-content: center;
                gap: 8px !important;
            }
        }

        .stats-panel {
            width: calc(100% - 20px);
            top: 10px;
            right: 10px;
        }
    }

    .video-section {
        flex: 1;
        margin-right: 20px;

        .main-video {
            width: 100%;
            height: 100%;
            margin-bottom: 20px;
            background: #000;
            position: relative;

            video {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
        }

        .remote-videos {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 10px;

            .remote-video {
                aspect-ratio: 16/9;
                background: #000;
                position: relative;

                video {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
            }
        }

        .video-info {
            position: absolute;
            bottom: 10px;
            left: 10px;
            color: #fff;
            background: rgba(0, 0, 0, 0.5);
            padding: 5px 10px;
            border-radius: 4px;
        }
    }

    .chat-section {
        width: 300px;
        display: flex;
        flex-direction: column;
        background: #fff;
        border-radius: 8px;

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;

            .message {
                margin-bottom: 15px;

                &.my-message {
                    text-align: right;

                    .message-content {
                        background: #1890ff;
                        color: #fff;
                    }
                }

                .message-info {
                    margin-bottom: 5px;
                    font-size: 12px;
                    color: #999;
                }

                .message-content {
                    display: inline-block;
                    padding: 8px 12px;
                    background: #f0f0f0;
                    border-radius: 4px;
                    max-width: 80%;
                    word-break: break-all;
                }
            }
        }

        .chat-input {
            padding: 10px;
            border-top: 1px solid #f0f0f0;
            display: flex;
            gap: 10px;
        }
    }

    .control-bar {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 20px;
        background: rgba(255, 255, 255, 0.9);
        display: flex;
        justify-content: center;
    }
}

.download-list {
    .download-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
            border-bottom: none;
        }
    }
}

.recording {
    color: #ff4d4f;
    animation: blink 1s infinite;
}

@keyframes blink {
    0% {
        opacity: 1;
    }

    50% {
        opacity: 0.5;
    }

    100% {
        opacity: 1;
    }
}

.stats-panel {
    position: fixed;
    top: 20px;
    right: 20px;
    width: 300px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    z-index: 100;
}

.help-icon {
    position: fixed;
    font-size: 20px;
    color: #666;
    cursor: pointer;
    z-index: 100;
}
</style>