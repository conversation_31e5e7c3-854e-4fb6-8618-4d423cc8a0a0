<template>
    <div class="container-manage">
        <SkCard ref="cardRef" :bordered="false">
            <SkAlert message="温馨提示：只有状态为空的条形码可以删除。" show-icon />
            <div class="table-search">
                <SkForm layout="inline" v-model="searchForm" @submit="handleSearch" @reset="handleReset"
                    :submitText="$t('common.search')" :resetText="$t('common.reset')" :isFormSubmit="false">
                    <template #submitIcon>
                        <search-outlined />
                    </template>
                    <a-form-item name="keyword">
                        <SkInput v-model="searchForm.keyword" placeholder="请输入资产编号" allowClear
                            @pressEnter="handleSearch" />
                    </a-form-item>
                </SkForm>
            </div>
            <div class="table-operations">
                <SkSpace>
                    <!-- 批量复制 -->
                    <SkButton type="primary" @click.prevent="handleBatchCopy">
                        <template #icon><copy-outlined /></template>
                        {{ $t('common.batchCopy') }}
                    </SkButton>
                    <!-- 新增 -->
                    <SkButton type="primary" @click.prevent="handleAdd">
                        <template #icon><plus-outlined /></template>
                        {{ $t('common.add') }}
                    </SkButton>
                    <SkButton type="primary" @click.prevent="fetchData">
                        <template #icon>
                            <ReloadOutlined />
                        </template>
                        {{ $t('common.refresh') }}
                    </SkButton>
                </SkSpace>
            </div>
            <SkTable :columns="columns" :data-source="tableData" :loading="loading" :pagination="pagination"
                :ready="tableReady" @change="handleTableChange">
                <template #bodyCell="{ column, record }">
                    <!-- 资产来源 -->
                    <template v-if="column.dataIndex === 'fromId'">
                        {{ initDictData['ADM_ASSET_FROM'][record.fromId] }}
                    </template>
                    <!-- 条形码 -->
                    <template v-if="column.dataIndex === 'barCodeMation'">
                        <SkTableImg :imgPath="record.barCodeMation?.imagePath" width="100px" />
                    </template>
                    <!-- 状态 -->
                    <template v-if="column.dataIndex === 'state'">
                        <div
                            v-html="$util.skyeyeClassEnumUtil.getEnumDataNameByCodeAndKeyStatic(initEnumData['assetReportState'], 'id', record.state, 'name')">
                        </div>
                    </template>
                    <template v-if="column.key === 'action'">
                        <SkSpace>
                            <!-- 编辑 -->
                            <a @click="handleEdit(record)"> {{ $t("common.edit") }} </a>
                            <SkDivider type="vertical" />
                            <!-- 删除 -->
                            <SkPopconfirm v-if="$util.isNull(record.state)" :title="$t('common.deleteConfirm')"
                                @confirm="handleDelete(record)" :okText="$t('common.delete')"
                                :cancelText="$t('common.cancel')">
                                <a class="danger-link">{{ $t('common.delete') }}</a>
                            </SkPopconfirm>
                        </SkSpace>
                    </template>
                </template>
            </SkTable>

            <SkModal v-model="modalVisible" :title="modalTitle">
                <ShowIndex ref="showIndexRef" v-if="modalType === 'edit'" :pageId="operatorParams.pageId"
                    :params="operatorParams.params" @close="handleModalClick">
                </ShowIndex>
                <SkAseetCopy v-if="modalType === 'copy'" :assetId="props.params.id" @cancel="modalVisible = false" />
                <Add v-if="modalType === 'add'" :assetId="props.params.id" @cancel="handleAddSubmit">
                </Add>
            </SkModal>
        </SkCard>
    </div>

</template>
<script setup>
import { reactive, ref, getCurrentInstance, onMounted, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import SkForm from '@/components/SkForm/index.vue'
import SkInput from '@/components/SkInput/index.vue'
import SkCard from '@/components/SkCard/index.vue'
import SkButton from '@/components/SkButton/index.vue'
import SkSpace from '@/components/SkSpace/index.vue'
import SkTable from '@/components/SkTable/index.vue'
import SkPopconfirm from '@/components/SkPopconfirm/index.vue'
import SkTableImg from '@/components/SkTableImg/index.vue'
import { SkMessage } from '@/components/SkMessage/index.vue'
import SkModal from '@/components/SkModal/index.vue'
import SkAlert from '@/components/SkAlert/index.vue'
import SkDivider from "@/components/SkDivider/index.vue"
import ShowIndex from "@/views/dsForm/show/index.vue"
import SkAseetCopy from '@/components/SkAseetCopy/index.vue'
import Add from '@/views/admin-assistant/assetReportManage/assetDetailsAdd.vue'

const { proxy } = getCurrentInstance()
const { t } = useI18n()

const props = defineProps({
    // 包含资产id
    params: {
        type: Object,
        default: {}
    }
})

// 搜索表单数据
const searchForm = reactive({
    keyword: ''
})

// 表格列配置
const columns = ref([
    {
        title: t('common.serialNum'),
        dataIndex: 'index',
        width: 80,
        align: 'center',
        fixed: 'left',
        customRender: ({ index }) => {
            // 计算实际序号 = (当前页码 - 1) * 每页条数 + 当前行索引 + 1
            return (pagination.current - 1) * pagination.pageSize + index + 1
        }
    },
    {
        title: '资产名称',
        dataIndex: 'assetMation',
        width: 150,
        customRender: ({ record }) => {
            return record.assetMation?.name
        }
    },
    {
        title: '资产编号',
        dataIndex: 'assetNum',
        width: 170
    },
    {
        title: '采购单价',
        dataIndex: 'unitPrice',
        width: 100,
    },
    {
        title: '资产来源',
        dataIndex: 'fromId',
        width: 130
    },
    {
        title: '条形码',
        dataIndex: 'barCodeMation',
        width: 150
    },
    {
        title: '状态',
        dataIndex: 'state',
        width: 100
    },
    {
        title: '入库状态',
        dataIndex: 'state1',
        width: 100,
        customRender: ({ record }) => {
            if (proxy.$util.isNull(record.state)) {
                return '未入库'
            }
            return '已入库'
        }
    },
    {
        title: '管理员',
        dataIndex: 'assetAdminMation',
        width: 140,
        customRender: ({ record }) => {
            return record.assetAdminMation?.name
        }
    },
    {
        title: '申领人',
        dataIndex: 'useUserMation',
        width: 140,
        customRender: ({ record }) => {
            return record.useUserMation?.name
        }
    },
    {
        title: '存放区域',
        dataIndex: 'storageArea',
        width: 140
    },
    {
        title: '创建人',
        dataIndex: 'createName',
        width: 140
    },
    {
        title: '创建时间',
        dataIndex: 'createTime',
        width: 150,
        align: 'center',
    },
    {
        title: '最后修改人',
        dataIndex: 'lastUpdateName',
        width: 140
    },
    {
        title: '最后修改时间',
        dataIndex: 'lastUpdateTime',
        width: 150,
        align: 'center'
    },
    {
        title: '操作',
        key: 'action',
        width: 150,
        align: 'center',
        fixed: 'right'
    }
])

const tableData = ref([]) // 表格数据
const loading = ref(false) // 加载状态
const tableReady = ref(false)
const operatorParams = ref({});

// 分页配置
const pagination = reactive(proxy.$config.pagination())

// 初始化数据字典数据
const initDictData = ref({})
// 初始化枚举数据
const initEnumData = ref({})
const getInitData = async () => {
    let enumResult = await proxy.$util.getEnumListMapByCode(['assetReportState']);
    initEnumData.value = enumResult

    let dictResult = await proxy.$util.getDictListMapByCode(['ADM_ASSET_FROM']);
    initDictData.value = dictResult
}

// 获取数据
const fetchData = async () => {
    loading.value = true
    try {
        // 构建查询参数，添加 keyword
        const params = {
            page: Number(pagination.current || 1),
            limit: Number(pagination.pageSize || 10),
            assetId: props.params.id,
            keyword: searchForm.keyword?.trim() || ''
        }

        const res = await proxy.$http.post(
            proxy.$config.getConfig().admBasePath + 'queryAssetReportList',
            params
        )

        tableData.value = res.rows || []
        pagination.total = res.total || 0
    } catch (error) {
        SkMessage.error('获取数据失败')
        tableData.value = []
        pagination.total = 0
    } finally {
        loading.value = false
    }
}

// 事件处理 搜索
const handleSearch = () => {
    pagination.current = 1 // 重置到第一页
    fetchData()
}

// 添加重置处理函数
const handleReset = () => {
    searchForm.keyword = '' // 清空搜索关键字
    pagination.current = 1 // 重置到第一页
    fetchData()
}

// 表格变化处理
const handleTableChange = (pag) => {
    if (pag) {
        pagination.current = Number(pag.current || 1)
        pagination.pageSize = Number(pag.pageSize || 10)
    }
    fetchData()
}

const modalVisible = ref(false)
const modalTitle = ref('')
const modalType = ref('')

// 添加
const handleAdd = () => {
    modalTitle.value = '新增'
    modalType.value = 'add'
    modalVisible.value = true
}

// 批量复制
const handleBatchCopy = () => {
    modalTitle.value = '批量复制'
    modalType.value = 'copy'
    modalVisible.value = true
}

// 弹窗关闭
const handleModalClick = (isSubmit) => {
    modalVisible.value = false
    if (isSubmit) {
        fetchData() // 刷新表格数据
    }
}

// 编辑
const handleEdit = (record) => {
    modalTitle.value = "编辑"
    modalType.value = "edit"
    modalVisible.value = true
    operatorParams.value = {
        pageId: "FP2025022200001",
        params: {
            id: record.id,
        }
    }
}

// 处理添加提交
const handleAddSubmit = (isSubmit) => {
    modalVisible.value = false
    fetchData() // 刷新表格数据
}

// 删除
const handleDelete = async (record) => {
    try {
        const params = {
            id: record.id
        }
        await proxy.$http.delete(
            proxy.$config.getConfig().admBasePath + 'deleteAssetReportById',
            params
        )
        SkMessage.success('删除成功')
        fetchData() // 刷新数据
    } catch (error) {
        SkMessage.error('删除失败')
    }
}

// 初始化
onMounted(async () => {
    await nextTick()
    requestAnimationFrame(() => {
        tableReady.value = true
    })

    await getInitData()
    fetchData()
})
</script>
<style scoped></style>