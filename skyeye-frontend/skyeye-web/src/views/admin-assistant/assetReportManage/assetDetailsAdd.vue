<template>
    <div class="container-manage">
        <SkForm ref="formRef" v-model="formData" :rules="formRules" @submit="handleSubmit" @reset="handleCancel"
            submitText="保存" resetText="取消" :showReset="true" :showButtons="true" class="asset-form">
            <SkHrTitle>基本信息</SkHrTitle>
            <!-- 资产明细表格 -->
            <div class="table-section">
                <SkDynamicTable ref="dynamicTableRef" v-model="tableData" :columns="columns" :row-key="'id'"
                    :showToolbar="true" :showAdd="true" :showDelete="true" @add="handleAdd" @delete="handleDelete">
                    <template #cell-select-assetId="{ record, index }">
                        <SkAssetSelect v-model="record.assetId" placeholder="请选择资产" :formData="record"
                            :attrKey="'rows[' + index + '].assetId'"
                            @change="(val) => handleAssetChange(val, record, index)" />
                    </template>
                    <template #cell-input-operNumber="{ record }">
                        <SkInputNumber v-model="record.operNumber" placeholder="请输入条形码数量" :min="1" :precision="0" />
                    </template>
                </SkDynamicTable>
            </div>
        </SkForm>
    </div>
</template>

<script setup>
import { ref, reactive, getCurrentInstance, onMounted } from 'vue'
import SkAssetSelect from '@/components/SkAssetSelect/index.vue'
import SkInputNumber from '@/components/SkInputNumber/index.vue'
import { SkMessage } from '@/components/SkMessage/index.vue'
import SkHrTitle from '@/components/SkHrTitle/index.vue'
import SkForm from '@/components/SkForm/index.vue'
import SkDynamicTable from '@/components/SkDynamicTable/index.vue'

const { proxy } = getCurrentInstance()

const emit = defineEmits(['submit', 'cancel'])

// props定义
const props = defineProps({
    params: {
        type: Object,
        default: () => ({})
    }
})

const formRef = ref(null)
const dynamicTableRef = ref(null)
const submitLoading = ref(false)

// 表单数据
const formData = reactive({
    rows: []
})

// 选中的资产集合
const selectedAssets = reactive({})

// 资产缓存，避免重复请求
const assetCache = reactive({})

// 表格列配置
const columns = ref([
    {
        attrKey: 'assetId',
        name: '资产',
        showType: 'select',
        width: 200,
        align: 'center',
        verify: 'required'
    },
    {
        attrKey: 'operNumber',
        name: '条形码数量',
        showType: 'input',
        width: 140,
        align: 'center',
        verify: 'required|number'
    }
])

// 表格数据
const tableData = ref([])

// 处理新增行
const handleAdd = (newRow) => {
    // 获取当前行索引
    const index = tableData.value.length - 1;
    // 初始化新行数据
    newRow.trcusid = `trassetList${index}`; // 使用固定格式 + 索引，我看idea就是这样的
    newRow.assetId = undefined;
    newRow.assetMation = null;
    newRow.operNumber = 1;
    newRow.sortNo = index;
    newRow.orderBy = index;
}

// 处理删除行
const handleDelete = (keys) => {
    // 处理被删除的行
    keys.forEach(key => {
        const selectedRow = tableData.value.find(item => item.id === key)
        if (selectedRow.assetId) {
            delete selectedAssets[selectedRow.trcusid]
        }
    })
}

// 获取资产详情
const fetchAssetDetail = async (assetId) => {
    if (assetCache[assetId]) {
        return assetCache[assetId]
    }
    const res = await proxy.$http.get(
        proxy.$config.getConfig().admBasePath + 'queryAssetById',
        { id: assetId }
    )
    if (res.bean) {
        assetCache[assetId] = res.bean
        return res.bean
    }
    return null
}

// 初始化数据
const initData = async () => {
    const assetId = props.params && props.params.assetId

    if (assetId) {
        // 先添加一条空行
        tableData.value.push({
            id: 'tr' + Date.now().toString(),
            trcusid: 'trassetList0', // 使用固定格式
            assetId: undefined,
            assetMation: null,
            operNumber: 1,
            sortNo: 0,
            orderBy: 0
        })

        // 获取资产详情
        const assetInfo = await fetchAssetDetail(assetId)
        if (assetInfo) {
            // 获取刚刚添加的行
            const row = tableData.value[0]

            // 设置资产信息
            row.assetId = assetId
            row.assetMation = assetInfo

            // 将资产信息添加到选中集合中
            selectedAssets[row.trcusid] = assetInfo
        }
    } else {
        // 添加一条空行
        tableData.value.push({
            id: 'tr' + Date.now().toString(),
            trcusid: 'trassetList0', // 使用固定格式
            assetId: undefined,
            assetMation: null,
            operNumber: 1,
            sortNo: 0,
            orderBy: 0
        })
    }
}

// 资产选择变化处理
const handleAssetChange = (record, rowData) => {
    if (!record) {
        // 如果清空选择，从选中集合中移除
        if (rowData.assetId && selectedAssets[rowData.trcusid]) {
            delete selectedAssets[rowData.trcusid]
        }
        rowData.assetMation = null
        return
    }

    // 检查资产是否已选
    const assetId = record.id
    const rowId = rowData.trcusid

    // 检查是否已在其他行选中
    for (const [existingRowId, assetInfo] of Object.entries(selectedAssets)) {
        if (existingRowId !== rowId && assetInfo.id === assetId) {
            SkMessage.warning('一张单中不允许出现相同的资产信息')
            // 清空当前选择
            rowData.assetId = undefined
            rowData.assetMation = null
            return
        }
    }

    // 更新选中的资产信息
    selectedAssets[rowId] = record
    rowData.assetMation = record
}

// 检查资产ID是否在数组中已存在
const isAssetIdInArray = (assetId, array) => {
    for (let i = 0; i < array.length; i++) {
        if (array[i].assetId === assetId) {
            return true
        }
    }
    return false
}

// 表单验证规则
const formRules = {
    rows: [
        {
            required: true,
            message: '请选择资产'
        }
    ],
    operNumber: [
        {
            required: true,
            message: '请输入条形码数量'
        }
    ]
}

// 验证表单
const validateForm = async () => {
    let isValid = true
    let hasError = false
    const submitData = []

    try {
        // 首先验证SkDynamicTable
        const tableValid = await dynamicTableRef.value?.validate()
        if (!tableValid?.valid) {
            return null
        }

        // 检查每一行数据
        for (let i = 0; i < tableData.value.length; i++) {
            const row = tableData.value[i]

            // 检查资产是否已选择
            if (!row.assetId || !row.assetMation) {
                SkMessage.warning(`第 ${i + 1} 行资产未选择`)
                isValid = false
                break
            }

            // 检查条形码数量
            if (parseInt(row.operNumber) === 0) {
                SkMessage.warning('数量不能为0')
                hasError = true
                break
            }

            // 检查资产是否重复
            if (isAssetIdInArray(row.assetId, submitData)) {
                SkMessage.warning('一张单中不允许出现相同的资产信息.')
                hasError = true
                break
            }

            // 确保trcusid、sortNo、orderBy依次递增，作为入参
            const trcusid = `trassetList${i}`;

            // 添加到提交数据中
            submitData.push({
                assetId: row.assetId,
                operNumber: row.operNumber,
                trcusid: trcusid,
                sortNo: i,
                orderBy: i // orderBy与索引一致
            })
        }

        if (!isValid || hasError) {
            return null
        }

        return submitData;
    } catch (error) {
        return null;
    }
}

// 提交表单
const handleSubmit = async () => {
    const submitData = await validateForm()
    if (!submitData) {
        return; // 验证失败直接返回
    }

    try {
        submitLoading.value = true
        // 构建提交数据
        const params = {
            list: JSON.stringify(submitData)
        }
        // 生产条形码
        await proxy.$http.post(
            proxy.$config.getConfig().admBasePath + 'insertAssetReport',
            params
        )
        // 添加成功后显示消息
        SkMessage.success('添加成功');
        // 向父组件发送成功信号
        emit('cancel');
    } catch (error) {
        SkMessage.error('提交失败，请检查表单数据')
    } finally {
        submitLoading.value = false
    }
}

// 取消操作
const handleCancel = () => {
    emit('cancel')
}

// 组件挂载时初始化数据
onMounted(() => {
    initData()
})
</script>

<style scoped>
.asset-form {
    padding: 16px;
    overflow: hidden !important;
}

.table-section {
    margin-bottom: 16px;
}
</style>