<template>
  <div class="config-manager">
    <a-card title="配置管理工具" style="margin: 20px;">
      <a-space direction="vertical" style="width: 100%;">
        
        <!-- 当前配置信息 -->
        <a-card size="small" title="当前配置信息">
          <a-descriptions :column="2" bordered>
            <a-descriptions-item label="应用版本">
              {{ configInfo.version }}
            </a-descriptions-item>
            <a-descriptions-item label="缓存前缀">
              {{ configInfo.cachePrefix }}
            </a-descriptions-item>
            <a-descriptions-item label="缓存Key">
              {{ configInfo.cacheKey }}
            </a-descriptions-item>
            <a-descriptions-item label="缓存状态">
              <a-tag :color="configInfo.hasCachedConfig ? 'green' : 'red'">
                {{ configInfo.hasCachedConfig ? '已缓存' : '无缓存' }}
              </a-tag>
            </a-descriptions-item>
          </a-descriptions>
        </a-card>

        <!-- 当前配置内容 -->
        <a-card size="small" title="当前配置内容">
          <pre style="background: #f5f5f5; padding: 10px; border-radius: 4px; max-height: 300px; overflow-y: auto;">{{ JSON.stringify(currentConfig, null, 2) }}</pre>
        </a-card>

        <!-- 操作按钮 -->
        <a-card size="small" title="配置操作">
          <a-space>
            <a-button type="primary" @click="refreshConfig" :loading="loading">
              刷新配置
            </a-button>
            <a-button @click="clearCache" :loading="loading">
              清除缓存
            </a-button>
            <a-button @click="forceRefresh" :loading="loading">
              强制刷新
            </a-button>
            <a-button @click="loadConfigInfo">
              重新加载信息
            </a-button>
          </a-space>
        </a-card>

        <!-- 强制刷新说明 -->
        <a-card size="small" title="使用说明">
          <a-typography-paragraph>
            <strong>强制刷新方法：</strong>
          </a-typography-paragraph>
          <a-typography-paragraph>
            <strong>方法1：</strong> 在URL后添加参数 <code>?refreshConfig=true</code>
          </a-typography-paragraph>
          <a-typography-paragraph>
            <strong>方法2：</strong> 点击上方"强制刷新"按钮
          </a-typography-paragraph>
          <a-typography-paragraph>
            <strong>方法3：</strong> 在控制台执行 <code>$config.forceRefreshConfig()</code> 然后刷新页面
          </a-typography-paragraph>
        </a-card>

      </a-space>
    </a-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue'
import { message } from 'ant-design-vue'

const { proxy } = getCurrentInstance()
const loading = ref(false)

const configInfo = reactive({
  version: '',
  cachePrefix: '',
  cacheKey: '',
  hasCachedConfig: false
})

const currentConfig = ref({})

// 加载配置信息
const loadConfigInfo = async () => {
  try {
    loading.value = true
    
    // 获取配置信息
    configInfo.version = proxy.$config.configVersion
    configInfo.cachePrefix = proxy.$config.cachePrefix
    configInfo.cacheKey = proxy.$config.key
    
    // 检查是否有缓存
    configInfo.hasCachedConfig = !proxy.$http.cachesIsNull(proxy.$config.key)
    
    // 获取当前配置
    currentConfig.value = await proxy.$config.getConfig()
    
    message.success('配置信息加载成功')
  } catch (error) {
    console.error('加载配置信息失败:', error)
    message.error('加载配置信息失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

// 刷新配置
const refreshConfig = async () => {
  try {
    loading.value = true
    currentConfig.value = await proxy.$config.loadConfig()
    await loadConfigInfo()
    message.success('配置刷新成功')
  } catch (error) {
    console.error('刷新配置失败:', error)
    message.error('刷新配置失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

// 清除缓存
const clearCache = () => {
  try {
    loading.value = true
    proxy.$config.clearConfigCache()
    loadConfigInfo()
    message.success('缓存清除成功')
  } catch (error) {
    console.error('清除缓存失败:', error)
    message.error('清除缓存失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

// 强制刷新
const forceRefresh = () => {
  try {
    proxy.$config.forceRefreshConfig()
    message.success('已设置强制刷新标记，请刷新页面生效')
    setTimeout(() => {
      window.location.reload()
    }, 1000)
  } catch (error) {
    console.error('设置强制刷新失败:', error)
    message.error('设置强制刷新失败: ' + error.message)
  }
}

onMounted(() => {
  loadConfigInfo()
})
</script>

<style scoped>
.config-manager {
  min-height: 100vh;
  background-color: #f0f2f5;
}

code {
  background-color: #f5f5f5;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
}
</style>
