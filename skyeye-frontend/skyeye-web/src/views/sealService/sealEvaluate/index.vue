<template>
    <div class="container-manage">
        <SkCard ref="cardRef" :bordered="false">
            <SkAlert message="温馨提示：只有待评价的工单才可以进行评价操作。" show-icon />
            <div class="table-operations">
                <SkSpace>
                    <SkButton v-if="!isEvaluate" type="primary" @click.prevent="handleEvaluate">
                        <template #icon>
                            <PlusOutlined />
                        </template>
                        {{ $t('common.evaluate') }}
                    </SkButton>

                    <SkButton type="primary" @click.prevent="fetchData">
                        <template #icon>
                            <ReloadOutlined />
                        </template>
                        {{ $t('common.refresh') }}
                    </SkButton>
                </SkSpace>
            </div>
            <SkTable :columns="columns" :data-source="tableData" :loading="loading" :pagination="pagination"
                :ready="tableReady" @change="handleTableChange">
                <template #bodyCell="{ column, record }">
                    <template v-if="column.dataIndex === 'typeId'">
                        {{ initDictData['AMS_SERVICE_EVALUATE_TYPE'][record.typeId] }}
                    </template>
                </template>
            </SkTable>
        </SkCard>

        <SkModal v-model="modalVisible" :width="modalWidth" :title="modalTitle">
            <ShowIndex ref="showIndexRef" v-if="modalType === 'add'" :pageId="operatorParams.pageId"
                :params="operatorParams.params" @close="handleModalClick" @saveData="saveData">
            </ShowIndex>
        </SkModal>
    </div>

</template>
<script setup>
import { reactive, ref, getCurrentInstance, onMounted, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import SkCard from '@/components/SkCard/index.vue'
import SkButton from '@/components/SkButton/index.vue'
import SkSpace from '@/components/SkSpace/index.vue'
import SkTable from '@/components/SkTable/index.vue'
import { SkMessage } from '@/components/SkMessage/index.vue'
import SkModal from '@/components/SkModal/index.vue'
import ShowIndex from '@/views/dsForm/show/index.vue'
import SkAlert from '@/components/SkAlert/index.vue'

const { proxy } = getCurrentInstance()
const { t } = useI18n()

const props = defineProps({
    objectId: {
        type: String,
        default: ''
    },
    objectKey: {
        type: String,
        default: ''
    }
})

// 表格列配置
const columns = ref([
    {
        title: t('common.serialNum'),
        dataIndex: 'index',
        width: 80,
        align: 'center',
        fixed: 'left',
        customRender: ({ index }) => {
            // 计算实际序号 = (当前页码 - 1) * 每页条数 + 当前行索引 + 1
            return (pagination.current - 1) * pagination.pageSize + index + 1
        }
    },
    {
        title: '评价类型',
        dataIndex: 'typeId',
        width: 200
    },
    {
        title: '评价内容',
        dataIndex: 'content',
        width: 300
    },
    {
        title: '创建人',
        dataIndex: 'createName',
        width: 140
    },
    {
        title: '创建时间',
        dataIndex: 'createTime',
        width: 150,
        align: 'center',
    },
    {
        title: '最后修改人',
        dataIndex: 'lastUpdateName',
        width: 140
    },
    {
        title: '最后修改时间',
        dataIndex: 'lastUpdateTime',
        width: 150,
        align: 'center'
    }
])

const tableData = ref([]) // 表格数据
const loading = ref(false) // 加载状态
const tableReady = ref(false)

// 分页配置
const pagination = reactive(proxy.$config.pagination())

// 判断是否已经评价
const isEvaluate = ref(false)
const getIsEvaluate = async () => {
    const res = await proxy.$http.get(
        proxy.$config.getConfig().sealServiceBasePath + 'querySealServiceOrderById',
        { id: props.objectId }
    )
    if (res.bean.state !== 'beEvaluated') {
        isEvaluate.value = true
    }
}

// 初始化数据字典数据
const initDictData = ref({})
const getInitData = async () => {
    let dictResult = await proxy.$util.getDictListMapByCode(['AMS_SERVICE_EVALUATE_TYPE']);
    initDictData.value = dictResult
}

// 获取数据
const fetchData = async () => {
    loading.value = true
    try {
        // 构建查询参数，添加 keyword
        const params = {
            page: Number(pagination.current || 1),
            limit: Number(pagination.pageSize || 10),
            objectId: props.objectId,
            objectKey: props.objectKey,
        }

        const res = await proxy.$http.post(
            proxy.$config.getConfig().sealServiceBasePath + 'querySealEvaluateList',
            params
        )

        tableData.value = res.rows || []
        pagination.total = res.total || 0
    } catch (error) {
        SkMessage.error('获取数据失败')
        tableData.value = []
        pagination.total = 0
    } finally {
        loading.value = false
    }
}

// 表格变化处理
const handleTableChange = (pag, filters, sorter) => {
    if (pag) {
        pagination.current = Number(pag.current || 1)
        pagination.pageSize = Number(pag.pageSize || 10)
    }
    fetchData()
}

// 保存数据
const saveData = (callback) => {
    callback({
        objectId: props.objectId,
        objectKey: props.objectKey
    })
}

const modalVisible = ref(false)
const modalWidth = ref('70%')
const modalTitle = ref('')
const modalType = ref('')
const operatorParams = ref({})

// 评价
const handleEvaluate = async () => {
    modalTitle.value = '评价'
    modalType.value = 'add'
    modalVisible.value = true
    operatorParams.value = {
        pageId: 'FP2023091000005',
        params: {}
    }
}

// 弹窗关闭
const handleModalClick = (isSubmit) => {
    modalVisible.value = false
    if (isSubmit) {
        isEvaluate.value = true
        fetchData() // 刷新表格数据
    }
}

// 初始化
onMounted(async () => {
    await nextTick()
    requestAnimationFrame(() => {
        tableReady.value = true
    })
    await getIsEvaluate()
    await getInitData()
    fetchData()
})
</script>

<style scoped></style>