<template>
    <div class="container-manage">
        <SkCard ref="cardRef" :bordered="false">
            <!-- 地图容器 -->
            <div class="map-container" ref="mapContainer"></div>
        </SkCard>
    </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, getCurrentInstance } from 'vue'
import AMapLoader from '@amap/amap-jsapi-loader'
import SkCard from '@/components/SkCard/index.vue'

const { proxy } = getCurrentInstance()

// 地图相关变量
const mapContainer = ref(null)
const map = ref(null)
const markers = ref([])
const infoWindow = ref(null)

// 初始化地图
const initMap = async () => {
    try {
        const AMap = await AMapLoader.load({
            key: proxy.$config.getConfig().skyeyeMapKey, // 替换为你的高德地图 key
            version: '2.0',
            plugins: ['AMap.ToolBar', 'AMap.Scale', 'AMap.HawkEye', 'AMap.MapType', 'AMap.Geolocation']
        })

        // 创建地图实例
        map.value = new AMap.Map(mapContainer.value, {
            zoom: 11,
            center: [116.397428, 39.90923], // 默认中心点
            resizeEnable: true
        })

        // 添加地图控件
        map.value.addControl(new AMap.ToolBar())
        map.value.addControl(new AMap.Scale())
        map.value.addControl(new AMap.HawkEye({ isOpen: true }))
        map.value.addControl(new AMap.MapType())
        map.value.addControl(new AMap.Geolocation())

        // 创建信息窗体
        infoWindow.value = new AMap.InfoWindow({
            isCustom: true,
            autoMove: true,
            offset: new AMap.Pixel(0, -30)
        })

        // 加载数据
        await loadWorkerData()
    } catch (error) {
        console.error('地图初始化失败:', error)
        proxy.$message.error('地图加载失败')
    }
}

// 加载人员数据
const loadWorkerData = async () => {
    try {
        const res = await proxy.$http.get(
            proxy.$config.getConfig().sealServiceBasePath + 'queryAllSealWorkerList'
        )

        // 清除现有标记
        clearMarkers()

        // 添加新标记
        res.rows.forEach(worker => {
            addMarker(worker)
        })

        // 调整地图视野以包含所有标记
        if (markers.value.length > 0) {
            map.value.setFitView()
        }
    } catch (error) {
        console.error('加载人员数据失败:', error)
        proxy.$message.error('加载人员数据失败')
    }
}

// 添加标记
const addMarker = (worker) => {
    const marker = new AMap.Marker({
        position: [worker.longitude, worker.latitude],
        icon: new AMap.Icon({
            size: new AMap.Size(25, 34),
            imageSize: new AMap.Size(25, 34),
            image: proxy.$config.getConfig().fileBasePath + worker.userMation?.userPhoto
        }),
        title: worker.name
    })

    // 点击标记时显示信息窗体
    marker.on('click', () => {
        showInfoWindow(worker, marker)
    })

    marker.setMap(map.value)
    markers.value.push(marker)
}

// 显示信息窗体
const showInfoWindow = (worker, marker) => {
    const content = `
        <div class="info-window">
            <div class="info-container">
                <div class="info-header">
                    <div class="avatar-wrapper">
                        <img class="user-avatar" 
                            src="${proxy.$config.getConfig().fileBasePath + worker.userMation?.userPhoto}" 
                            onerror="this.src='${proxy.$config.defaultUserImg}'"
                        />
                        <span class="user-status ${worker.userMation?.enabled === '1' ? 'active' : 'inactive'}"></span>
                    </div>
                    <div class="user-info">
                        <div class="user-name">${worker.userMation?.name || '未知用户'}</div>
                        <div class="user-dept">
                            <span class="dept-icon">
                                <svg viewBox="64 64 896 896" width="1em" height="1em" fill="currentColor">
                                    <path d="M880 298.4H521L403.7 186.2a8.15 8.15 0 00-5.5-2.2H144c-17.7 0-32 14.3-32 32v592c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V330.4c0-17.7-14.3-32-32-32zM840 768H184V256h188.5l119.6 114.4H840V768z"/>
                                </svg>
                            </span>
                            ${worker.userMation?.departmentName || '未知部门'}
                        </div>
                    </div>
                </div>
                <div class="info-content">
                    <div class="info-item">
                        <span class="info-icon">
                            <svg viewBox="64 64 896 896" width="1em" height="1em" fill="currentColor">
                                <path d="M877.1 238.7L770.6 132.3c-13-13-30.4-20.3-48.8-20.3s-35.8 7.2-48.8 20.3L558.3 246.8c-13 13-20.3 30.5-20.3 48.9 0 18.5 7.2 35.8 20.3 48.9l89.6 89.7a405.46 405.46 0 01-86.4 127.3c-36.7 36.9-79.6 66-127.2 86.6l-89.6-89.7c-13-13-30.4-20.3-48.8-20.3a68.2 68.2 0 00-48.8 20.3L132.3 673c-13 13-20.3 30.5-20.3 48.9 0 18.5 7.2 35.8 20.3 48.9l106.4 106.4c22.2 22.2 52.8 34.9 84.2 34.9 6.5 0 12.8-.5 19.2-1.6 132.4-21.8 263.8-92.3 369.9-198.3C818 606 888.4 474.6 910.4 342.1c6.3-37.6-6.3-76.3-33.3-103.4zm-37.6 91.5c-19.5 117.9-82.9 235.5-178.4 331s-213 158.9-330.9 178.4c-14.8 2.5-30-2.5-40.8-13.2L184.9 721.9 295.7 611l119.4 119.4 28.2-14.5c71.3-36.7 134.2-88.9 184.7-139.3l.4-.4c50.4-50.4 102.6-113.3 139.3-184.7l14.5-28.2-119.4-119.4 110.9-110.9 104.5 104.5c10.8 10.8 15.8 26 13.3 40.8z"/>
                            </svg>
                        </span>
                        <a href="tel:${worker.userMation?.phone}" class="info-text">
                            ${worker.userMation?.phone || '暂无联系方式'}
                        </a>
                    </div>
                    <div class="info-item">
                        <span class="info-icon">
                            <svg viewBox="64 64 896 896" width="1em" height="1em" fill="currentColor">
                                <path d="M512 327c-29.9 0-58 11.6-79.2 32.8A111.6 111.6 0 00400 439c0 29.9 11.7 58 32.8 79.2C454 539.3 482.1 551 512 551c29.9 0 58-11.7 79.2-32.8C612.3 497 624 468.9 624 439c0-29.9-11.7-58-32.8-79.2A111.6 111.6 0 00512 327zm342.6-37.9a362.49 362.49 0 00-79.9-115.7 370.83 370.83 0 00-118.2-77.8C610.7 76.6 562.1 67 512 67c-50.1 0-98.7 9.6-144.5 28.5-44.3 18.3-84 44.5-118.2 77.8A363.6 363.6 0 00169.4 289c-19.5 45-29.4 92.8-29.4 142 0 70.6 16.9 140.9 50.1 208.7 26.7 54.5 64 107.6 111 158.1 80.3 86.2 164.5 138.9 188.4 153a43.9 43.9 0 0022.4 6.1c7.8 0 15.5-2 22.4-6.1 23.9-14.1 108.1-66.8 188.4-153 47-50.4 84.3-103.6 111-158.1C867.1 572 884 501.8 884 431.1c0-49.2-9.9-97-29.4-142zM512 880.2c-65.9-41.9-300-207.8-300-449.1 0-77.9 31.1-151.1 87.6-206.3C356.3 169.5 431.7 139 512 139s155.7 30.5 212.4 85.9C780.9 280 812 353.2 812 431.1c0 241.3-234.1 407.2-300 449.1z"/>
                            </svg>
                        </span>
                        <span class="info-text">${worker.absoluteAddress || '暂无地址信息'}</span>
                    </div>
                </div>
            </div>
        </div>
    `

    infoWindow.value.setContent(content)
    infoWindow.value.open(map.value, marker.getPosition())
}

// 清除所有标记
const clearMarkers = () => {
    markers.value.forEach(marker => {
        marker.setMap(null)
    })
    markers.value = []
}

// 组件挂载时初始化
onMounted(() => {
    initMap()
})

// 组件卸载时清理资源
onBeforeUnmount(() => {
    if (map.value) {
        map.value.destroy()
    }
})
</script>

<style scoped>
.map-container {
    width: 100%;
    height: 100%;
    min-height: 500px;
    border: 1px solid #f0f0f0;
}

:deep(.info-window) {
    padding: 12px;
    max-width: 400px;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    background: #fff;
}

:deep(.info-container) {
    background: #fff;
}

:deep(.info-header) {
    display: flex;
    align-items: center;
    gap: 12px;
    padding-bottom: 12px;
    border-bottom: 1px solid #f0f0f0;
    margin-bottom: 12px;
}

:deep(.avatar-wrapper) {
    position: relative;
    flex-shrink: 0;
}

:deep(.user-avatar) {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    background: #f5f5f5;
}

:deep(.user-status) {
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    border: 2px solid #fff;
}

:deep(.user-status.active) {
    background-color: #52c41a;
}

:deep(.user-status.inactive) {
    background-color: #ff4d4f;
}

:deep(.user-info) {
    flex: 1;
    min-width: 0;
}

:deep(.user-name) {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

:deep(.user-dept) {
    font-size: 13px;
    color: #666;
    display: flex;
    align-items: center;
    gap: 4px;
}

:deep(.dept-icon) {
    color: #1890ff;
    display: flex;
    align-items: center;
    font-size: 16px;
}

:deep(.info-content) {
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding-top: 4px;
}

:deep(.info-item) {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #666;
    font-size: 14px;
    line-height: 1.5;
}

:deep(.info-icon) {
    color: #1890ff;
    display: flex;
    align-items: center;
    font-size: 16px;
    flex-shrink: 0;
}

:deep(.info-text) {
    flex: 1;
    min-width: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

:deep(.info-item:first-child .info-text) {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

:deep(.info-item:last-child .info-text) {
    white-space: pre-wrap;
    word-break: break-all;
}

:deep(a.info-text) {
    color: #1890ff;
    text-decoration: none;
}

:deep(a.info-text:hover) {
    color: #40a9ff;
    text-decoration: underline;
}

:deep(.info-item:last-child) {
    align-items: flex-start;
    padding-top: 4px;
}
</style>