<template>
    <div class="container-manage">
        <SkCard ref="cardRef" :bordered="false">
            <SkAlert message="温馨提示：只有待完工的工单才可以进行故障信息录入操作。" show-icon />
            <div class="table-search">
                <SkForm layout="inline" v-model="searchForm" @submit="handleSearch" @reset="handleReset"
                    :submitText="$t('common.search')" :resetText="$t('common.reset')" :isFormSubmit="false">
                    <template #submitIcon>
                        <search-outlined />
                    </template>
                    <a-form-item name="keyword">
                        <SkInput v-model="searchForm.keyword" placeholder="请输入单号" allowClear
                            @pressEnter="handleSearch" />
                    </a-form-item>
                </SkForm>
            </div>
            <div class="table-operations">
                <SkSpace>
                    <SkButton v-if="whetherWrite" type="primary" @click.prevent="handleAdd">
                        <template #icon><plus-outlined /></template>
                        {{ $t('common.add') }}
                    </SkButton>
                    <SkButton type="primary" @click.prevent="fetchData">
                        <template #icon>
                            <ReloadOutlined />
                        </template>
                        {{ $t('common.refresh') }}
                    </SkButton>
                </SkSpace>
            </div>
            <SkTable :columns="columns" :data-source="tableData" :loading="loading" :pagination="pagination"
                :ready="tableReady" @change="handleTableChange">
                <template #bodyCell="{ column, record }">
                    <template v-if="column.dataIndex === 'oddNumber'">
                        <a @click="handleDetail(record)">
                            {{ record.oddNumber }}
                        </a>
                    </template>
                    <template v-if="column.key === 'action'">
                        <SkSpace>
                            <!-- 编辑 -->
                            <a v-if="whetherWrite" @click="handleEdit(record, 'edit')">
                                {{ $t('common.edit') }}
                            </a>
                            <SkDivider v-if="whetherWrite" type="vertical" />

                            <!-- 删除 -->
                            <SkPopconfirm v-if="whetherWrite" :title="$t('common.deleteConfirm')"
                                @confirm="handleDelete(record)" :okText="$t('common.delete')"
                                :cancelText="$t('common.cancel')">
                                <a class="danger-link">{{ $t('common.delete') }}</a>
                            </SkPopconfirm>
                        </SkSpace>
                    </template>
                </template>
            </SkTable>

            <SkModal v-model="modalVisible" :width="modalWidth" :title="modalTitle">
                <ShowIndex ref="showIndexRef"
                    v-if="modalType === 'add' || modalType === 'edit' || modalType === 'details'"
                    :pageId="operatorParams.pageId" :params="operatorParams.params" @close="handleModalClick"
                    @cell-change="handleCellChange" @saveData="saveData" @handleChange="handleChange">
                    <!-- 传递自定义单元格组件 -->
                    <template #cell-chooseInput-materialId="slotProps">
                        <a-form-item-rest>
                            <SkMaterialSelect v-model="slotProps.record[slotProps.column.dataIndex]"
                                :formData="slotProps.record"
                                :isEdit="slotProps.column.getConfig(slotProps.record).isEdit"
                                :attrKey="slotProps.column.dataIndex"
                                @change="(material) => handleMaterialChange(material, slotProps.record, slotProps.column, slotProps.formData)" />
                        </a-form-item-rest>
                    </template>
                </ShowIndex>
            </SkModal>
        </SkCard>
    </div>

</template>
<script setup>
import { reactive, ref, getCurrentInstance, onMounted, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import SkForm from '@/components/SkForm/index.vue'
import SkInput from '@/components/SkInput/index.vue'
import SkCard from '@/components/SkCard/index.vue'
import SkButton from '@/components/SkButton/index.vue'
import SkSpace from '@/components/SkSpace/index.vue'
import SkTable from '@/components/SkTable/index.vue'
import SkDivider from '@/components/SkDivider/index.vue'
import SkPopconfirm from '@/components/SkPopconfirm/index.vue'
import { SkMessage } from '@/components/SkMessage/index.vue'
import SkModal from '@/components/SkModal/index.vue'
import ShowIndex from '@/views/dsForm/show/index.vue'
import SkMaterialSelect from '@/components/SkMaterialSelect/index.vue'
import SkAlert from '@/components/SkAlert/index.vue'

const { proxy } = getCurrentInstance()
const { t } = useI18n()

const props = defineProps({
    objectId: {
        type: String,
        default: ''
    },
    objectKey: {
        type: String,
        default: ''
    }
})

// 搜索表单数据
const searchForm = reactive({
    keyword: ''
})

// 表格列配置
const columns = ref([
    {
        title: t('common.serialNum'),
        dataIndex: 'index',
        width: 80,
        align: 'center',
        fixed: 'left',
        customRender: ({ index }) => {
            // 计算实际序号 = (当前页码 - 1) * 每页条数 + 当前行索引 + 1
            return (pagination.current - 1) * pagination.pageSize + index + 1
        }
    },
    {
        title: '单号',
        dataIndex: 'oddNumber',
        width: 150
    },
    {
        title: '实际开始时间',
        dataIndex: 'comStartTime',
        width: 150
    },
    {
        title: '实际结束时间',
        dataIndex: 'comEndTime',
        width: 150
    },
    {
        title: '工时',
        dataIndex: 'comWorkTime',
        width: 140
    },
    {
        title: '材料费',
        dataIndex: 'materialCost',
        width: 140
    },
    {
        title: '服务费',
        dataIndex: 'coverCost',
        width: 120
    },
    {
        title: '其他费用',
        dataIndex: 'otherCost',
        width: 120
    },
    {
        title: '总计金额',
        dataIndex: 'allPrice',
        width: 120
    },
    {
        title: '创建人',
        dataIndex: 'createName',
        width: 140
    },
    {
        title: '创建时间',
        dataIndex: 'createTime',
        width: 150,
        align: 'center',
    },
    {
        title: '最后修改人',
        dataIndex: 'lastUpdateName',
        width: 140
    },
    {
        title: '最后修改时间',
        dataIndex: 'lastUpdateTime',
        width: 150,
        align: 'center'
    },
    {
        title: '操作',
        key: 'action',
        width: 150,
        align: 'center',
        fixed: 'right'
    }
])

const tableData = ref([]) // 表格数据
const loading = ref(false) // 加载状态
const tableReady = ref(false)

// 分页配置
const pagination = reactive(proxy.$config.pagination())

// 获取工单状态
const whetherWrite = ref(false)
const getOrderState = async () => {
    const res = await proxy.$http.get(
        proxy.$config.getConfig().sealServiceBasePath + 'querySealServiceOrderById',
        { id: props.objectId }
    )
    if (res.bean.state == 'beCompleted') {
        whetherWrite.value = true
    }
}

// 获取数据
const fetchData = async () => {
    loading.value = true
    try {
        // 构建查询参数，添加 keyword
        const params = {
            page: Number(pagination.current || 1),
            limit: Number(pagination.pageSize || 10),
            objectId: props.objectId,
            objectKey: props.objectKey,
            keyword: searchForm.keyword?.trim() || '' // 添加关键字搜索参数
        }

        const res = await proxy.$http.post(
            proxy.$config.getConfig().sealServiceBasePath + 'querySealFaultList',
            params
        )

        tableData.value = res.rows || []
        pagination.total = res.total || 0
    } catch (error) {
        SkMessage.error('获取数据失败')
        tableData.value = []
        pagination.total = 0
    } finally {
        loading.value = false
    }
}

// 事件处理 搜索
const handleSearch = () => {
    pagination.current = 1 // 重置到第一页
    fetchData()
}

// 添加重置处理函数
const handleReset = () => {
    searchForm.keyword = '' // 清空搜索关键字
    pagination.current = 1 // 重置到第一页
    fetchData()
}

// 表格变化处理
const handleTableChange = (pag, filters, sorter) => {
    if (pag) {
        pagination.current = Number(pag.current || 1)
        pagination.pageSize = Number(pag.pageSize || 10)
    }
    fetchData()
}

// 保存数据
const saveData = (callback) => {
    callback({
        objectId: props.objectId,
        objectKey: props.objectKey
    })
}

const modalVisible = ref(false)
const modalWidth = ref('70%')
const modalTitle = ref('')
const modalType = ref('')
const operatorParams = ref({})

// 添加
const handleAdd = () => {
    modalTitle.value = '新增'
    modalType.value = 'add'
    modalVisible.value = true
    operatorParams.value = {
        pageId: 'FP2023090900001',
        params: {}
    }
}

// 编辑
const handleEdit = (record) => {
    modalTitle.value = '编辑'
    modalType.value = 'edit'
    modalVisible.value = true
    operatorParams.value = {
        pageId: 'FP2023090900002',
        params: {
            id: record.id
        }
    }
}

// 详情
const handleDetail = (record) => {
    modalTitle.value = '详情'
    modalType.value = 'details'
    modalVisible.value = true
    operatorParams.value = {
        pageId: 'FP2023090900003',
        params: {
            id: record.id
        }
    }
}

// 弹窗关闭
const handleModalClick = (isSubmit) => {
    modalVisible.value = false
    if (isSubmit) {
        fetchData() // 刷新表格数据
    }
}

// 删除
const handleDelete = async (record) => {
    try {
        const params = {
            id: record.id
        }
        await proxy.$http.delete(
            proxy.$config.getConfig().sealServiceBasePath + 'deleteSealFaultById',
            params
        )
        SkMessage.success('删除成功')
        fetchData() // 刷新数据
    } catch (error) {
        SkMessage.error('删除失败')
    }
}

const showIndexRef = ref(null)
// 处理单元格变化
const handleCellChange = async ({ record, dataIndex, value, column }, formData) => {
    // 处理物料选择变化
    if (dataIndex == 'materialId') {
        record.unitPrice = 0
    } else if (dataIndex == 'normsId') {
        const materialNorms = column.getConfig(record).defaultData
        const norms = materialNorms.find(item => item.id === value)
        // 获取物料的销售价
        record.unitPrice = norms.retailPrice
        const res = await proxy.$http.get(
            proxy.$config.getConfig().sealServiceBasePath + 'queryMyPartsNumByNormsId',
            { normsId: value }
        )
        if (!proxy.$util.isNull(res.bean)) {
            record.placeholder1 = res.bean.stock
        } else {
            record.placeholder1 = "00.00"
        }
    }
    // 处理金额计算
    const result = proxy.$util.erpUtil.calcMoneyKey(dataIndex, record)
    // 将result合入到record中
    Object.assign(record, result)

    calcMoney(formData)
}

// 处理表单项变化
const handleChange = ({ attrKey, formData }) => {
    if (attrKey == 'coverCost' || attrKey == 'otherCost') {
        calcMoney(formData)
    }
}

const calcMoney = (formData) => {
    // 处理金额计算
    const sealFaultUseMaterialList = formData?.sealFaultUseMaterialList || []
    let totalPrice = 0;
    if (!proxy.$util.isNull(sealFaultUseMaterialList)) {
        sealFaultUseMaterialList.forEach((item, i) => {
            totalPrice = proxy.$util.calculationUtil.sum(totalPrice, item.allPrice)
        })
    }
    formData.materialCost = totalPrice

    // 服务费信息
    totalPrice = proxy.$util.calculationUtil.sum(totalPrice, formData.coverCost)
    // 其他费用信息
    totalPrice = proxy.$util.calculationUtil.sum(totalPrice, formData.otherCost)
    formData.allPrice = totalPrice
}

// 处理物料选择变化
const handleMaterialChange = async (material, record, column, formData) => {
    const dataIndex = column.dataIndex
    const mationKey = proxy.$util.getKeyIdToMation(dataIndex)

    if (material) {
        // 更新其他相关字段
        record[mationKey] = material
        record["normsId"] = undefined
        record["unitPrice"] = 0

        // 等待组件挂载完成
        await nextTick()

        // 修改当前行的表格列配置
        showIndexRef.value?.writeComponentRef?.updateTableColumns(column.pIdDataIndex, record, {
            "normsId": {
                dataType: 1,
                defaultData: material.materialNorms
            }
        })

        // 触发 cell-change 事件
        handleCellChange({
            record,
            dataIndex: column.dataIndex,
            value: material,
            column: column
        }, formData)
    } else {
        // 清空相关字段
        delete record[mationKey]
    }
}

// 初始化
onMounted(async () => {
    await nextTick()
    requestAnimationFrame(() => {
        tableReady.value = true
    })
    await getOrderState()
    fetchData()
})
</script>
<style scoped></style>