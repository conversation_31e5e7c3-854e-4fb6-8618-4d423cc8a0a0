<template>
    <div class="container-manage">
        <SkCard :bordered="false">
            <div class="table-search">
                <SkForm layout="inline" v-model="searchForm" @submit="handleSearch" @reset="handleReset"
                    :submitText="$t('common.search')" :resetText="$t('common.reset')" :isFormSubmit="false">
                    <template #submitIcon>
                        <search-outlined />
                    </template>
                    <a-form-item name="keyword">
                        <SkInput v-model="searchForm.keyword" placeholder="请输入单号" allowClear
                            @pressEnter="handleSearch" />
                    </a-form-item>
                </SkForm>
            </div>

            <!-- 表格工具栏 -->
            <div class="table-operations">
                <SkSpace>
                    <SkButton type="primary" @click.prevent="handleAdd" v-if="$config.auth('1582381689724')">
                        <template #icon><plus-outlined /></template>
                        {{ $t('common.add') }}
                    </SkButton>
                    <SkButton type="primary" @click.prevent="fetchData">
                        <template #icon>
                            <reload-outlined />
                        </template>
                        {{ $t('common.refresh') }}
                    </SkButton>
                </SkSpace>
            </div>

            <!-- 表格 -->
            <SkTable ref="tableRef" :columns="columns" :data-source="tableData" :loading="loading"
                :pagination="pagination" :ready="tableReady" @change="handleTableChange">
                <template #bodyCell="{ column, record }">
                    <!-- 系统单号(可点击) -->
                    <template v-if="column.dataIndex === 'oddNumber'">
                        <a @click="handleDetail(record)">{{ record.oddNumber }}</a>
                    </template>

                    <!-- 流程ID(可点击) -->
                    <template v-if="column.dataIndex === 'processInstanceId'">
                        <ProcessDetail :processInstanceId="record.processInstanceId" />
                    </template>

                    <!-- 状态 -->
                    <template v-if="column.dataIndex === 'state'">
                        <div
                            v-html="$util.skyeyeClassEnumUtil.getEnumDataNameByCodeAndKeyStatic(initEnumData['flowableStateEnum'], 'id', record.state, 'name')">
                        </div>
                    </template>

                    <!-- 出库状态 -->
                    <template v-if="column.dataIndex === 'otherState'">
                        <div
                            v-html="$util.skyeyeClassEnumUtil.getEnumDataNameByCodeAndKeyStatic(initEnumData['depotOutState'], 'id', record.otherState, 'name')">
                        </div>
                    </template>

                    <!-- 操作 -->
                    <template v-if="column.dataIndex === 'action'">
                        <SkSpace>
                            <!-- 编辑状态 -->
                            <template v-if="record.editRow == 1">
                                <!-- 提交审批 -->
                                <a v-if="$config.auth('1694315440776')" @click="handleConfirmOk(record)">
                                    {{ $t('common.submitApproval') }}
                                </a>
                                <SkDivider v-if="$config.auth('1694315440776')" type="vertical" />

                                <!-- 编辑 -->
                                <a v-if="$config.auth('1582381689724')" @click="handleEdit(record, 'edit')">
                                    {{ $t('common.edit') }}
                                </a>
                                <SkDivider v-if="$config.auth('1582381689724')" type="vertical" />

                                <!-- 删除 -->
                                <SkPopconfirm v-if="$config.auth('1582381948035')" :title="$t('common.deleteConfirm')"
                                    @confirm="handleDelete(record)" :okText="$t('common.delete')"
                                    :cancelText="$t('common.cancel')">
                                    <a class="danger-link">{{ $t('common.delete') }}</a>
                                </SkPopconfirm>
                            </template>
                            <!-- 审批状态 -->
                            <template v-if="record.editRow == 2">
                                <!-- 撤销 -->
                                <SkPopconfirm v-if="$config.auth('1694315448441')" :title="$t('common.revokeConfirm')"
                                    @confirm="handleRevoke(record)" :okText="$t('common.confirm')"
                                    :cancelText="$t('common.cancel')">
                                    <a class="danger-link">{{ $t('common.revoke') }}</a>
                                </SkPopconfirm>
                            </template>
                        </SkSpace>
                    </template>
                </template>
            </SkTable>

            <SkModal v-model="modalVisible" :width="modalWidth" :title="modalTitle">
                <ShowIndex ref="showIndexRef"
                    v-if="modalType === 'add' || modalType === 'edit' || modalType === 'details'"
                    :pageId="operatorParams.pageId" :params="operatorParams.params" @cell-change="handleCellChange"
                    @close="handleModalClick">
                    <!-- 传递自定义单元格组件 -->
                    <template #cell-chooseInput-materialId="slotProps">
                        <a-form-item-rest>
                            <SkMaterialSelect v-model="slotProps.record[slotProps.column.dataIndex]"
                                :formData="slotProps.record"
                                :isEdit="slotProps.column.getConfig(slotProps.record).isEdit"
                                :attrKey="slotProps.column.dataIndex"
                                @change="(material) => handleMaterialChange(material, slotProps.record, slotProps.column, slotProps.formData)" />
                        </a-form-item-rest>
                    </template>
                </ShowIndex>
                <ApprovalPersonSelect v-if="modalType === 'approval'" :actKey="currentRecord?.serviceClassName"
                    :businessData="null" @submit="handleApprovalPersonSubmit" @cancel="handleModalCancel" />
            </SkModal>
        </SkCard>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted, getCurrentInstance, nextTick } from 'vue'
import { ReloadOutlined, PlusOutlined } from '@ant-design/icons-vue'
import { SkMessage } from '@/components/SkMessage/index.vue'
import { useI18n } from 'vue-i18n'
import SkForm from '@/components/SkForm/index.vue'
import SkInput from '@/components/SkInput/index.vue'
import SkCard from '@/components/SkCard/index.vue'
import SkButton from '@/components/SkButton/index.vue'
import SkDivider from '@/components/SkDivider/index.vue'
import SkTable from '@/components/SkTable/index.vue'
import SkSpace from '@/components/SkSpace/index.vue'
import SkPopconfirm from '@/components/SkPopconfirm/index.vue'
import SkModal from '@/components/SkModal/index.vue'
import ShowIndex from '@/views/dsForm/show/index.vue'
import ProcessDetail from '@/views/dsForm/process/detail.vue'
import ApprovalPersonSelect from '@/views/system/submitApproval/approvalPersonSelect.vue'
import SkMaterialSelect from '@/components/SkMaterialSelect/index.vue'

const { proxy } = getCurrentInstance()
const { t } = useI18n()

// 搜索表单数据
const searchForm = reactive({
    keyword: ''
})

// 表格列定义
const columns = [
    {
        title: t('common.serialNum'),
        dataIndex: 'index',
        width: 80,
        align: 'center',
        fixed: 'left',
        customRender: ({ index }) => {
            // 计算实际序号 = (当前页码 - 1) * 每页条数 + 当前行索引 + 1
            return (pagination.current - 1) * pagination.pageSize + index + 1
        }
    },
    { title: '系统单号', dataIndex: 'oddNumber', width: 200 },
    { title: '流程ID', dataIndex: 'processInstanceId', width: 200 },
    { title: '状态', dataIndex: 'state', width: 90 },
    { title: '出库状态', dataIndex: 'otherState', width: 90 },
    { title: '申领日期', dataIndex: 'applyTime', width: 100 },
    { title: '总金额', dataIndex: 'allPrice', width: 120 },
    { title: proxy.$t('common.createName'), dataIndex: 'createName', width: 140 },
    { title: proxy.$t('common.createTime'), dataIndex: 'createTime', width: 150, align: 'center' },
    { title: proxy.$t('common.lastUpdateName'), dataIndex: 'lastUpdateName', width: 140 },
    { title: proxy.$t('common.lastUpdateTime'), dataIndex: 'lastUpdateTime', width: 150, align: 'center' },
    { title: proxy.$t('common.operation'), dataIndex: 'action', fixed: 'right', width: 250, align: 'center' }
]

// 表格数据
const tableData = ref([])
const loading = ref(false)
const tableReady = ref(false)

// 分页配置
const pagination = reactive(proxy.$config.pagination())

// 初始化枚举数据
const initEnumData = ref({})
const getInitData = async () => {
    let result = await proxy.$util.getEnumListMapByCode(['flowableStateEnum', 'depotOutState']);
    initEnumData.value = result
}

// 获取数据
const fetchData = async () => {
    loading.value = true
    try {
        // 构建查询参数，添加 keyword
        const params = {
            page: Number(pagination.current || 1),
            limit: Number(pagination.pageSize || 10),
            keyword: searchForm.keyword?.trim() || '' // 添加关键字搜索参数
        }
        const res = await proxy.$http.post(
            proxy.$config.getConfig().sealServiceBasePath + 'sealseservice023',
            params
        )

        tableData.value = res.rows || []
        pagination.total = res.total || 0
    } catch (error) {
        SkMessage.error('获取数据失败')
        tableData.value = []
        pagination.total = 0
    } finally {
        loading.value = false
    }
}

// 事件处理 搜索
const handleSearch = () => {
    pagination.current = 1 // 重置到第一页
    fetchData()
}

// 添加重置处理函数
const handleReset = () => {
    searchForm.keyword = '' // 清空搜索关键字
    pagination.current = 1 // 重置到第一页
    fetchData()
}

// 表格变化处理
const handleTableChange = (pag, filters, sorter) => {
    if (pag) {
        pagination.current = Number(pag.current || 1)
        pagination.pageSize = Number(pag.pageSize || 10)
    }
    fetchData()
}

const modalVisible = ref(false)
const modalWidth = ref('70%')
const modalTitle = ref('')
const modalType = ref('')
const operatorParams = ref({})

// 添加
const handleAdd = () => {
    modalTitle.value = '新增'
    modalType.value = 'add'
    modalVisible.value = true
    operatorParams.value = {
        pageId: 'FP2023091000001',
        params: {}
    }
}

// 编辑
const handleEdit = (record) => {
    modalTitle.value = '编辑'
    modalType.value = 'edit'
    modalVisible.value = true
    operatorParams.value = {
        pageId: 'FP2023091000002',
        params: {
            id: record.id
        }
    }
}

// 详情
const handleDetail = (record) => {
    modalTitle.value = '详情'
    modalType.value = 'details'
    modalVisible.value = true
    operatorParams.value = {
        pageId: 'FP2023091000003',
        params: {
            id: record.id
        }
    }
}

// 弹窗关闭
const handleModalClick = (isSubmit) => {
    modalVisible.value = false
    if (isSubmit) {
        fetchData() // 刷新表格数据
    }
}

// 删除
const handleDelete = async (record) => {
    try {
        const params = {
            id: record.id
        }
        await proxy.$http.delete(
            proxy.$config.getConfig().sealServiceBasePath + 'deleteSealApplyById',
            params
        )
        SkMessage.success('删除成功')
        fetchData() // 刷新数据
    } catch (error) {
        SkMessage.error('删除失败')
    }
}

// 当前行记录
const currentRecord = ref(null)

// 修改提交审批处理方法
const handleConfirmOk = async (record) => {
    try {
        await nextTick()
        modalTitle.value = '提交审批'
        modalType.value = 'approval'
        // 打开审批人选择弹窗
        currentRecord.value = record
        modalVisible.value = true
    } catch (error) {
        SkMessage.error('操作失败')
    }
}

// 处理弹窗取消
const handleModalCancel = async () => {
    try {
        await nextTick()
        modalVisible.value = false
    } catch (error) {
        console.error('处理失败:', error)
    }
}

// 处理审批人选择提交
const handleApprovalPersonSubmit = async (person) => {
    try {
        // 构建参数，与旧系统保持一致
        const params = {
            id: currentRecord.value.id,
            approvalId: person.id
        }

        // 发送提交请求
        await proxy.$http.post(
            proxy.$config.getConfig().sealServiceBasePath + 'submitSealApplyToApproval',
            params
        )

        // 提交成功
        SkMessage.success('提交成功')
        // 关闭弹窗
        modalVisible.value = false
        // 刷新数据
        await fetchData()
    } catch (error) {
        SkMessage.error('提交失败')
    }
}

// 撤销
const handleRevoke = async (record) => {
    try {
        const params = {
            processInstanceId: record.processInstanceId
        }

        await proxy.$http.put(
            proxy.$config.getConfig().sealServiceBasePath + 'revokeSealApply',
            params
        )

        SkMessage.success('撤销成功')
        fetchData() // 刷新数据
    } catch (error) {
        SkMessage.error('撤销失败')
    }
}

const showIndexRef = ref(null)

// 处理单元格变化
const handleCellChange = ({ record, dataIndex, value, column }, formData) => {
    // 处理物料选择变化
    if (dataIndex == 'materialId') {
        record.unitPrice = 0
    } else if (dataIndex == 'normsId') {
        const materialNorms = column.getConfig(record).defaultData
        const norms = materialNorms.find(item => item.id === value)
        // 获取物料的销售价
        record.unitPrice = norms.retailPrice
    }
    // 处理金额计算
    const result = proxy.$util.erpUtil.calcMoneyKey(dataIndex, record)
    // 将result合入到record中
    Object.assign(record, result)

    calcMoney(formData)
}

const calcMoney = (formData) => {
    // 处理金额计算
    const applyLinkList = formData?.applyLinkList || []
    let totalPrice = 0;
    if (!proxy.$util.isNull(applyLinkList)) {
        applyLinkList.forEach((item, i) => {
            totalPrice = proxy.$util.calculationUtil.sum(totalPrice, item.allPrice)
        })
    }

    formData.allPrice = totalPrice
}

// 处理物料选择变化
const handleMaterialChange = async (material, record, column, formData) => {
    const dataIndex = column.dataIndex
    const mationKey = proxy.$util.getKeyIdToMation(dataIndex)

    if (material) {
        // 更新其他相关字段
        record[mationKey] = material
        record["normsId"] = undefined
        record["unitPrice"] = 0

        // 等待组件挂载完成
        await nextTick()

        // 修改当前行的表格列配置
        showIndexRef.value?.writeComponentRef?.updateTableColumns(column.pIdDataIndex, record, {
            "normsId": {
                dataType: 1,
                defaultData: material.materialNorms
            }
        })

        // 触发 cell-change 事件
        handleCellChange({
            record,
            dataIndex: column.dataIndex,
            value: material,
            column: column
        }, formData)
    } else {
        // 清空相关字段
        delete record[mationKey]
    }
}

onMounted(async () => {
    await nextTick()
    requestAnimationFrame(() => {
        tableReady.value = true
    })
    await getInitData()
    await fetchData()
})
</script>

<style lang="less" scoped></style>