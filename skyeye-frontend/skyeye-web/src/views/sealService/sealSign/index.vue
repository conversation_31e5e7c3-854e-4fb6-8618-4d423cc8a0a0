<template>
    <div class="container-manage">
        <SkCard ref="cardRef" :bordered="false">
            <SkAlert message="温馨提示：只有待签到的工单才可以进行签到操作。" show-icon />
            <div class="table-operations">
                <SkSpace>
                    <a-popconfirm :title="t('common.signConfirm')" @confirm="handleSign" :okText="t('common.confirm')"
                        :cancelText="t('common.cancel')">
                        <SkButton v-if="!isSign" type="primary">
                            <template #icon>
                                <PlusOutlined />
                            </template>
                            {{ $t('common.sign') }}
                        </SkButton>
                    </a-popconfirm>

                    <SkButton type="primary" @click.prevent="fetchData">
                        <template #icon>
                            <ReloadOutlined />
                        </template>
                        {{ $t('common.refresh') }}
                    </SkButton>
                </SkSpace>
            </div>
            <SkTable :columns="columns" :data-source="tableData" :loading="loading" :pagination="pagination"
                :ready="tableReady" @change="handleTableChange" :tipInfoHeight="56">
            </SkTable>
        </SkCard>
    </div>

</template>
<script setup>
import { reactive, ref, getCurrentInstance, onMounted, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import SkCard from '@/components/SkCard/index.vue'
import SkButton from '@/components/SkButton/index.vue'
import SkSpace from '@/components/SkSpace/index.vue'
import SkTable from '@/components/SkTable/index.vue'
import { SkMessage } from '@/components/SkMessage/index.vue'
import SkAlert from '@/components/SkAlert/index.vue'

const { proxy } = getCurrentInstance()
const { t } = useI18n()

const props = defineProps({
    objectId: {
        type: String,
        default: ''
    },
    objectKey: {
        type: String,
        default: ''
    }
})

// 表格列配置
const columns = ref([
    {
        title: t('common.serialNum'),
        dataIndex: 'index',
        width: 80,
        align: 'center',
        fixed: 'left',
        customRender: ({ index }) => {
            // 计算实际序号 = (当前页码 - 1) * 每页条数 + 当前行索引 + 1
            return (pagination.current - 1) * pagination.pageSize + index + 1
        }
    },
    {
        title: '经度',
        dataIndex: 'longitude',
        width: 120
    },
    {
        title: '纬度',
        dataIndex: 'latitude',
        width: 120
    },
    {
        title: '签到地址',
        dataIndex: 'address',
        width: 300
    },
    {
        title: '签到时间',
        dataIndex: 'signTime',
        width: 150
    },
    {
        title: '备注',
        dataIndex: 'remark',
        width: 150
    }
])

const tableData = ref([]) // 表格数据
const loading = ref(false) // 加载状态
const tableReady = ref(false)

// 分页配置
const pagination = reactive(proxy.$config.pagination())

// 判断是否已经签到
const isSign = ref(false)
const getIsSign = async () => {
    const res = await proxy.$http.get(
        proxy.$config.getConfig().sealServiceBasePath + 'querySealServiceOrderById',
        { id: props.objectId }
    )
    if (res.bean.state !== 'beSigned') {
        isSign.value = true
    }
}

// 获取数据
const fetchData = async () => {
    loading.value = true
    try {
        // 构建查询参数，添加 keyword
        const params = {
            page: Number(pagination.current || 1),
            limit: Number(pagination.pageSize || 10),
            objectId: props.objectId,
            objectKey: props.objectKey,
        }

        const res = await proxy.$http.post(
            proxy.$config.getConfig().sealServiceBasePath + 'querySealSignList',
            params
        )

        tableData.value = res.rows || []
        pagination.total = res.total || 0
    } catch (error) {
        SkMessage.error('获取数据失败')
        tableData.value = []
        pagination.total = 0
    } finally {
        loading.value = false
    }
}

// 表格变化处理
const handleTableChange = (pag, filters, sorter) => {
    if (pag) {
        pagination.current = Number(pag.current || 1)
        pagination.pageSize = Number(pag.pageSize || 10)
    }
    fetchData()
}

// 签到
const handleSign = async () => {
    try {
        await proxy.$http.post(
            proxy.$config.getConfig().sealServiceBasePath + 'insertSealSign',
            {
                objectId: props.objectId,
                objectKey: props.objectKey
            })
        SkMessage.success('签到成功')
        isSign.value = true
        fetchData()
    } catch (error) {
        SkMessage.error('签到失败')
    }
}

// 初始化
onMounted(async () => {
    await nextTick()
    requestAnimationFrame(() => {
        tableReady.value = true
    })
    await getIsSign()
    fetchData()
})
</script>

<style scoped></style>