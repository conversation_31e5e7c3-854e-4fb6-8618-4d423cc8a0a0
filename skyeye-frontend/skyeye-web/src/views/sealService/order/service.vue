<template>
    <div class="container-manage">
        <SkTabs v-model="activeTab" :tabs="tabs">
            <template #details>
                <div v-if="activeTab === 'details'">
                    <ShowIndex pageId="FP2023082900006" :params="{ id: objectId }" />
                </div>
            </template>
            <template #signRecord>
                <div v-if="activeTab === 'signRecord'">
                    <SealSign :objectId="objectId" :objectKey="objectKey" />
                </div>
            </template>
            <template #faultInfo>
                <div v-if="activeTab === 'faultInfo'">
                    <SealFault :objectId="objectId" :objectKey="objectKey" />
                </div>
            </template>
            <template #serviceEvaluation>
                <div v-if="activeTab === 'serviceEvaluation'">
                    <SealEvaluate :objectId="objectId" :objectKey="objectKey" />
                </div>
            </template>
            <template #feedBack>
                <div v-if="activeTab === 'feedBack'">
                    <SealFeedBack :objectId="objectId" :objectKey="objectKey" />
                </div>
            </template>
        </SkTabs>
    </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import SkTabs from '@/components/SkTabs/index.vue'
import ShowIndex from '@/views/dsForm/show/index.vue'
import SealSign from '@/views/sealService/sealSign/index.vue'
import SealFault from '@/views/sealService/sealFault/index.vue'
import SealEvaluate from '@/views/sealService/sealEvaluate/index.vue'
import SealFeedBack from '@/views/sealService/feedBack/index.vue'

const { t } = useI18n()

const props = defineProps({
    params: {
        type: Object,
        default: () => ({})
    }
})

const objectId = computed(() => props.params.objectId)
const objectKey = computed(() => props.params.objectKey)

// 当前激活的选项卡
const activeTab = ref('details')

// 定义选项卡配置
const tabs = [
    { key: 'details', label: t('common.details') },
    { key: 'signRecord', label: t('common.signRecord') },
    { key: 'faultInfo', label: t('common.faultInfo') },
    { key: 'serviceEvaluation', label: t('common.serviceEvaluation') },
    { key: 'feedBack', label: t('common.feedBack') }
]
</script>

<style scoped></style>
