<template>
    <div class="container-manage">
        <SkCard ref="cardRef" :bordered="false">
            <div class="table-operations">
                <SkSpace>
                    <SkButton type="primary" @click.prevent="fetchData">
                        <template #icon>
                            <ReloadOutlined />
                        </template>
                        {{ $t('common.refresh') }}
                    </SkButton>
                </SkSpace>
            </div>
            <SkTable :columns="columns" :data-source="tableData" :loading="loading" :pagination="pagination"
                :ready="tableReady" @change="handleTableChange">
                <template #bodyCell="{ column, record }">

                    <template v-if="column.dataIndex === 'stock'">
                        <a @click="inventory(record)">{{ record.stock }}</a>
                    </template>

                </template>
            </SkTable>

            <!-- 弹窗 -->
            <SkModal v-model="modalVisible" :title="modalTitle">
                <DepartmentalInventoryDetails v-if="modalType === 'stock'" :materialId="materialId" :normsId="normsId"
                    :departmentId="departmentId">
                </DepartmentalInventoryDetails>
            </SkModal>
        </SkCard>
    </div>
</template>
<script setup>
import { reactive, ref, getCurrentInstance, onMounted, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import SkCard from '@/components/SkCard/index.vue'
import SkButton from '@/components/SkButton/index.vue'
import SkSpace from '@/components/SkSpace/index.vue'
import SkTable from '@/components/SkTable/index.vue'
import { SkMessage } from '@/components/SkMessage/index.vue'
import DepartmentalInventoryDetails from './departmentalInventoryDetails.vue'
import SkModal from '@/components/SkModal/index.vue'

const { proxy } = getCurrentInstance()
const { t } = useI18n()

// 表格列配置
const columns = ref([
    {
        title: t('common.serialNum'),
        dataIndex: 'index',
        width: 80,
        align: 'center',
        fixed: 'left',
        customRender: ({ index }) => {
            // 计算实际序号 = (当前页码 - 1) * 每页条数 + 当前行索引 + 1
            return (pagination.current - 1) * pagination.pageSize + index + 1
        }
    },
    {
        title: '产品',
        dataIndex: 'materialMation',
        width: 120,
        customRender: ({ record }) => record.materialMation?.name
    },
    {
        title: '规格',
        dataIndex: 'normsMation',
        width: 150,
        customRender: ({ record }) => record.normsMation?.name
    },
    {
        title: '处理部门',
        dataIndex: 'departmentMation',
        width: 120,
        customRender: ({ record }) => record.departmentMation?.name
    },
    {
        title: '部门库存',
        dataIndex: 'stock',
        width: 50,
        align: 'center',
        fixed: 'right'
    }
])

// 弹窗相关
const materialId = ref('')
const normsId = ref('')
const departmentId = ref('')
const modalVisible = ref(false)
const modalTitle = ref('')
const modalType = ref('')

// 详情
const inventory = (record) => {
    materialId.value = record.materialId
    normsId.value = record.normsId
    departmentId.value = record.departmentId
    modalTitle.value = '部门商品库存详情'
    modalType.value = 'stock'
    modalVisible.value = true
}

const tableData = ref([]) // 表格数据
const loading = ref(false) // 加载状态
const tableReady = ref(false)

// 分页配置
const pagination = reactive(proxy.$config.pagination())

// 获取数据
const fetchData = async () => {
    loading.value = true
    try {
        const params = {
            page: Number(pagination.current || 1),
            limit: Number(pagination.pageSize || 10),
            type: 'department'
        }

        // 发送查询请求
        const res = await proxy.$http.post(
            proxy.$config.getConfig().erpBasePath + 'erpdepartstock001',
            params
        )

        tableData.value = res.rows || []
        pagination.total = res.total || 0
    } catch (error) {
        SkMessage.error('获取数据失败')
        tableData.value = []
        pagination.total = 0
    } finally {
        loading.value = false
    }
}

// 表格变化处理
const handleTableChange = (pag) => {
    if (pag) {
        pagination.current = Number(pag.current || 1)
        pagination.pageSize = Number(pag.pageSize || 10)
    }
    fetchData()
}

// 初始化
onMounted(async () => {
    await nextTick()
    requestAnimationFrame(() => {
        tableReady.value = true
    })
    fetchData()
})
</script>