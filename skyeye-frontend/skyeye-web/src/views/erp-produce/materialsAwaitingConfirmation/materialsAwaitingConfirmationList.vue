<template>
    <div class="container-manage">
        <SkCard ref="cardRef" :bordered="false">
            <!-- 顶部切换组件 -->
            <div class="table-btn-group">
                <SkAuthBtnGroup authPointCode="1721045686446" @change="handleChanges" />
            </div>
            <!-- 搜索表单 -->
            <div class="table-search">
                <SkForm layout="inline" v-model="searchForm" @submit="handleSearch" @reset="handleReset"
                    :submitText="$t('common.search')" :resetText="$t('common.reset')" :isFormSubmit="false">
                    <template #submitIcon>
                        <search-outlined />
                    </template>
                    <a-form-item name="keyword">
                        <SkInput v-model="searchForm.keyword" placeholder="请输入单号" allowClear
                            @pressEnter="handleSearch" />
                    </a-form-item>
                </SkForm>
            </div>

            <!-- 操作按钮 -->
            <div class="table-operations">
                <SkSpace>
                    <SkButton type="primary" @click.prevent="fetchData">
                        <template #icon>
                            <ReloadOutlined />
                        </template>
                        {{ $t('common.refresh') }}
                    </SkButton>
                </SkSpace>
            </div>

            <!-- 表格 -->
            <SkTable :columns="columns" :data-source="tableData" :loading="loading" :pagination="pagination"
                :ready="tableReady" @change="handleTableChange">
                <template #bodyCell="{ column, record }">
                    <template v-if="column.dataIndex === 'oddNumber'">
                        <a @click="handleDetail(record)">
                            {{ record.oddNumber }}
                            <template v-if="!$util.isNull(record.fromId)">
                                <SkTag :color="$util.getTagColor(column.dataIndex)">
                                    转
                                </SkTag>
                            </template>
                        </a>
                    </template>

                    <template v-if="column.dataIndex === 'processInstanceId'">
                        <!-- 流程id点击后的详情 -->
                        <ProcessDetail :processInstanceId="record.processInstanceId" />
                    </template>

                    <!-- 获取来源类型 -->
                    <template v-if="column.dataIndex === 'fromTypeId'">
                        <div
                            v-html="$util.skyeyeClassEnumUtil.getEnumDataNameByCodeAndKeyStatic(initEnumData['depotOutFromType'], 'id', record.fromTypeId, 'name')">
                        </div>
                    </template>

                    <!-- 获取状态 -->
                    <template v-if="column.dataIndex === 'state'">
                        <div
                            v-html="$util.skyeyeClassEnumUtil.getEnumDataNameByCodeAndKeyStatic(initEnumData['flowableStateEnum'], 'id', record.state, 'name')">
                        </div>
                    </template>

                    <!-- 获取确认状态-->
                    <template v-if="column.dataIndex === 'otherState'">
                        <div
                            v-html="$util.skyeyeClassEnumUtil.getEnumDataNameByCodeAndKeyStatic(initEnumData['depotOutOtherState'], 'id', record.otherState, 'name')">
                        </div>
                    </template>
                    <!-- 操作列 -->
                    <template v-if="column.key === 'action'">
                        <SkSpace>
                            <template
                                v-if="record.state == 'pass' && (record.otherState == 1 || record.otherState == 2)">
                                <!-- 物料接收 -->
                                <a v-if="$config.auth('1721046594407')" @click="materialReceipt(record)">
                                    {{ $t('erpProduce.materialsAwaitingConfirmation.materialReceipt') }}
                                </a>
                                <SkDivider v-if="$config.auth('1721046594407')" type="vertical" />

                                <!-- 物料退货 -->
                                <a v-if="$config.auth('1721046610103')" @click="materialReturn(record)">
                                    {{ $t('erpProduce.materialsAwaitingConfirmation.materialReturn') }}
                                </a>
                            </template>
                        </SkSpace>
                    </template>
                </template>
            </SkTable>

            <!--弹窗 -->
            <SkModal v-model="modalVisible" :title="modalTitle">
                <ShowIndex ref="showIndexRef"
                    v-if="modalType === 'details' || modalType === 'materialReceipt' || modalType === 'materialReturn'"
                    :pageId="operatorParams.pageId" :params="operatorParams.params"
                    :whetherCustomerData="whetherCustomerData" :customerData="customerData"
                    @customerDataSave="customerDataSave" @close="handleModalClick" @cell-change="handleCellChange"
                    @loaded="handleShowIndexLoaded" @afterDataLoaded="handleAfterDataLoaded"
                    @handleChange="handleChange">
                    <!-- 传递自定义单元格组件 -->
                    <template #cell-chooseInput-materialId="slotProps">
                        <a-form-item-rest>
                            <SkMaterialSelect v-model="slotProps.record[slotProps.column.dataIndex]"
                                :formData="slotProps.record"
                                :isEdit="slotProps.column.getConfig(slotProps.record).isEdit"
                                :attrKey="slotProps.column.dataIndex"
                                @change="(material) => handleMaterialChange(material, slotProps.record, slotProps.column, slotProps.formData)" />
                        </a-form-item-rest>
                    </template>
                </ShowIndex>
                <ApprovalPersonSelect v-if="modalType === 'approval'" :actKey="currentRecord?.serviceClassName"
                    :businessData="null" @submit="handleApprovalPersonSubmit" @cancel="handleModalCancel" />
            </SkModal>
        </SkCard>
    </div>
</template>

<script setup>
import { reactive, ref, getCurrentInstance, onMounted, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import SkTag from '@/components/SkTag/index.vue'
import SkInput from '@/components/SkInput/index.vue'
import SkCard from '@/components/SkCard/index.vue'
import SkForm from '@/components/SkForm/index.vue'
import SkTable from '@/components/SkTable/index.vue'
import SkSpace from '@/components/SkSpace/index.vue'
import SkButton from '@/components/SkButton/index.vue'
import SkAuthBtnGroup from '@/components/SkAuthBtnGroup/index.vue'
import SkModal from '@/components/SkModal/index.vue'
import ShowIndex from '@/views/dsForm/show/index.vue'
import ApprovalPersonSelect from '@/views/system/submitApproval/approvalPersonSelect.vue'
import SkMaterialSelect from '@/components/SkMaterialSelect/index.vue'
import ProcessDetail from '@/views/dsForm/process/detail.vue'
import SkDivider from '@/components/SkDivider/index.vue'

const { proxy } = getCurrentInstance()
const { t } = useI18n()

const tableData = ref([]) // 表格数据
const loading = ref(false) // 加载状态
const tableReady = ref(false)
// 分页配置
const pagination = reactive(proxy.$config.pagination())

// 搜索表单数据
const searchForm = reactive({
    keyword: ''
})

// 权限点处理
const authMation = ref({})
const handleChanges = (key, value) => {
    authMation.value[key] = value
    pagination.current = 1
    fetchData()
}

// 事件处理 搜索
const handleSearch = () => {
    pagination.current = 1 // 重置到第一页
    fetchData()
}

// 添加重置处理函数
const handleReset = () => {
    searchForm.keyword = '' // 清空搜索关键字
    pagination.current = 1 // 重置到第一页
    fetchData()
}

// 表格变化处理
const handleTableChange = (pag) => {
    if (pag) {
        pagination.current = Number(pag.current || 1)
        pagination.pageSize = Number(pag.pageSize || 10)
    }
    fetchData()
}

// 表格列配置
const columns = ref([
    {
        title: t('common.serialNum'),
        dataIndex: 'index',
        width: 60,
        align: 'center',
        fixed: 'left',
        customRender: ({ index }) => {
            // 计算实际序号 = (当前页码 - 1) * 每页条数 + 当前行索引 + 1
            return (pagination.current - 1) * pagination.pageSize + index + 1
        }
    },
    {
        title: '单号',
        dataIndex: 'oddNumber',
        width: 200,
        align: 'center'
    },
    {
        title: '单据日期',
        dataIndex: 'operTime',
        width: 100,
        align: 'center'
    },
    {
        title: '来源单据信息',
        width: 300,
        align: 'center',
        children: [{
            title: '来源类型',
            dataIndex: 'fromTypeId',
            width: 120,
            align: 'center'
        },
        {
            title: '单据编号',
            dataIndex: 'fromId',
            width: 180,
            align: 'center',
            customRender: ({ record }) => record.fromMation?.oddNumber
        }]
    },
    {
        title: '部门',
        dataIndex: 'departmentMation',
        width: 120,
        customRender: ({ record }) => record.departmentMation?.name
    },
    {
        title: '车间',
        dataIndex: 'farmMation',
        width: 160,
        customRender: ({ record }) => record.farmMation?.name
    },
    {
        title: '业务员',
        dataIndex: 'salesmanMation',
        width: 120,
        customRender: ({ record }) => record.salesmanMation?.name
    },
    {
        title: '总金额',
        dataIndex: 'totalPrice',
        width: 160,
        align: 'center'
    },
    {
        title: '流程ID',
        dataIndex: 'processInstanceId',
        width: 200,
        align: 'center'
    },
    {
        title: '状态',
        dataIndex: 'state',
        width: 100,
        align: 'center'
    },
    {
        title: '确认状态',
        dataIndex: 'otherState',
        width: 100,
        align: 'center'
    },
    {
        title: '创建人',
        dataIndex: 'createName',
        width: 120,
        align: 'center',
    },
    {
        title: '创建时间',
        dataIndex: 'createTime',
        width: 160,
        align: 'center',
    },
    {
        title: '最后修改人',
        dataIndex: 'lastUpdateName',
        width: 120,
        align: 'center'
    },
    {
        title: '最后修改时间',
        dataIndex: 'lastUpdateTime',
        width: 160,
        align: 'center'
    },
    {
        title: t('common.operation'),
        key: 'action',
        width: 200,
        align: 'center',
        fixed: 'right'
    }
])

// 初始化枚举数据
const initEnumData = ref({})
const getInitData = async () => {
    let result = await proxy.$util.getEnumListMapByCode(['depotOutFromType', 'flowableStateEnum', 'depotOutOtherState']);
    initEnumData.value = result
}

// 获取数据
const fetchData = async () => {
    loading.value = true
    try {
        var params = {
            ...authMation.value,
            page: Number(pagination.current || 1),
            limit: Number(pagination.pageSize || 10),
            keyword: searchForm.keyword?.trim() || ''
        }

        // 发送查询请求
        const res = await proxy.$http.post(
            proxy.$config.getConfig().erpBasePath + 'queryNeedConfirmDepotOutList',
            params
        )

        tableData.value = res.rows || []
        pagination.total = res.total || 0
    } catch (error) {
        SkMessage.error('获取数据失败')
        tableData.value = []
        pagination.total = 0
    } finally {
        loading.value = false
    }
}

const modalVisible = ref(false)
const modalTitle = ref('')
const modalType = ref('')
const whetherCustomerData = ref(false)
const customerData = ref({})
const operatorParams = ref({})

// 详情
const handleDetail = (record) => {
    modalTitle.value = '详情'
    modalType.value = 'details'
    modalVisible.value = true
    whetherCustomerData.value = false
    operatorParams.value = {
        pageId: 'FP2024070100007',
        params: {
            id: record.id
        }
    }
}

// 物料接收
const materialReceipt = async (record) => {
    modalTitle.value = '转物料接收'
    modalType.value = 'materialReceipt'

    const res = await proxy.$http.get(
        proxy.$config.getConfig().erpBasePath + 'queryDepotOutTransById',
        {
            id: record.id
        })
    whetherCustomerData.value = true
    customerData.value = res.bean

    modalVisible.value = true
    operatorParams.value = {
        // 物料接收的【编辑布局】
        pageId: 'FP2024071500004',
        params: {
            id: record.id
        }
    }
}

// 物料退货
const materialReturn = async (record) => {
    modalTitle.value = '转物料退货'
    modalType.value = 'materialReturn'

    const res = await proxy.$http.get(
        proxy.$config.getConfig().erpBasePath + 'queryDepotOutTransById',
        {
            id: record.id
        })
    whetherCustomerData.value = true
    customerData.value = res.bean

    modalVisible.value = true
    operatorParams.value = {
        // 物料退货的【编辑布局】
        pageId: 'FP2024071500005',
        params: {
            id: record.id
        }
    }
}

const showIndexRef = ref(null)

// 处理数据会显示，刚获取到数据的逻辑
const handleAfterDataLoaded = (formData) => {
    // 这里可以处理特殊的参数
    const pageId = operatorParams.value.pageId
    const notEdit = proxy.$config.formEditType.notEdit

    // 若formData.erpOrderItemList不为空，进行遍历
    if (!proxy.$util.isNull(formData.erpOrderItemList)) {
        formData.erpOrderItemList.forEach(item => {
            // 下拉框的特殊配置
            item[`normsId_config`] = {
                // 自定义类型
                dataType: 1,
                // materialNorms为计量单位的数据
                defaultData: item.materialMation?.materialNorms || []
            }
            if (pageId == 'FP2024071500004' || pageId == 'FP2024071500005') {
                item[`materialId_config`] = { isEdit: notEdit }
                item[`normsId_config`].isEdit = notEdit
                item[`depotId_config`] = { isEdit: notEdit }
                item[`allPrice_config`] = { isEdit: notEdit }
                item[`unitPrice_config`] = { isEdit: notEdit }
                item[`taxRate_config`] = { isEdit: notEdit }
                item[`taxMoney_config`] = { isEdit: notEdit }
                item[`taxUnitPrice_config`] = { isEdit: notEdit }
                item[`taxLastMoney_config`] = { isEdit: notEdit }
            }
        })
    }
}

// 处理组件加载完成
const handleShowIndexLoaded = (formData) => {
    const pageId = operatorParams.value.pageId

    // 在组件加载完成后执行需要的操作
    if (pageId == 'FP2024071500004' || pageId == 'FP2024071500005') {
        showIndexRef.value?.writeComponentRef?.updateShowAdd("erpOrderItemList", false)
    }
}

// 处理单元格变化
const handleCellChange = ({ record, dataIndex}, formData) => {
    // 处理金额计算
    const result = proxy.$util.erpUtil.calcMoneyKey(dataIndex, record)
    // 将result合入到record中
    Object.assign(record, result)

    calcMoney(formData)
}

// 处理表单项变化
const handleChange = ({ attrKey, formData }) => {
    if (attrKey == 'discount') {
        calcMoney(formData)
    }
}

const calcMoney = (formData) => {
    // 处理金额计算
    const erpOrderItemList = formData?.erpOrderItemList || []
    let totalPrice = 0;
    if (!proxy.$util.isNull(erpOrderItemList)) {
        erpOrderItemList.forEach((item, i) => {
            totalPrice = proxy.$util.calculationUtil.sum(totalPrice, item.taxLastMoney)
        })
    }

    // 优惠信息
    const discountMoney = proxy.$util.erpUtil.calcDiscountMoney(formData, totalPrice)
    totalPrice = proxy.$util.calculationUtil.subtraction(totalPrice, discountMoney)
    formData.discountMoney = discountMoney
    formData.totalPrice = totalPrice
}

// 处理数据保存
const customerDataSave = async (data) => {
    const pageId = operatorParams.value.pageId
    if (pageId == 'FP2024071500004') {
        // 物料接收
        await proxy.$http.post(
            proxy.$config.getConfig().erpBasePath + 'insertDepotOutToTurnPut',
            data
        )
    } else if (pageId == 'FP2024071500005') {
        // 物料退货
        await proxy.$http.post(
            proxy.$config.getConfig().erpBasePath + 'insertDepotOutToSealsReturns',
            data
        )
    }
    handleModalClick(true)

}

// 弹窗关闭
const handleModalClick = (isSubmit) => {
    modalVisible.value = false
    if (isSubmit) {
        fetchData() // 刷新表格数据
    }
}

// 处理弹窗取消
const handleModalCancel = async () => {
    try {
        await nextTick()
        modalVisible.value = false
    } catch (error) {
        SkMessage.error('处理失败')
    }
}

// 处理审批人选择提交
const handleApprovalPersonSubmit = async (person) => {
    try {
        // 构建参数，与旧系统保持一致
        const params = {
            id: currentRecord.value.id,
            serviceClassName: currentRecord.value.serviceClassName,
            approvalId: person.id
        }

        // 发送提交请求
        await proxy.$http.put(
            proxy.$config.getConfig().erpBasePath + 'erpcommon006',
            params
        )

        // 提交成功
        SkMessage.success('提交成功')
        // 关闭弹窗
        modalVisible.value = false
        // 刷新数据
        await fetchData()

    } catch (error) {
        SkMessage.error('提交失败')
    }
}

// 初始化
onMounted(async () => {
    await nextTick()
    requestAnimationFrame(() => {
        tableReady.value = true
    })
    await getInitData()
    fetchData()
})
</script>

<style></style>