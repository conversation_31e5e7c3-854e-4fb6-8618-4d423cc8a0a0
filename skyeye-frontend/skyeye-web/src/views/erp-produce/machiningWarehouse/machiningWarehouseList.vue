<template>
    <div class="container-manage">
        <SkCard ref="cardRef" :bordered="false">
            <!-- 搜索表单 -->
            <div class="table-search">
                <SkForm layout="inline" v-model="searchForm" @submit="handleSearch" @reset="handleSearch"
                    :submitText="$t('common.search')" :isFormSubmit="false" :resetText="$t('common.reset')">
                    <template #submitIcon>
                        <search-outlined />
                    </template>
                    <a-form-item name="keyword">
                        <SkInput v-model="searchForm.keyword" placeholder="请输入单据编号" allowClear />
                    </a-form-item>
                </SkForm>
            </div>

            <div class="table-operations">
                <SkSpace>
                    <SkButton type="primary" @click.prevent="fetchData">
                        <template #icon><reload-outlined /></template>
                        {{ $t('common.refresh') }}
                    </SkButton>
                </SkSpace>
            </div>
            <!-- 表格 -->
            <SkTable :columns="columns" :data-source="tableData" :loading="loading" :pagination="pagination"
                :ready="tableReady" @change="handleTableChange">
                <template #bodyCell="{ column, record }">
                    <template v-if="column.dataIndex === 'oddNumber'">
                        <a @click="handleDetail(record)">{{ record.oddNumber }}
                            <template v-if="!$util.isNull(record.fromId)">
                                <SkTag :color="$util.getTagColor(column.dataIndex)">
                                    转
                                </SkTag>
                            </template>
                        </a>
                    </template>
                    <template v-if="column.dataIndex === 'processInstanceId'">
                        <!-- 流程id点击后的详情 -->
                        <ProcessDetail :processInstanceId="record.processInstanceId" />
                    </template>
                    <template v-if="column.dataIndex === 'state'">
                        <div
                            v-html="$util.skyeyeClassEnumUtil.getEnumDataNameByCodeAndKeyStatic(initEnumData['erpOrderStateEnum'], 'id', record.state, 'name')">
                        </div>
                    </template>
                    <template v-if="column.dataIndex === 'otherState'">
                        <div
                            v-html="$util.skyeyeClassEnumUtil.getEnumDataNameByCodeAndKeyStatic(initEnumData['depotPutState'], 'id', record.otherState, 'name')">
                        </div>
                    </template>
                    <template v-if="column.dataIndex === 'fromTypeId'">
                        <div
                            v-html="$util.skyeyeClassEnumUtil.getEnumDataNameByCodeAndKeyStatic(initEnumData['machinPutFromType'], 'id', record.fromTypeId, 'name')">
                        </div>
                    </template>
                    <template v-if="column.key === 'action'">
                        <SkSpace>
                            <template v-if="record.editRow === 1">
                                <a v-if="$config.auth('1722236830172')" @click="handleConfirmOk(record)">{{
                                    $t('common.submitApproval')
                                }}</a>
                                <SkDivider type="vertical" />
                                <a v-if="$config.auth('1722236598520')" @click="handleEdit(record)">{{ $t('common.edit')
                                    }}</a>
                                <SkDivider type="vertical" />
                                <SkPopconfirm v-if="$config.auth('1722236807894')" :title="$t('common.deleteConfirm')"
                                    @confirm="handleDelete(record)" :okText="$t('common.delete')"
                                    :cancelText="$t('common.cancel')">
                                    <a class="danger-link">{{ $t('common.delete') }}</a>
                                </SkPopconfirm>
                            </template>
                            <template v-if="record.editRow === 2">
                                <SkPopconfirm v-if="$config.auth('1616239762178')" :title="$t('common.revokeConfirm')"
                                    @confirm="handleRevoke(record)" :okText="$t('common.confirm')"
                                    :cancelText="$t('common.cancel')">
                                    <a class="danger-link">{{ $t("common.revoke") }}</a>
                                </SkPopconfirm>
                            </template>
                        </SkSpace>
                    </template>
                </template>
            </SkTable>

            <SkModal v-model="modalVisible" :title="modalTitle">
                <ShowIndex v-if="modalType === 'add' || modalType === 'edit' || modalType === 'details'"
                    :pageId="operatorParams.pageId" :params="operatorParams.params" @close="handleModalClick"
                    @afterDataLoaded="handleAfterDataLoaded" @cell-change="handleCellChange" ref="showIndexRef">
                    <!-- 传递自定义单元格组件 -->
                    <template #cell-chooseInput-materialId="slotProps">
                        <a-form-item-rest>
                            <SkMaterialSelect v-model="slotProps.record[slotProps.column.dataIndex]"
                                :formData="slotProps.record"
                                :isEdit="slotProps.column.getConfig(slotProps.record).isEdit"
                                :attrKey="slotProps.column.dataIndex"
                                @change="(material) => handleMaterialChange(material, slotProps.record, slotProps.column, slotProps.formData)" />
                        </a-form-item-rest>
                    </template>
                </ShowIndex>
                <ApprovalPersonSelect v-if="modalType === 'approval'" :actKey="currentRecord?.serviceClassName"
                    :businessData="null" @submit="handleApprovalPersonSubmit" @cancel="handleModalCancel" />
            </SkModal>
        </SkCard>
    </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance, reactive, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import { PlusOutlined, ReloadOutlined, SearchOutlined } from '@ant-design/icons-vue'
import { SkMessage } from '@/components/SkMessage/index.vue'
import SkButton from '@/components/SkButton/index.vue'
import SkForm from '@/components/SkForm/index.vue'
import SkCard from '@/components/SkCard/index.vue'
import SkModal from '@/components/SkModal/index.vue'
import SkTable from '@/components/SkTable/index.vue'
import SkSpace from '@/components/SkSpace/index.vue'
import SkInput from '@/components/SkInput/index.vue'
import SkDivider from '@/components/SkDivider/index.vue'
import SkPopconfirm from '@/components/SkPopconfirm/index.vue'
import ShowIndex from '@/views/dsForm/show/index.vue'
import SkTag from '@/components/SkTag/index.vue'
import ProcessDetail from '@/views/dsForm/process/detail.vue'
import ApprovalPersonSelect from '@/views/system/submitApproval/approvalPersonSelect.vue'
import SkMaterialSelect from '@/components/SkMaterialSelect/index.vue'

const { t } = useI18n()
const { proxy } = getCurrentInstance()
const tableReady = ref(false)

// 搜索表单数据
const searchForm = reactive({
    keyword: undefined
})

// 表格列配置
const columns = [
    {
        title: t('common.serialNum'),
        dataIndex: 'index',
        width: 80,
        align: 'center',
        fixed: 'left',
        customRender: ({ index }) => {
            return (pagination.current - 1) * pagination.pageSize + index + 1
        }
    },
    {
        title: '单号',
        dataIndex: 'oddNumber',
        width: 220,
        align: 'center'
    },
    {
        title: '流程ID',
        dataIndex: 'processInstanceId',
        width: 220,
        align: 'center'
    },
    {
        title: '状态',
        dataIndex: 'state',
        width: 150,
    },
    {
        title: '入库状态',
        dataIndex: 'otherState',
        width: 120,
        align: 'left'
    },
    {
        title: '来源单据信息',
        children: [{
            title: '来源类型',
            width: 120,
            dataIndex: 'fromTypeId',
        }, {
            title: '单据编号',
            dataIndex: 'fromId',
            width: 200,
            customRender: ({ record }) => record.fromMation?.oddNumber
        }]
    },

    {
        title: '录入时间',
        dataIndex: 'createTime',
        width: 160,
        align: 'center',
        sorter: true
    },
    {
        title: t('common.action'),
        key: 'action',
        width: 200,
        fixed: 'right',
        align: 'center'
    }
]

// 表格数据
const tableData = ref([])
const loading = ref(false)
const pagination = reactive(proxy.$config.pagination())
const modalVisible = ref(false)
const modalTitle = ref('')
const modalType = ref('')
const operatorParams = ref({})
const currentRecord = ref(null)
// 初始化枚举数据
const initEnumData = ref({})
const getInitData = async () => {
    let result = await proxy.$util.getEnumListMapByCode(['erpOrderStateEnum', 'depotPutState', 'machinPutFromType'])
    initEnumData.value = result
}
// 获取数据
const fetchData = async () => {
    loading.value = true
    try {
        const params = {
            page: Number(pagination.current || 1),
            limit: Number(pagination.pageSize || 10),
            keyword: searchForm.keyword?.trim() || ''
        }
        const res = await proxy.$http.post(
            proxy.$config.getConfig().erpBasePath + 'queryMachinPutList',
            params
        )
        tableData.value = res.rows || []
        pagination.total = res.total || 0
    } catch (error) {
        SkMessage.error(error.message || '获取数据失败')
        tableData.value = []
        pagination.total = 0
    } finally {
        loading.value = false
    }
}

// 搜索
const handleSearch = () => {
    pagination.current = 1
    fetchData()
}

// 处理表格变化
const handleTableChange = (pag) => {
    pagination.current = pag.current
    pagination.pageSize = pag.pageSize
    fetchData()
}
const showIndexRef = ref(null)

// 编辑
const handleEdit = (record) => {
    modalTitle.value = '编辑'
    modalType.value = 'edit'
    modalVisible.value = true
    operatorParams.value = {
        pageId: 'FP2024072600003',
        params: {
            id: record.id
        }
    }
}

// 详情
const handleDetail = (record) => {
    modalTitle.value = '详情'
    modalType.value = 'details'
    modalVisible.value = true
    operatorParams.value = {
        pageId: 'FP2024072600004',
        params: {
            id: record.id
        }
    }
}

// 处理数据回显示，刚获取到数据的逻辑
const handleAfterDataLoaded = (formData) => {
    // 这里可以处理特殊的参数
    if (!proxy.$util.isNull(formData.erpOrderItemList)) {
        formData.erpOrderItemList.forEach(item => {
            // 下拉框的特殊配置
            item[`normsId_config`] = {
                // 自定义类型
                dataType: 1,
                // materialNorms为计量单位的数据
                defaultData: item.materialMation?.materialNorms || []
            }
        })
    }
}

// 处理单元格变化
const handleCellChange = async ({ record, dataIndex, value, column }, formData) => {
    // 处理物料选择变化
    if (dataIndex == 'materialId') {
        record.unitPrice = 0
    } else if (dataIndex == 'normsId') {
        const materialNorms = column.getConfig(record).defaultData
        const norms = materialNorms.find(item => item.id === value)

        // 获取物料的预估采购价
        record.unitPrice = norms.salePrice
    }
}
// 删除
const handleDelete = async (record) => {
    try {
        await proxy.$http.delete(
            proxy.$config.getConfig().erpBasePath + 'deleteMachiningWarehouseById',
            { id: record.id }
        )
        SkMessage.success('删除成功')
        fetchData()
    } catch (error) {
        SkMessage.error(error.message || '删除失败')
    }
}

// 处理物料选择变化
const handleMaterialChange = async (material, record, column, formData) => {
    const dataIndex = column.dataIndex
    const mationKey = proxy.$util.getKeyIdToMation(dataIndex)

    if (material) {
        record[mationKey] = material
        record["normsId"] = undefined
        record["unitPrice"] = 0

        await nextTick()

        showIndexRef.value?.writeComponentRef?.updateTableColumns(column.pIdDataIndex, record, {
            "normsId": {
                dataType: 1,
                defaultData: material.materialNorms
            }
        })

        handleCellChange({
            record,
            dataIndex: column.dataIndex,
            value: material,
            column: column
        }, formData)
    } else {
        delete record[mationKey]
    }
}

// 提交审批
const handleConfirmOk = async (record) => {
    try {
        await nextTick()
        modalTitle.value = '审批人选择'
        modalType.value = 'approval'
        currentRecord.value = record
        modalVisible.value = true
    } catch (error) {
        SkMessage.error('操作失败')
    }
}

// 处理审批人选择提交
const handleApprovalPersonSubmit = async (person) => {
    try {
        await proxy.$http.put(
            proxy.$config.getConfig().erpBasePath + 'erpcommon006',
            {
                id: currentRecord.value.id,
                approvalId: person.id,
                serviceClassName: 'com.skyeye.machin.service.impl.MachinPutServiceImpl'
            }
        )
        SkMessage.success('提交审批成功')
        modalVisible.value = false
        fetchData()
    } catch (error) {
        SkMessage.error(error.message || '提交审批失败')
    }
}

// 处理审批人选择取消
const handleModalCancel = () => {
    modalVisible.value = false
}

// 撤销
const handleRevoke = async (record) => {
    try {
        await proxy.$http.put(
            proxy.$config.getConfig().erpBasePath + 'erpcommon003',
            {
                id: record.id,
                processInstanceId: record.processInstanceId,
                serviceClassName: record.serviceClassName
            }
        )
        SkMessage.success('撤销成功')
        fetchData()
    } catch (error) {
        SkMessage.error(error.message || '撤销失败')
    }
}

// 弹窗确认方法
const handleModalClick = (isSubmit) => {
    modalVisible.value = false
    if (isSubmit) {
        fetchData()
    }
}

// 初始化
onMounted(async () => {
    await nextTick()
    requestAnimationFrame(() => {
        tableReady.value = true
    })
    await getInitData() // 添加这行
    fetchData()
})
</script>

<style scoped></style>