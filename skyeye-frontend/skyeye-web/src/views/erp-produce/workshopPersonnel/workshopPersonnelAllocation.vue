<template>
    <div class="container-manage">
        <!-- 左侧车间列表 -->
        <div class="left-tree">
            <SkCard title="车间" :bordered="false">
                <!-- 添加搜索框 -->
                <div class="list-search">
                    <SkInput v-model="warehouseSearchText" placeholder="搜索车间" @change="handleWarehouseSearch"
                        allowClear />
                </div>
                <SkTable :columns="warehouseColumns" :data-source="warehouseData" :loading="warehouseLoading"
                    :pagination="false" :ready="tableReady" :customRow="customWarehouseRow">
                    <template #bodyCell="{ column, record }">
                        <template v-if="column.dataIndex === 'name'">
                            <a @click="handleWarehouseSelect(record)"
                                :class="{ 'text-primary': currentDepotId === record.id }">
                                {{ record.name }}
                            </a>
                        </template>
                    </template>
                </SkTable>
            </SkCard>
        </div>

        <!-- 右侧员工列表 -->
        <div class="right-content">
            <SkCard title="员工" :bordered="false">
                <!-- 操作按钮 -->
                <div class="table-operations">
                    <SkSpace>
                        <SkUserStaffSelectForButton v-if="$config.auth('1721305232197')" @click="handleAddClick"
                            @success="handleStaffSelect" />
                        <SkButton type="primary" @click.prevent="loadStaff">
                            <template #icon>
                                <ReloadOutlined />
                            </template>
                            {{ $t('common.refresh') }}
                        </SkButton>
                    </SkSpace>
                </div>

                <SkTable :columns="staffColumns" :data-source="staffData" :loading="staffLoading"
                    :pagination="pagination" :ready="tableReady" @change="handleTableChange">
                    <template #bodyCell="{ column, record }">
                        <template v-if="column.key === 'action'">
                            <SkSpace>
                                <SkPopconfirm v-if="$config.auth('1721305310416')" :title="$t('common.deleteConfirm')"
                                    @confirm="handleDelete(record)" :okText="$t('common.delete')"
                                    :cancelText="$t('common.cancel')">
                                    <a class="danger-link">{{ $t('common.delete') }}</a>
                                </SkPopconfirm>
                            </SkSpace>
                        </template>
                    </template>
                </SkTable>
            </SkCard>
        </div>
    </div>
</template>

<script setup>
import { ref, reactive, getCurrentInstance, onMounted, nextTick } from 'vue'
import { ReloadOutlined } from '@ant-design/icons-vue'
import SkTable from '@/components/SkTable/index.vue'
import SkButton from '@/components/SkButton/index.vue'
import SkSpace from '@/components/SkSpace/index.vue'
import SkCard from '@/components/SkCard/index.vue'
import SkPopconfirm from '@/components/SkPopconfirm/index.vue'
import { SkMessage } from '@/components/SkMessage/index.vue'
import SkInput from '@/components/SkInput/index.vue'
import SkUserStaffSelectForButton from '@/components/SkUserStaffSelect/indexForButton.vue'
import { useI18n } from 'vue-i18n'

const { proxy } = getCurrentInstance()
const { t } = useI18n()

// 车间表格配置
const warehouseColumns = ref([
    {
        title: '序号',
        dataIndex: 'index',
        width: 80,
        align: 'center',
        customRender: ({ index }) => index + 1
    },
    {
        title: '车间',
        dataIndex: 'name',
        width: 100,
    }
])

// 员工表格配置
const staffColumns = ref([
    {
        title: t('common.serialNum'),
        dataIndex: 'index',
        width: 80,
        align: 'center',
        fixed: 'left',
        customRender: ({ index }) => {
            return (pagination.current - 1) * pagination.pageSize + index + 1
        }
    },
    {
        title: '工号',
        dataIndex: 'jobNumber',
        width: 140,
        align: 'center',
        customRender: ({ record }) => record.staffMation?.jobNumber
    },
    {
        title: '姓名',
        dataIndex: 'userName',
        width: 120,
        customRender: ({ record }) => record.staffMation?.userName
    },
    {
        title: '企业',
        dataIndex: 'companyName',
        width: 150,
        customRender: ({ record }) => record.staffMation?.companyName
    },
    {
        title: '部门',
        dataIndex: 'departmentName',
        width: 140,
        customRender: ({ record }) => record.staffMation?.departmentName
    },
    {
        title: '职位',
        dataIndex: 'jobName',
        width: 140,
        customRender: ({ record }) => record.staffMation?.jobName
    },
    {
        title: t('common.action'),
        key: 'action',
        width: 200,
        fixed: 'right',
        align: 'center'
    }
])

// 车间相关数据
const warehouseSearchText = ref('')
const originalWarehouseData = ref([])
const warehouseData = ref([])
const warehouseLoading = ref(false)
const staffData = ref([])
const staffLoading = ref(false)
const tableReady = ref(false)
const pagination = reactive(proxy.$config.pagination())
const currentDepotId = ref('')

// 加载车间列表
const loadWarehouse = async () => {
    warehouseLoading.value = true
    try {
        const res = await proxy.$http.get(
            proxy.$config.getConfig().erpBasePath + 'queryMyChargeFarmList'
        )
        // 保存原始数据
        originalWarehouseData.value = res.rows || []
        // 设置显示数据
        warehouseData.value = res.rows || []
        currentDepotId.value = ''
        loadStaff()
    } catch (error) {
        SkMessage.error('获取车间列表失败')
        originalWarehouseData.value = []
        warehouseData.value = []
    } finally {
        warehouseLoading.value = false
    }
}

// 加载员工列表
const loadStaff = async () => {
    staffLoading.value = true
    try {
        const params = {
            objectId: currentDepotId.value,
            page: Number(pagination.current || 1),
            limit: Number(pagination.pageSize || 10)
        }

        const res = await proxy.$http.post(
            proxy.$config.getConfig().erpBasePath + 'queryFarmStaffList',
            params
        )

        staffData.value = res.rows || []
        pagination.total = res.total || 0
    } catch (error) {
        SkMessage.error('获取员工列表失败')
        staffData.value = []
        pagination.total = 0
    } finally {
        staffLoading.value = false
    }
}

// 搜索车间
const handleWarehouseSearch = (value) => {
    if (proxy.$util.isNull(value.value)) {
        warehouseData.value = JSON.parse(JSON.stringify(originalWarehouseData.value))
        return
    }
    warehouseData.value = originalWarehouseData.value.filter(item =>
        item.name.toLowerCase().includes(value.value.toLowerCase())
    )
}

// 自定义行属性
const customWarehouseRow = (record) => {
    return {
        class: record.id === currentDepotId.value ? 'state-success' : '',
        onClick: () => handleWarehouseSelect(record)
    }
}

// 选择车间
const handleWarehouseSelect = (record) => {
    currentDepotId.value = record.id
    // 重置分页
    pagination.current = 1
    loadStaff()
}

// 表格变化处理
const handleTableChange = (pag) => {
    if (pag) {
        pagination.current = Number(pag.current || 1)
        pagination.pageSize = Number(pag.pageSize || 10)
    }
    loadStaff()
}

// 处理添加按钮点击
const handleAddClick = (callback) => {
    if (!currentDepotId.value) {
        SkMessage.warning('请先选择车间信息')
        callback(false)
        return
    }
    callback(true)
}

// 处理员工选择
const handleStaffSelect = async (staffs) => {
    try {
        const params = {
            farmId: currentDepotId.value,
            staffId: JSON.stringify(staffs.map(staff => staff.id))
        }

        await proxy.$http.post(
            proxy.$config.getConfig().erpBasePath + 'insertFarmStaff',
            params
        )

        loadStaff() // 重新加载员工列表
    } catch (error) {
        SkMessage.error('添加失败')
    }
}

// 删除
const handleDelete = async (record) => {
    try {
        await proxy.$http.delete(
            proxy.$config.getConfig().erpBasePath + 'deleteFarmStaffById',
            { id: record.id }
        )
        SkMessage.success('删除成功')
        loadStaff()
    } catch (error) {
        SkMessage.error('删除失败')
    }
}

// 初始化
onMounted(async () => {
    nextTick(async () => {
        tableReady.value = true
    })
    await loadWarehouse()

})
</script>

<style scoped>
:deep(.ant-table-row.state-success) {
    background-color: #e6f7ff;
}
</style>