<template>
    <div class="container-manage">
        <SkCard ref="cardRef" :bordered="false">
            <div class="table-search">
                <SkForm layout="inline" v-model="searchForm" @submit="handleSearch" @reset="handleReset"
                    :submitText="$t('common.search')" :resetText="$t('common.reset')" :isFormSubmit="false">
                    <a-form-item name="workshopId">
                        <SkSelect v-model="searchForm.workshopId" style="width: 200px" placeholder="请选择车间"
                            :options="workshopOptions" allow-clear show-search @change="handleSearch" />
                    </a-form-item>
                </SkForm>
            </div>
            <div class="table-operations">
                <SkSpace>
                    <SkButton type="primary" @click.prevent="fetchData">
                        <template #icon>
                            <ReloadOutlined />
                        </template>
                        {{ $t('common.refresh') }}
                    </SkButton>
                </SkSpace>
            </div>
            <SkTable :columns="columns" :data-source="tableData" :loading="loading" :pagination="pagination"
                :ready="tableReady" @change="handleTableChange">
                <template #bodyCell="{ column, record }">
                    <template v-if="column.dataIndex === 'stock'">
                        <a @click="handleInventory(record)">{{ record.stock }}</a>
                    </template>
                </template>
            </SkTable>
        </SkCard>

        <!-- 库存详情弹窗 -->
        <SkModal v-model="modalVisible" :title="modalTitle" :width="modalWidth">
            <Detail :materialId="materialId" :normsId="normsId" :farmId="farmId" />
        </SkModal>
    </div>
</template>

<script setup>
import { reactive, ref, getCurrentInstance, onMounted, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import { ReloadOutlined } from '@ant-design/icons-vue'
import SkForm from '@/components/SkForm/index.vue'
import SkCard from '@/components/SkCard/index.vue'
import SkButton from '@/components/SkButton/index.vue'
import SkSpace from '@/components/SkSpace/index.vue'
import SkTable from '@/components/SkTable/index.vue'
import SkModal from '@/components/SkModal/index.vue'
import SkSelect from '@/components/SkSelect/index.vue'
import { SkMessage } from '@/components/SkMessage/index.vue'
import Detail from './detail.vue'

const { proxy } = getCurrentInstance()
const { t } = useI18n()

// 搜索表单数据
const searchForm = reactive({
    workshopId: undefined
})

// 车间选项
const workshopOptions = ref([])

// 表格列配置
const columns = ref([
    {
        title: t('common.serialNum'),
        dataIndex: 'index',
        width: 80,
        align: 'center',
        customRender: ({ index }) => {
            return (pagination.current - 1) * pagination.pageSize + index + 1
        }
    },
    {
        title: '产品',
        dataIndex: 'materialMation',
        width: 120,
        customRender: ({ record }) => {
            return record.materialMation?.name
        }
    },
    {
        title: '规格',
        dataIndex: 'normsMation',
        width: 200,
        customRender: ({ record }) => {
            return record.normsMation?.name
        }
    },
    {
        title: '来源部门',
        dataIndex: 'departmentMation',
        width: 200,
        customRender: ({ record }) => {
            return record.departmentMation?.name
        }
    },
    {
        title: '车间库存',
        dataIndex: 'stock',
        width: 80,
        align: 'center'
    }
])

const tableData = ref([])
const loading = ref(false)
const tableReady = ref(false)

// 分页配置
const pagination = reactive(proxy.$config.pagination())

// 弹窗相关
const modalVisible = ref(false)
const modalTitle = ref('车间商品库存详情')
const modalWidth = ref('70%')
const materialId = ref(undefined)
const normsId = ref(undefined)
const farmId = ref(undefined)

// 获取车间列表
const fetchWorkshopList = async () => {
    try {
        const res = await proxy.$http.get(
            proxy.$config.getConfig().erpBasePath + 'queryStaffBelongFarmList'
        )
        workshopOptions.value = res.rows?.map(item => ({
            label: item.name,
            value: item.id
        })) || []
    } catch (error) {
        SkMessage.error('获取车间列表失败')
    }
}

// 获取表格数据
const fetchData = async () => {
    loading.value = true
    try {
        const params = {
            page: Number(pagination.current || 1),
            limit: Number(pagination.pageSize || 10),
            type: 'farm',
            objectId: searchForm.workshopId || ''
        }

        const res = await proxy.$http.post(
            proxy.$config.getConfig().erpBasePath + 'erpdepartstock001',
            params
        )

        tableData.value = res.rows || []
        pagination.total = res.total || 0
    } catch (error) {
        SkMessage.error('获取数据失败')
        tableData.value = []
        pagination.total = 0
    } finally {
        loading.value = false
    }
}

// 查看库存详情
const handleInventory = (record) => {
    materialId.value = record.materialId
    normsId.value = record.normsId
    farmId.value = record.farmId
    modalVisible.value = true
}

// 事件处理 搜索
const handleSearch = () => {
    pagination.current = 1 // 重置到第一页
    fetchData()
}

// 添加重置处理函数
const handleReset = () => {
    searchForm.workshopId = undefined // 清空搜索关键字
    pagination.current = 1 // 重置到第一页
    fetchData()
}

// 表格变化处理
const handleTableChange = (pag) => {
    if (pag) {
        pagination.current = Number(pag.current || 1)
        pagination.pageSize = Number(pag.pageSize || 10)
    }
    fetchData()
}

// 初始化
onMounted(async () => {
    await nextTick()
    requestAnimationFrame(() => {
        tableReady.value = true
    })
    await fetchWorkshopList()
    await fetchData()
})
</script>

<style scoped></style>