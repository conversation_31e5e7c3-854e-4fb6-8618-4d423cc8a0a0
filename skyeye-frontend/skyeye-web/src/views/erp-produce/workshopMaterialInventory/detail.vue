<template>
    <div class="container-manage">
        <SkCard ref="cardRef" :bordered="false">
            <div class="table-btn-group">
                <sk-segmented v-model="authMation.type" :options="authOoptions" @change="handleChange">
                    <template #default="{ item }">
                        <div style="padding: 4px 0">
                            <div style="margin-top: 4px">{{ item.label }}</div>
                        </div>
                    </template>
                </sk-segmented>
            </div>
            <div class="table-search">
                <SkForm layout="inline" v-model="searchForm" @submit="handleSearch" @reset="handleReset"
                    :submitText="$t('common.search')" :resetText="$t('common.reset')" :isFormSubmit="false">
                    <template #submitIcon>
                        <search-outlined />
                    </template>
                    <a-form-item name="keyword">
                        <SkInput v-model="searchForm.keyword" placeholder="请输入条形码" allowClear
                            @pressEnter="handleSearch" />
                    </a-form-item>
                </SkForm>
            </div>
            <div class="table-operations">
                <SkSpace>
                    <SkButton type="primary" @click.prevent="fetchData">
                        <template #icon>
                            <ReloadOutlined />
                        </template>
                        {{ $t('common.refresh') }}
                    </SkButton>
                </SkSpace>
            </div>
            <SkTable :columns="columns" :data-source="tableData" :loading="loading" :pagination="pagination"
                :ready="tableReady" @change="handleTableChange">
                <template #bodyCell="{ column, record }">
                    <template v-if="column.dataIndex === 'pickUseState'">
                        <div
                            v-html="$util.skyeyeClassEnumUtil.getEnumDataNameByCodeAndKeyStatic(initEnumData['pickNormsCodeUseState'], 'id', record.pickUseState, 'name')">
                        </div>
                    </template>
                    <template v-if="column.dataIndex === 'pickState'">
                        <div
                            v-html="$util.skyeyeClassEnumUtil.getEnumDataNameByCodeAndKeyStatic(initEnumData['machinProcedureAcceptChildType'], 'id', record.pickState, 'name')">
                        </div>
                    </template>
                </template>
            </SkTable>
        </SkCard>
    </div>
</template>
<script setup>
import { reactive, ref, getCurrentInstance, onMounted, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import SkCard from '@/components/SkCard/index.vue'
import SkButton from '@/components/SkButton/index.vue'
import SkSpace from '@/components/SkSpace/index.vue'
import SkTable from '@/components/SkTable/index.vue'
import { SkMessage } from '@/components/SkMessage/index.vue'
import SkForm from '@/components/SkForm/index.vue'
import SkInput from '@/components/SkInput/index.vue'
import SkSegmented from '@/components/SkSegmented/index.vue'

const { proxy } = getCurrentInstance()
const { t } = useI18n()

const props = defineProps({
    materialId: {
        type: [String, Number],
        default: ''
    },
    normsId: {
        type: [String, Number],
        default: ''
    },
    farmId: {
        type: [String, Number],
        default: ''
    }
})

// 搜索表单数据
const searchForm = reactive({
    keyword: ''
})

// 表格列配置
const columns = ref([
    {
        title: t('common.serialNum'),
        dataIndex: 'index',
        width: 80,
        align: 'center',
        fixed: 'left',
        customRender: ({ index }) => {
            // 计算实际序号 = (当前页码 - 1) * 每页条数 + 当前行索引 + 1
            return (pagination.current - 1) * pagination.pageSize + index + 1
        }
    },
    {
        title: '产品',
        dataIndex: 'materialMation',
        width: 150,
        customRender: ({ record }) => record.materialMation?.name
    },
    {
        title: '规格',
        dataIndex: 'normsMation',
        width: 150,
        customRender: ({ record }) => record.normsMation?.name
    },
    {
        title: '条形码',
        dataIndex: 'codeNum',
        align: 'center',
        width: 200
    },
    {
        title: '来源部门',
        dataIndex: 'departmentMation',
        width: 140,
        customRender: ({ record }) => record.departmentMation?.name
    },
    {
        title: '来源仓库',
        dataIndex: 'depotMation',
        width: 100,
        customRender: ({ record }) => record.depotMation?.name
    },
    {
        title: '车间加工使用结果',
        dataIndex: 'pickState',
        width: 150,
        align: 'center'
    },
    {
        title: '使用状态',
        dataIndex: 'pickUseState',
        width: 100,
        align: 'center'
    }
])

// 初始化枚举数据
const initEnumData = ref({})
const getInitData = async () => {
    let result = await proxy.$util.getEnumListMapByCode(['pickNormsCodeUseState', 'machinProcedureAcceptChildType']);
    initEnumData.value = result
}

const tableData = ref([]) // 表格数据
const loading = ref(false) // 加载状态
const tableReady = ref(false)

// 权限点处理
const authMation = ref({
    type: 'noUse'
})
const authOoptions = ref([{
    label: '所有',
    value: 'all'
}, {
    label: '未使用',
    value: 'noUse'
}, {
    label: '已使用',
    value: 'used'
}])
const handleChange = (value) => {
    authMation.value.type = value
    pagination.current = 1
    fetchData()
}

// 分页配置
const pagination = reactive(proxy.$config.pagination())

// 获取数据
const fetchData = async () => {
    loading.value = true
    try {
        const params = {
            page: Number(pagination.current || 1),
            limit: Number(pagination.pageSize || 10),
            materialId: props.materialId,
            normsId: props.normsId,
            farmId: props.farmId,
            ...authMation.value,
            ...searchForm
        }

        // 发送查询请求
        const res = await proxy.$http.post(
            proxy.$config.getConfig().erpBasePath + 'queryNormsStockDetailList',
            params
        )

        tableData.value = res.rows || []
        pagination.total = res.total || 0
    } catch (error) {
        SkMessage.error('获取数据失败')
        tableData.value = []
        pagination.total = 0
    } finally {
        loading.value = false
    }
}

// 表格变化处理
const handleTableChange = (pag) => {
    if (pag) {
        pagination.current = Number(pag.current || 1)
        pagination.pageSize = Number(pag.pageSize || 10)
    }
    fetchData()
}

// 事件处理 搜索
const handleSearch = () => {
    pagination.current = 1 // 重置到第一页
    fetchData()
}

// 添加重置处理函数
const handleReset = () => {
    searchForm.keyword = '' // 清空搜索关键字
    pagination.current = 1 // 重置到第一页
    fetchData()
}

// 初始化
onMounted(async () => {
    await nextTick()
    requestAnimationFrame(() => {
        tableReady.value = true
    })
    await getInitData()
    await fetchData()
})

</script>