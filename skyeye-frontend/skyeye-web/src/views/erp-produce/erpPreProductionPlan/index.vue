<template>
	<div class="container-manage">
		<SkCard ref="cardRef" :bordered="false">
			<!-- 操作按钮 -->
			<div class="table-search">
				<SkForm layout="inline" v-model="searchForm" @submit="handleSearch" @reset="handleReset"
					:submitText="$t('common.search')" :resetText="$t('common.reset')" :isFormSubmit="false">
					<template #submitIcon>
						<search-outlined />
					</template>
					<a-form-item name="keyword">
						<SkInput v-model="searchForm.keyword" placeholder="请输入单号" allowClear
							@pressEnter="handleSearch" />
					</a-form-item>
				</SkForm>
			</div>
			<div class="table-operations">
				<SkSpace>
					<SkButton v-if="$config.auth('1720705272841')" type="primary" @click.prevent="handleAdd">
						<template #icon><plus-outlined /></template>
						{{ $t('common.add') }}
					</SkButton>
					<SkButton type="primary" @click.prevent="fetchData">
						<template #icon>
							<ReloadOutlined />
						</template>
						{{ $t('common.refresh') }}
					</SkButton>
				</SkSpace>
			</div>

			<!-- 表格 -->
			<SkTable :columns="columns" :data-source="tableData" :loading="loading" :pagination="pagination"
				:ready="tableReady" @change="handleTableChange">
				<template #bodyCell="{ column, record }">
					<template v-if="column.dataIndex === 'oddNumber'">
						<a @click="handleDetail(record)">
							{{ record.oddNumber }}
							<template v-if="!$util.isNull(record.fromId)">
								<SkTag :color="$util.getTagColor(column.dataIndex)">
									转
								</SkTag>
							</template>
						</a>
					</template>
					<template v-if="column.dataIndex === 'processInstanceId'">
						<!-- 流程id点击后的详情 -->
						<ProcessDetail :processInstanceId="record.processInstanceId" />
					</template>
					<!-- 获取来源类型 -->
					<template v-if="column.dataIndex === 'fromTypeId'">
						<div
							v-html="$util.skyeyeClassEnumUtil.getEnumDataNameByCodeAndKeyStatic(initEnumData['productionPlanFromType'], 'id', record.fromTypeId, 'name')">
						</div>
					</template>
					<!-- 获取状态 -->
					<template v-if="column.dataIndex === 'state'">
						<div
							v-html="$util.skyeyeClassEnumUtil.getEnumDataNameByCodeAndKeyStatic(initEnumData['erpOrderStateEnum'], 'id', record.state, 'name')">
						</div>
					</template>
					<!-- 获取采购状态 -->
					<template v-if="column.dataIndex === 'purchaseState'">
						<div
							v-html="$util.skyeyeClassEnumUtil.getEnumDataNameByCodeAndKeyStatic(initEnumData['productionPlanPurchaseState'], 'id', record.purchaseState, 'name')">
						</div>
					</template>
					<!-- 获取生产状态 -->
					<template v-if="column.dataIndex === 'produceState'">
						<div
							v-html="$util.skyeyeClassEnumUtil.getEnumDataNameByCodeAndKeyStatic(initEnumData['productionPlanProduceState'], 'id', record.produceState, 'name')">
						</div>
					</template>
					<!-- 操作列 -->
					<template v-if="column.key === 'action'">
						<SkSpace>
							<template v-if="record.editRow == 1">
								<!-- 提交审批 -->
								<a v-if="$config.auth('1720705286166')" @click="handleConfirmOk(record)">
									{{ $t('common.submitApproval') }}
								</a>
								<SkDivider v-if="$config.auth('1720705286166')" type="vertical" />

								<!-- 编辑 -->
								<a v-if="$config.auth('1720705272841')" @click="handleEdit(record, 'edit')">
									{{ $t('common.edit') }}
								</a>
								<SkDivider v-if="$config.auth('1720705272841')" type="vertical" />

								<!-- 删除 -->
								<SkPopconfirm v-if="$config.auth('1720705252808')" :title="$t('common.deleteConfirm')"
									@confirm="handleDelete(record)" :okText="$t('common.delete')"
									:cancelText="$t('common.cancel')">
									<a class="danger-link">{{ $t('common.delete') }}</a>
								</SkPopconfirm>
							</template>

							<template v-if="record.editRow == 2">
								<!-- 撤销 -->
								<SkPopconfirm v-if="$config.auth('1720705233677')" :title="$t('common.revokeConfirm')"
									@confirm="handleRevoke(record)" :okText="$t('common.confirm')"
									:cancelText="$t('common.cancel')">
									<a class="danger-link">{{ $t('common.revoke') }}</a>
								</SkPopconfirm>
							</template>

							<template
								v-if="record.state == 'pass' && (record.produceState == 2 || record.produceState == 3)">
								<!-- 转生产计划 -->
								<a v-if="$config.auth('1720705318422')" @click="transferProduct(record)">
									{{ $t('erpProduce.productionPlan.transferProduct') }}
								</a>
							</template>

							<template
								v-if="record.state == 'pass' && (record.purchaseState == 2 || record.purchaseState == 3)">
								<!-- 转采购订单 -->
								<a v-if="$config.auth('1729643581348')" @click="transferPurchase(record)">
									{{ $t('erpProduce.productionPlan.transferPurchase') }}
								</a>
							</template>

						</SkSpace>
					</template>
				</template>
			</SkTable>

			<!-- 弹窗 -->
			<SkModal v-model="modalVisible" :title="modalTitle">
				<ShowIndex ref="showIndexRef"
					v-if="modalType === 'add' || modalType === 'edit' || modalType === 'details' || modalType === 'transferProduct' || modalType === 'transferPurchase'"
					:pageId="operatorParams.pageId" :params="operatorParams.params" @customerDataSave="customerDataSave"
					:whetherCustomerData="whetherCustomerData" :customerData="customerData" @close="handleModalClick"
					@cell-change="handleCellChange" @loaded="handleShowIndexLoaded"
					@afterDataLoaded="handleAfterDataLoaded" @handleChange="handleChange">
					<!-- 传递自定义单元格组件 -->
					<template #cell-chooseInput-materialId="slotProps">
						<a-form-item-rest>
							<SkMaterialSelect v-model="slotProps.record[slotProps.column.dataIndex]"
								:formData="slotProps.record"
								:isEdit="slotProps.column.getConfig(slotProps.record).isEdit"
								:attrKey="slotProps.column.dataIndex"
								@change="(material) => handleMaterialChange(material, slotProps.record, slotProps.column, slotProps.formData)" />
						</a-form-item-rest>
					</template>
					<!-- 计划开始时间 -->
					<template #cell-input-planStartTime="slotProps">
						<SkDatePicker v-model="slotProps.record[slotProps.column.dataIndex]"
							:formData="slotProps.record" :isEdit="slotProps.column.getConfig(slotProps.record).isEdit"
							:attrKey="slotProps.column.dataIndex" />
					</template>
					<!-- 计划结束时间 -->
					<template #cell-input-planEndTime="slotProps">
						<SkDatePicker v-model="slotProps.record[slotProps.column.dataIndex]"
							:formData="slotProps.record" :isEdit="slotProps.column.getConfig(slotProps.record).isEdit"
							:attrKey="slotProps.column.dataIndex" />
					</template>
					<!-- 交货日期 -->
					<template #cell-input-deliveryTime="slotProps">
						<SkDatePicker v-model="slotProps.record[slotProps.column.dataIndex]"
							:formData="slotProps.record" :isEdit="slotProps.column.getConfig(slotProps.record).isEdit"
							:attrKey="slotProps.column.dataIndex" />
					</template>
				</ShowIndex>
				<!-- 审批人弹窗 -->
				<ApprovalPersonSelect v-if="modalType === 'approval'" :actKey="currentRecord?.serviceClassName"
					:businessData="null" @submit="handleApprovalPersonSubmit" @cancel="handleModalCancel" />
			</SkModal>
		</SkCard>
	</div>
</template>

<script setup>
import { reactive, ref, getCurrentInstance, onMounted, nextTick, h } from 'vue'
import { useI18n } from 'vue-i18n'
import SkTag from '@/components/SkTag/index.vue'
import SkInput from '@/components/SkInput/index.vue'
import SkCard from '@/components/SkCard/index.vue'
import SkForm from '@/components/SkForm/index.vue'
import SkTable from '@/components/SkTable/index.vue'
import SkSpace from '@/components/SkSpace/index.vue'
import SkButton from '@/components/SkButton/index.vue'
import { SkMessage } from '@/components/SkMessage/index.vue'
import SkDivider from '@/components/SkDivider/index.vue'
import SkPopconfirm from '@/components/SkPopconfirm/index.vue'
import SkModal from '@/components/SkModal/index.vue'
import ApprovalPersonSelect from '@/views/system/submitApproval/approvalPersonSelect.vue'
import ShowIndex from '@/views/dsForm/show/index.vue'
import SkMaterialSelect from '@/components/SkMaterialSelect/index.vue'
import SkDatePicker from '@/components/SkDatePicker/index.vue'
import ProcessDetail from '@/views/dsForm/process/detail.vue'
import util from '@/plugins/util.js'

const { proxy } = getCurrentInstance()
const { t } = useI18n()

// 初始化枚举数据
const initEnumData = ref({})
const getInitData = async () => {
	let result = await proxy.$util.getEnumListMapByCode(['erpOrderStateEnum', 'productionPlanPurchaseState', 'productionPlanProduceState', 'productionPlanFromType']);
	initEnumData.value = result
}

// 搜索表单数据
const searchForm = reactive({
	keyword: ''
})

// 表格列配置
const columns = ref([
	{
		title: t('common.serialNum'),
		dataIndex: 'index',
		width: 80,
		align: 'center',
		fixed: 'left',
		customRender: ({ index }) => {
			// 计算实际序号 = (当前页码 - 1) * 每页条数 + 当前行索引 + 1
			return (pagination.current - 1) * pagination.pageSize + index + 1
		}
	},
	{
		title: '单号',
		dataIndex: 'oddNumber',
		width: 200,
		align: 'center'
	},
	{
		title: '来源单据信息',
		width: 300,
		align: 'center',
		children: [{
			title: '来源类型',
			dataIndex: 'fromTypeId',
			width: 120,
			align: 'center'
		},
		{
			title: '单据编号',
			dataIndex: 'sourceNumber',
			width: 180,
			align: 'center',
			customRender: ({ record }) => record.fromMation?.oddNumber
		}]
	},
	{
		title: '单据日期',
		dataIndex: 'operTime',
		width: 120,
		align: 'center',
	},
	{
		title: '流程ID',
		dataIndex: 'processInstanceId',
		width: 120,
		align: 'center'
	},
	{
		title: '状态',
		dataIndex: 'state',
		width: 100,
		align: 'center'
	},
	{
		title: '采购状态',
		dataIndex: 'purchaseState',
		width: 100,
		align: 'center'
	},
	{
		title: '生产状态',
		dataIndex: 'produceState',
		width: 100,
		align: 'center'
	},
	{
		title: '创建人',
		dataIndex: 'createName',
		width: 120,
		align: 'center',
	},
	{
		title: '创建时间',
		dataIndex: 'createTime',
		width: 160,
		align: 'center',
	},
	{
		title: '最后修改人',
		dataIndex: 'lastUpdateName',
		width: 120,
		align: 'center'
	},
	{
		title: '最后修改时间',
		dataIndex: 'lastUpdateTime',
		width: 160,
		align: 'center'
	},
	{
		title: t('common.operation'),
		key: 'action',
		width: 250,
		align: 'center',
		fixed: 'right'
	}
])

const tableData = ref([]) // 表格数据
const loading = ref(false) // 加载状态
const tableReady = ref(false)

// 分页配置
const pagination = reactive(proxy.$config.pagination())

// 获取数据
const fetchData = async () => {
	loading.value = true
	try {
		// 构建查询参数，添加 keyword
		const params = {
			page: Number(pagination.current || 1),
			limit: Number(pagination.pageSize || 10),
			keyword: searchForm.keyword?.trim() || '', // 添加关键字搜索参数
		}
		// 发送查询请求
		const res = await proxy.$http.post(
			proxy.$config.getConfig().erpBasePath + 'queryProductionPlanList',
			params
		)

		tableData.value = res.rows || []
		pagination.total = res.total || 0
	} catch (error) {
		SkMessage.error('获取数据失败')
		tableData.value = []
		pagination.total = 0
	} finally {
		loading.value = false
	}
}

// 事件处理 搜索
const handleSearch = () => {
	pagination.current = 1 // 重置到第一页
	fetchData()
}

// 添加重置处理函数
const handleReset = () => {
	searchForm.keyword = '' // 清空搜索关键字
	pagination.current = 1 // 重置到第一页
	fetchData()
}

// 表格变化处理
const handleTableChange = (pag, filters, sorter) => {
	if (pag) {
		pagination.current = Number(pag.current || 1)
		pagination.pageSize = Number(pag.pageSize || 10)
	}
	fetchData()
}

const modalVisible = ref(false)
const modalTitle = ref('审批人选择')
const modalType = ref('approval')
const whetherCustomerData = ref(false)
const customerData = ref({})
const operatorParams = ref({})

// 添加
const handleAdd = () => {
	modalTitle.value = '新增'
	modalType.value = 'add'
	modalVisible.value = true
	whetherCustomerData.value = false
	operatorParams.value = {
		pageId: 'FP2024071100001',
		params: {}
	}
}

// 编辑
const handleEdit = (record) => {
	modalTitle.value = '编辑'
	modalType.value = 'edit'
	modalVisible.value = true
	whetherCustomerData.value = false
	operatorParams.value = {
		pageId: 'FP2024071100002',
		params: {
			id: record.id
		}
	}
}

// 删除
const handleDelete = async (record) => {
	try {
		const params = {
			id: record.id,
			serviceClassName: record.serviceClassName
		}
		await proxy.$http.delete(
			proxy.$config.getConfig().erpBasePath + 'deleteProductionPlan',
			params
		)
		SkMessage.success('删除成功')
		fetchData() // 刷新数据
	} catch (error) {
		SkMessage.error('删除失败')
	}
}
// 撤销处理
const handleRevoke = async (record) => {
	try {
		const params = {
			processInstanceId: record.processInstanceId,
			serviceClassName: record.serviceClassName
		}

		await proxy.$http.put(
			proxy.$config.getConfig().erpBasePath + 'revokeProductionPlan',
			params
		)

		SkMessage.success('撤销成功')
		await fetchData() // 刷新数据
	} catch (error) {
		SkMessage.error('撤销失败')
	}
}

// 详情
const handleDetail = (record) => {
	modalTitle.value = '详情'
	modalType.value = 'details'
	modalVisible.value = true
	whetherCustomerData.value = false
	operatorParams.value = {
		pageId: 'FP2024071100003',
		params: {
			id: record.id
		}
	}
}

// 转生产计划
const transferProduct = async (record) => {
	modalTitle.value = '转生产计划'
	modalType.value = 'transferProduct'

	const res = await proxy.$http.get(
		proxy.$config.getConfig().erpBasePath + 'queryProductionPlanTransById',
		{
			id: record.id
		})
	whetherCustomerData.value = true
	customerData.value = res.bean
	customerData.value.productionChildList = customerData.value.productionPlanChildList

	modalVisible.value = true
	operatorParams.value = {
		pageId: 'FP2023092200002',
		params: {
			id: record.id
		}
	}
}

// 转采购订单
const transferPurchase = async (record) => {
	modalTitle.value = '转采购订单'
	modalType.value = 'transferPurchase'

	const res = await proxy.$http.get(
		proxy.$config.getConfig().erpBasePath + 'queryProductionPlanTransPurchaseOrderById',
		{
			id: record.id
		})
	// 是否自定义业务数据来源以及保存接口的操作
	whetherCustomerData.value = true
	customerData.value = res.bean
	customerData.value.erpOrderItemList = customerData.value.productionPlanChildList
	customerData.value.erpOrderItemList.forEach(item => {
		item.unitPrice = parseFloat(item.normsMation.estimatePurchasePrice).toFixed(2);
		item.taxRate = 0;
	});
	// 实现回显
	customerData.value.erpOrderItemList = proxy.$util.erpUtil.calcOrderItem(customerData.value.erpOrderItemList)

	// 调计算总金额函数实现回显
	calcMoney(customerData.value)

	modalVisible.value = true
	operatorParams.value = {
		// 采购订单的【编辑布局】
		pageId: 'FP2023042000002',
		params: {
			id: record.id
		}
	}
}

// 弹窗关闭
const handleModalClick = (isSubmit) => {
	modalVisible.value = false
	if (isSubmit) {
		fetchData() // 刷新表格数据
	}
}


// 当前行记录
const currentRecord = ref(null)

// 修改提交审批处理方法
const handleConfirmOk = async (record) => {
	try {
		// 先关闭 Popconfirm
		await nextTick()

		// 打开审批人选择弹窗
		currentRecord.value = record
		modalTitle.value = '审批人选择'
		modalType.value = 'approval'
		modalVisible.value = true
	} catch (error) {
		SkMessage.error('操作失败')
	}
}

// 处理数据保存
const customerDataSave = async (data) => {
	const pageId = operatorParams.value.pageId
	if (pageId == 'FP2023092200002') {
		// 转生产计划
		await proxy.$http.post(
			proxy.$config.getConfig().erpBasePath + 'insertProductionPlanToProduction',
			data
		)
		// 采购订单的【编辑布局】
	} else if (pageId == 'FP2023042000002') {
		// 转采购订单
		await proxy.$http.post(
			proxy.$config.getConfig().erpBasePath + 'insertProductionPlanToPurchaseOrder',
			data
		)
	}
	handleModalClick(true)
}

// 处理弹窗取消
const handleModalCancel = async () => {
	try {
		await nextTick()
		modalVisible.value = false
	} catch (error) {
		SkMessage.error('处理失败')
	}
}

// 处理审批人选择提交
const handleApprovalPersonSubmit = async (person) => {
	try {
		// 构建参数，与旧系统保持一致
		const params = {
			id: currentRecord.value.id,
			serviceClassName: currentRecord.value.serviceClassName,
			approvalId: person.id
		}

		// 发送提交请求
		await proxy.$http.post(
			proxy.$config.getConfig().erpBasePath + 'submitProductionPlanToApproval',
			params
		)

		// 提交成功
		SkMessage.success('提交成功')
		// 关闭弹窗
		modalVisible.value = false
		// 刷新数据
		await fetchData()

	} catch (error) {
		SkMessage.error('提交失败')
	}
}

const showIndexRef = ref(null)

// 处理数据会显示，刚获取到数据的逻辑
const handleAfterDataLoaded = (formData) => {
	// 这里可以处理特殊的参数
	const pageId = operatorParams.value.pageId
	const notEdit = proxy.$config.formEditType.notEdit
	// 若formData.productionPlanChildList不为空，进行遍历
	if (!proxy.$util.isNull(formData.productionPlanChildList)) {
		formData.productionPlanChildList.forEach(item => {

			// 设置bom方案的数据来源
			item[`bomId_config`] = {
				// 自定义类型
				dataType: 1,
				// bom方案的数据
				defaultData: item.bomList
			}

			// 下拉框的特殊配置
			item[`normsId_config`] = {
				// 自定义类型
				dataType: 1,
				// materialNorms为计量单位的数据
				defaultData: item.materialMation?.materialNorms || []
			}

			if (!proxy.$util.isNull(formData.fromId)) {
				item[`materialId_config`] = { isEdit: notEdit }
				item[`normsId_config`].isEdit = notEdit
			} else {
				if (pageId == 'FP2023092200002') {
					item[`materialId_config`] = { isEdit: notEdit }
					item[`normsId_config`].isEdit = notEdit
				} else if (pageId == 'FP2023042000002') {
					item[`materialId_config`] = { isEdit: notEdit }
					item[`normsId_config`].isEdit = notEdit
				}
			}
		})
	}
}

// 处理组件加载完成
const handleShowIndexLoaded = (formData) => {
	const pageId = operatorParams.value.pageId

	// 在组件加载完成后执行需要的操作，生产计划的【编辑布局】
	if (pageId == 'FP2023092200002') {
		showIndexRef.value?.writeComponentRef?.updateShowAdd("productionChildList", false)
		// 采购订单的【编辑布局】
	} else if (pageId == 'FP2023042000002') {
		showIndexRef.value?.writeComponentRef?.updateShowAdd("erpOrderItemList", false)
		// 有来源单据时，编辑布局禁用新增按钮
	} else if (pageId == 'FP2024071100002' && !proxy.$util.isNull(formData.fromId)) {
		showIndexRef.value?.writeComponentRef?.updateShowAdd("productionPlanChildList", false)
	}
}

// 处理单元格变化
const handleCellChange = async ({ record, dataIndex, value, column }, formData) => {

	// 判断开始时间是否晚于结束时间
	if (record.planStartTime > record.planEndTime) {
		SkMessage.error('开始时间不能晚于结束时间');
		record.planStartTime = '';  // 清空开始时间
		record.planEndTime = '';    // 清空结束时间
		return;
	}

	// 处理物料选择变化
	if (dataIndex == 'materialId') {
		record.unitPrice = 0
	} else if (dataIndex == 'normsId') {
		const materialNorms = column.getConfig(record).defaultData
		const norms = materialNorms.find(item => item.id === value)

		// 设置bom方案的数据来源
		const res = await proxy.$http.get(
			proxy.$config.getConfig().erpBasePath + 'queryBomListByNormsId',
			{
				normsId: norms.id
			}
		)
		showIndexRef.value?.writeComponentRef?.updateTableColumns(column.pIdDataIndex, record, {
			"bomId": {
				dataType: 1,
				defaultData: res.rows
			}
		})

		// 获取物料的预估采购价
		record.unitPrice = norms.estimatePurchasePrice
	}
	// 处理金额计算
	const result = proxy.$util.erpUtil.calcMoneyKey(dataIndex, record)
	// 将result合入到record中
	Object.assign(record, result)

	calcMoney(formData)
}

// 处理表单项变化
const handleChange = ({ attrKey, formData }) => {
	if (attrKey == 'discount') {
		calcMoney(formData)
	}
}

const calcMoney = (formData) => {
	// 处理金额计算
	const erpOrderItemList = formData?.erpOrderItemList || []
	let totalPrice = 0;
	if (!proxy.$util.isNull(erpOrderItemList)) {
		erpOrderItemList.forEach((item, i) => {
			totalPrice = proxy.$util.calculationUtil.sum(totalPrice, item.taxLastMoney)
		})
	}

	// 优惠信息
	const discountMoney = proxy.$util.erpUtil.calcDiscountMoney(formData, totalPrice)
	totalPrice = proxy.$util.calculationUtil.subtraction(totalPrice, discountMoney)
	formData.discountMoney = discountMoney
	formData.totalPrice = totalPrice
}

// 处理物料选择变化
const handleMaterialChange = async (material, record, column, formData) => {
	const dataIndex = column.dataIndex
	const mationKey = proxy.$util.getKeyIdToMation(dataIndex)

	if (material) {
		// 更新其他相关字段
		record[mationKey] = material
		record["normsId"] = undefined
		record["unitPrice"] = 0

		// 等待组件挂载完成
		await nextTick()

		// 修改当前行的表格列配置
		showIndexRef.value?.writeComponentRef?.updateTableColumns(column.pIdDataIndex, record, {
			"normsId": {
				dataType: 1,
				defaultData: material.materialNorms
			}
		})

		// 触发 cell-change 事件
		handleCellChange({
			record,
			dataIndex: column.dataIndex,
			value: material,
			column: column
		}, formData)
	} else {
		// 清空相关字段
		delete record[mationKey]
	}
}

// 初始化
onMounted(async () => {
	await nextTick()
	requestAnimationFrame(() => {
		tableReady.value = true
	})
	await getInitData()
	fetchData() // 获取表格数据
})
</script>

<style></style>