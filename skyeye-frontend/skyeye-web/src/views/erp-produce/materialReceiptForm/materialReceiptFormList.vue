<template>
	<div class="container-manage">
		<SkCard ref="cardRef" :bordered="false">
			<!-- 操作按钮 -->
			<div class="table-search">
				<SkForm layout="inline" v-model="searchForm" @submit="handleSearch" @reset="handleReset"
					:submitText="$t('common.search')" :resetText="$t('common.reset')" :isFormSubmit="false">
					<template #submitIcon>
						<search-outlined />
					</template>
					<a-form-item name="keyword">
						<SkInput v-model="searchForm.keyword" placeholder="请输入单号" allowClear
							@pressEnter="handleSearch" />
					</a-form-item>
				</SkForm>
			</div>
			<div class="table-operations">
				<SkSpace>
					<SkButton type="primary" @click.prevent="fetchData">
						<template #icon>
							<ReloadOutlined />
						</template>
						{{ $t('common.refresh') }}
					</SkButton>
				</SkSpace>
			</div>

			<!-- 表格 -->
			<SkTable :columns="columns" :data-source="tableData" :loading="loading" :pagination="pagination"
				:ready="tableReady" @change="handleTableChange">
				<template #bodyCell="{ column, record }">
					<template v-if="column.dataIndex === 'oddNumber'">
						<a @click="handleDetail(record)">
							{{ record.oddNumber }}
							<template v-if="!$util.isNull(record.fromId)">
								<SkTag :color="$util.getTagColor(column.dataIndex)">
									转
								</SkTag>
							</template>
						</a>
					</template>
					<template v-if="column.dataIndex === 'processInstanceId'">
						<!-- 流程id点击后的详情 -->
						<ProcessDetail :processInstanceId="record.processInstanceId" />
					</template>
					<!-- 获取来源类型 -->
					<template v-if="column.dataIndex === 'fromTypeId'">
						<div
							v-html="$util.skyeyeClassEnumUtil.getEnumDataNameByCodeAndKeyStatic(initEnumData['confirmFromType'], 'id', record.fromTypeId, 'name')">
						</div>
					</template>
					<!-- 获取状态 -->
					<template v-if="column.dataIndex === 'state'">
						<div
							v-html="$util.skyeyeClassEnumUtil.getEnumDataNameByCodeAndKeyStatic(initEnumData['flowableStateEnum'], 'id', record.state, 'name')">
						</div>
					</template>
					<!-- 操作列 -->
					<template v-if="column.key === 'action'">
						<SkSpace>
							<template v-if="record.editRow == 1">
								<!-- 提交审批 -->
								<a v-if="$config.auth('1721114400445')" @click="handleConfirmOk(record)">
									{{ $t("common.submitApproval") }}
								</a>
								<SkDivider v-if="$config.auth('1721114400445')" type="vertical" />

								<!-- 编辑 -->
								<a v-if="$config.auth('1721028158455')" @click="handleEdit(record, 'edit')">
									{{ $t('common.edit') }}
								</a>
								<SkDivider v-if="$config.auth('1721028158455')" type="vertical" />

								<!-- 删除 -->
								<SkPopconfirm v-if="$config.auth('1721114323209')" :title="$t('common.deleteConfirm')"
									@confirm="handleDelete(record)" :okText="$t('common.delete')"
									:cancelText="$t('common.cancel')">
									<a class="danger-link">{{ $t('common.delete') }}</a>
								</SkPopconfirm>
							</template>

							<template v-if="record.editRow == 2">
								<!-- 撤销 -->
								<SkPopconfirm v-if="$config.auth('1721114309851')" :title="$t('common.revokeConfirm')"
									@confirm="handleRevoke(record)" :okText="$t('common.confirm')"
									:cancelText="$t('common.cancel')">
									<a class="danger-link">{{ $t('common.revoke') }}</a>
								</SkPopconfirm>
							</template>
						</SkSpace>
					</template>
				</template>
			</SkTable>

			<!-- 弹窗 -->
			<SkModal v-model="modalVisible" :title="modalTitle" @cancel="handleModalCancel">
				<ShowIndex ref="showIndexRef" v-if="modalType === 'edit' || modalType === 'details'"
					:pageId="operatorParams.pageId" :params="operatorParams.params" @close="handleModalClick"
					@loaded="handleShowIndexLoaded" @afterDataLoaded="handleAfterDataLoaded">
					<!-- 传递自定义单元格组件 -->
					<template #cell-chooseInput-materialId="slotProps">
						<a-form-item-rest>
							<SkMaterialSelect v-model="slotProps.record[slotProps.column.dataIndex]"
								:formData="slotProps.record"
								:isEdit="slotProps.column.getConfig(slotProps.record).isEdit"
								:attrKey="slotProps.column.dataIndex" />
						</a-form-item-rest>
					</template>
				</ShowIndex>
				<!-- 审批人弹窗 -->
				<ApprovalPersonSelect v-if="modalType === 'approval'" :actKey="currentRecord?.serviceClassName"
					:businessData="null" @submit="handleApprovalPersonSubmit" @cancel="handleModalCancel" />
			</SkModal>
		</SkCard>
	</div>
</template>

<script setup>
import { reactive, ref, getCurrentInstance, onMounted, nextTick} from 'vue'
import { useI18n } from 'vue-i18n'
import SkTag from '@/components/SkTag/index.vue'
import SkInput from '@/components/SkInput/index.vue'
import SkCard from '@/components/SkCard/index.vue'
import SkForm from '@/components/SkForm/index.vue'
import SkTable from '@/components/SkTable/index.vue'
import SkSpace from '@/components/SkSpace/index.vue'
import SkButton from '@/components/SkButton/index.vue'
import { SkMessage } from '@/components/SkMessage/index.vue'
import SkDivider from '@/components/SkDivider/index.vue'
import SkPopconfirm from '@/components/SkPopconfirm/index.vue'
import SkModal from '@/components/SkModal/index.vue'
import ApprovalPersonSelect from '@/views/system/submitApproval/approvalPersonSelect.vue'
import ShowIndex from '@/views/dsForm/show/index.vue'
import ProcessDetail from '@/views/dsForm/process/detail.vue'
import SkMaterialSelect from '@/components/SkMaterialSelect/index.vue'

const { proxy } = getCurrentInstance()
const { t } = useI18n()

// 初始化枚举数据
const initEnumData = ref({})
const getInitData = async () => {
	let result = await proxy.$util.getEnumListMapByCode(['flowableStateEnum', 'confirmFromType']);
	initEnumData.value = result
}

// 搜索表单数据
const searchForm = reactive({
	keyword: ''
})

// 表格列配置
const columns = ref([
	{
		title: t('common.serialNum'),
		dataIndex: 'index',
		width: 80,
		align: 'center',
		fixed: 'left',
		customRender: ({ index }) => {
			return (pagination.current - 1) * pagination.pageSize + index + 1
		}
	},
	{
		title: '单号',
		dataIndex: 'oddNumber',
		width: 220,
		align: 'center',
	},
	{
		title: '单据日期',
		dataIndex: 'operTime',
		width: 140,
	},
	{
		title: '来源单据信息',
		width: 300,
		align: 'center',
		children: [{
			title: '来源类型',
			dataIndex: 'sourceType',
			dataIndex: 'fromTypeId',
			width: 120,
			align: 'center'
		},
		{
			title: '单据编号',
			dataIndex: 'sourceNumber',
			width: 180,
			align: 'center',
			customRender: ({ record }) => record.fromMation?.oddNumber
		}]
	},
	{
		title: '部门',
		dataIndex: 'departmentMation',
		width: 120,
		align: 'center',
		customRender: ({ record }) => record.departmentMation?.name
	},
	{
		title: '车间',
		dataIndex: 'farmMation',
		width: 120,
		align: 'center',
		customRender: ({ record }) => record.farmMation?.name
	},
	{
		title: '业务员',
		dataIndex: 'salesmanMation',
		width: 120,
		align: 'center',
		customRender: ({ record }) => record.salesmanMation?.name
	},
	{
		title: '总金额',
		dataIndex: 'totalPrice',
		width: 120,
		align: 'center'
	},
	{
		title: '流程ID',
		dataIndex: 'processInstanceId',
		width: 120,
		align: 'center'
	},
	{
		title: '状态',
		dataIndex: 'state',
		width: 100,
		align: 'center'
	},
	{
		title: '创建人',
		dataIndex: 'createName',
		width: 120,
		align: 'center',
	},
	{
		title: '创建时间',
		dataIndex: 'createTime',
		width: 160,
		align: 'center',
	},
	{
		title: '最后修改人',
		dataIndex: 'lastUpdateName',
		width: 120,
		align: 'center'
	},
	{
		title: '最后修改时间',
		dataIndex: 'lastUpdateTime',
		width: 160,
		align: 'center'
	},
	{
		title: t('common.action'),
		key: 'action',
		width: 220,
		align: 'center',
		fixed: 'right'
	}
])

const tableData = ref([]) // 表格数据
const loading = ref(false) // 加载状态
const tableReady = ref(false)

// 分页配置
const pagination = reactive(proxy.$config.pagination())

// 获取数据
const fetchData = async () => {
	loading.value = true
	try {
		// 构建查询参数，添加 keyword
		const params = {
			page: Number(pagination.current || 1),
			limit: Number(pagination.pageSize || 10),
			keyword: searchForm.keyword?.trim() || '', // 添加关键字搜索参数
		}
		// 发送查询请求
		const res = await proxy.$http.post(
			proxy.$config.getConfig().erpBasePath + 'queryConfirmPutList',
			params
		)

		tableData.value = res.rows || []
		pagination.total = res.total || 0
	} catch (error) {
		SkMessage.error('获取数据失败')
		tableData.value = []
		pagination.total = 0
	} finally {
		loading.value = false
	}
}

// 事件处理 搜索
const handleSearch = () => {
	pagination.current = 1 // 重置到第一页
	fetchData()
}

// 添加重置处理函数
const handleReset = () => {
	searchForm.keyword = '' // 清空搜索关键字
	pagination.current = 1 // 重置到第一页
	fetchData()
}

// 表格变化处理
const handleTableChange = (pag) => {
	if (pag) {
		pagination.current = Number(pag.current || 1)
		pagination.pageSize = Number(pag.pageSize || 10)
	}
	fetchData()
}

const modalVisible = ref(false)
const modalTitle = ref('审批人选择')
const modalType = ref('approval')
const operatorParams = ref({})

// 编辑
const handleEdit = (record) => {
	modalTitle.value = '编辑'
	modalType.value = 'edit'
	modalVisible.value = true
	operatorParams.value = {
		pageId: 'FP2024071500004',
		params: {
			id: record.id
		}
	}
}

// 删除
const handleDelete = async (record) => {
	try {
		const params = {
			id: record.id,
			serviceClassName: record.serviceClassName
		}
		await proxy.$http.delete(
			proxy.$config.getConfig().erpBasePath + 'erpcommon005',
			params
		)
		SkMessage.success('删除成功')
		fetchData()
	} catch (error) {
		SkMessage.error('删除失败')
	}
}

// 撤销处理
const handleRevoke = async (record) => {
	try {
		const params = {
			processInstanceId: record.processInstanceId,
			serviceClassName: record.serviceClassName
		}

		await proxy.$http.put(
			proxy.$config.getConfig().erpBasePath + 'erpcommon003',
			params
		)

		SkMessage.success('撤销成功')
		await fetchData()
	} catch (error) {
		SkMessage.error('撤销失败')
	}
}

// 详情
const handleDetail = (record) => {
	modalTitle.value = '详情'
	modalType.value = 'details'
	modalVisible.value = true
	operatorParams.value = {
		pageId: 'FP2024071600001',
		params: {
			id: record.id
		}
	}
}

// 弹窗关闭
const handleModalClick = (isSubmit) => {
	modalVisible.value = false
	if (isSubmit) {
		fetchData() // 刷新表格数据
	}
}

// 当前行记录
const currentRecord = ref(null)

// 修改提交审批处理方法
const handleConfirmOk = async (record) => {
	try {
		await nextTick()
		modalTitle.value = '审批人选择'
		modalType.value = 'approval'
		currentRecord.value = record
		modalVisible.value = true
	} catch (error) {
		SkMessage.error('操作失败')
	}
}

// 处理弹窗取消
const handleModalCancel = () => {
	modalVisible.value = false
}

// 处理审批人选择提交
const handleApprovalPersonSubmit = async (person) => {
	try {
		// 构建参数
		const params = {
			id: currentRecord.value.id,
			serviceClassName: currentRecord.value.serviceClassName,
			approvalId: person.id
		}

		// 发送提交请求 person.id
		await proxy.$http.put(
			proxy.$config.getConfig().erpBasePath + 'erpcommon006',
			params
		)

		// 提交成功
		SkMessage.success('提交成功')
		// 关闭弹窗
		modalVisible.value = false
		// 刷新数据
		await fetchData()

	} catch (error) {
		SkMessage.error('提交失败')
	}
}

const showIndexRef = ref(null)

// 处理数据会显示，刚获取到数据的逻辑
const handleAfterDataLoaded = (formData) => {
	// 这里可以处理特殊的参数
	const notEdit = proxy.$config.formEditType.notEdit

	if (!proxy.$util.isNull(formData.erpOrderItemList)) {
		formData.erpOrderItemList.forEach(item => {
			// 下拉框的特殊配置
			item[`normsId_config`] = {
				// 自定义类型
				dataType: 1,
				// materialNorms为计量单位的数据
				defaultData: item.materialMation?.materialNorms || []
			}
			if (!proxy.$util.isNull(formData.fromId)) {
				item[`materialId_config`] = { isEdit: notEdit }
				item[`normsId_config`].isEdit = notEdit
				item[`depotId_config`] = { isEdit: notEdit }
				item[`unitPrice_config`] = { isEdit: notEdit }
				item[`allPrice_config`] = { isEdit: notEdit }
				item[`taxRate_config`] = { isEdit: notEdit }
				item[`taxMoney_config`] = { isEdit: notEdit }
				item[`taxUnitPrice_config`] = { isEdit: notEdit }
				item[`taxLastMoney_config`] = { isEdit: notEdit }
			}
		})
	}
}

// 处理组件加载完成
const handleShowIndexLoaded = () => {
	const pageId = operatorParams.value.pageId
	if (pageId == 'FP2024071500004') {
		showIndexRef.value?.writeComponentRef?.updateShowAdd("erpOrderItemList", false)
	}
}

// 初始化
onMounted(async () => {
	await nextTick()
	requestAnimationFrame(() => {
		tableReady.value = true
	})
	await getInitData()
	fetchData() // 获取表格数据
})
</script>

<style></style>