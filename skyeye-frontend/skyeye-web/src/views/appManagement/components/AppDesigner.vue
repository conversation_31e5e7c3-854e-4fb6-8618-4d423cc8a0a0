<template>
    <div class="app-designer">
        <div class="designer-container">
            <!-- 顶部工具栏 -->
            <div class="designer-header">
                <div class="header-left">
                    <div class="app-info">
                        <img :src="appInfo?.icon" class="app-icon" alt="app icon" />
                        <div class="app-meta">
                            <span class="app-name">{{ appInfo?.appName }}</span>
                            <span class="app-version">v{{ appInfo?.version }}</span>
                        </div>
                    </div>
                </div>
                <div class="header-right">
                    <SkSpace>
                        <SkButton @click.prevent="handlePreview">
                            <template #icon>
                                <EyeOutlined />
                            </template>
                            预览
                        </SkButton>
                        <SkButton @click.prevent="handleCancel">
                            取消
                        </SkButton>
                        <SkButton type="primary" @click.prevent="handleSave">
                            <template #icon>
                                <SaveOutlined />
                            </template>
                            保存
                        </SkButton>
                    </SkSpace>
                </div>
            </div>

            <!-- 设计器主体 -->
            <div class="designer-body">
                <!-- 页面列表面板 -->
                <div class="pages-panel">
                    <sk-card title="页面列表" :bordered="false">
                        <div class="page-list">
                            <div v-for="(page, index) in pages" :key="page.id" class="page-item"
                                :class="{ active: currentPage === index }" @click="handlePageSelect(index)">
                                <div class="page-info">
                                    <div class="page-name">{{ page.name }}</div>
                                    <div class="page-path">{{ page.path }}</div>
                                </div>
                                <div class="page-actions">
                                    <sk-space>
                                        <sk-button size="small" @click.stop.prevent="handleEditPage(index)">
                                            <edit-outlined />
                                        </sk-button>
                                        <sk-button size="small" @click.stop.prevent="handleDeletePage(index)">
                                            <delete-outlined />
                                        </sk-button>
                                    </sk-space>
                                </div>
                            </div>
                            <sk-button type="dashed" block @click="handleAddPage">
                                <plus-outlined />添加页面
                            </sk-button>
                        </div>
                    </sk-card>
                </div>

                <!-- 左侧组件面板 -->
                <div class="components-panel">
                    <div class="component-list">
                        <template v-for="category in ['basic', 'layout', 'navigation']" :key="category">
                            <div class="category-title">{{ getCategoryName(category) }}</div>
                            <div class="category-content">
                                <div v-for="item in getCategoryComponents(category)" :key="item.type"
                                    class="component-item" draggable="true"
                                    @dragstart="handleDragStart($event, item.type)">
                                    <div class="component-icon">
                                        <component :is="item.icon" />
                                    </div>
                                    <div class="component-name">{{ item.name }}</div>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>

                <!-- 中间画布区域 -->
                <div class="canvas-area">
                    <div class="phone-simulator">
                        <div class="phone-header"></div>
                        <div class="phone-content" @dragover.prevent="handleDragOver" @drop="handleDrop"
                            @dragleave.prevent>
                            <div class="content-wrapper">
                                <template v-if="pageComponents.length">
                                    <div v-for="(comp, index) in pageComponents" :key="index" class="design-component"
                                        :class="{
                                            'active': currentComponent === index,
                                            'dragging': draggingIndex === index,
                                            'drag-over': dragOverIndex === index
                                        }" draggable="true" @dragstart="handleDragStart($event, index)"
                                        @dragend="handleDragEnd" @dragover.prevent="handleItemDragOver($event, index)"
                                        @dragleave.prevent="handleDragLeave" @click="selectComponent(index)">
                                        <component :is="getComponent(comp.type)" v-bind="comp.props"
                                            :is-design="true" />
                                        <div class="component-actions" v-if="currentComponent === index">
                                            <sk-space @click.stop>
                                                <sk-button size="small"
                                                    @click.stop="handleMoveComponent($event, index, -1)">
                                                    <up-outlined />
                                                </sk-button>
                                                <sk-button size="small"
                                                    @click.stop="handleMoveComponent($event, index, 1)">
                                                    <down-outlined />
                                                </sk-button>
                                                <sk-button size="small"
                                                    @click.stop="handleRemoveComponent($event, index)">
                                                    <delete-outlined />
                                                </sk-button>
                                            </sk-space>
                                        </div>
                                    </div>
                                </template>
                                <div v-else class="empty-tip">
                                    从左侧拖拽组件到此处
                                </div>
                            </div>
                        </div>
                        <div class="phone-footer"></div>
                    </div>
                </div>

                <!-- 右侧属性面板 -->
                <div class="props-panel" v-if="currentComponent !== null && currentComponentConfig">
                    <SkCard title="组件属性" :bordered="false">
                        <div class="props-content">
                            <SkForm v-model="formData" :showButtons="false">
                                <template v-for="(prop, key) in currentComponentConfig.props" :key="key">
                                    <a-form-item class="prop-item">
                                        <template #label v-if="key !== 'dataSource'">
                                            <span>{{ prop.label }}</span>
                                            <a-tooltip v-if="prop.help">
                                                <template #title>
                                                    <div class="help-content">
                                                        <div class="help-title">{{ prop.help.title }}</div>
                                                        <div class="help-text"
                                                            v-html="prop.help.content.replace(/\n/g, '<br/>')">
                                                        </div>
                                                    </div>
                                                </template>
                                                <component :is="prop.help.icon" class="help-icon" />
                                            </a-tooltip>
                                        </template>

                                        <!-- 数据源配置 -->
                                        <template v-if="key === 'dataSource'">
                                            <sk-data-source-config
                                                v-model="pageComponents[currentComponent].props.dataSource"
                                                :config="prop.config" />
                                        </template>

                                        <!-- 其他属性配置保持不变 -->
                                        <template v-else>
                                            <!-- 数组类型 -->
                                            <template v-if="prop.type === 'array'">
                                                <sk-button @click="addArrayItem(key, prop)">添加项</sk-button>
                                                <div v-for="(item, index) in pageComponents[currentComponent].props[key]"
                                                    :key="index" class="array-item">
                                                    <div v-for="(itemProp, itemKey) in prop.config.item.props"
                                                        :key="itemKey">
                                                        <a-form-item :label="itemProp.label">
                                                            <sk-input
                                                                v-if="itemProp.type === 'string' || itemProp.type === 'image'"
                                                                v-model="item[itemKey]" />
                                                        </a-form-item>
                                                    </div>
                                                    <sk-button size="small"
                                                        @click="removeArrayItem(key, index)">删除</sk-button>
                                                </div>
                                            </template>
                                            <!-- 其他类型 -->
                                            <template v-else>
                                                <sk-input v-if="prop.type === 'string'"
                                                    v-model="pageComponents[currentComponent].props[key]" />
                                                <sk-switch v-else-if="prop.type === 'boolean'"
                                                    v-model="pageComponents[currentComponent].props[key]" />
                                                <sk-select v-else-if="prop.type === 'select'"
                                                    v-model="pageComponents[currentComponent].props[key]"
                                                    :options="prop.options" />
                                                <sk-input-number v-else-if="prop.type === 'number'"
                                                    v-model="pageComponents[currentComponent].props[key]"
                                                    :min="prop.min" :max="prop.max" />
                                                <sk-color-picker v-else-if="prop.type === 'color'"
                                                    v-model="pageComponents[currentComponent].props[key]" />
                                            </template>
                                        </template>
                                    </a-form-item>
                                </template>
                            </SkForm>
                        </div>
                    </SkCard>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, onBeforeUnmount, watch } from 'vue'
import {
    EyeOutlined,
    SaveOutlined,
    UpOutlined,
    DownOutlined,
    DeleteOutlined,
    EditOutlined,
    PlusOutlined
} from '@ant-design/icons-vue'
import { SkMessage } from '@/components/SkMessage/index.vue'
import {
    designComponents,
    designConfigs,
    componentCategories
} from '@/uni-components/util/designTool'

// 导入设计器需要的组件
import SkButton from '@/components/SkButton/index.vue'
import SkSpace from '@/components/SkSpace/index.vue'
import SkCard from '@/components/SkCard/index.vue'
import SkForm from '@/components/SkForm/index.vue'
import SkInput from '@/components/SkInput/index.vue'
import SkSwitch from '@/components/SkSwitch/index.vue'
import SkSelect from '@/components/SkSelect/index.vue'
import SkInputNumber from '@/components/SkInputNumber/index.vue'
import SkColorPicker from '@/components/SkColorPicker/index.vue'
import SkDataSourceConfig from '@/components/SkDataSourceConfig/index.vue'

// 组件配置映射
const componentConfigs = designConfigs

// 注册组件
const components = designComponents

const props = defineProps({
    appInfo: {
        type: Object,
        required: true
    }
})

const emit = defineEmits(['save', 'cancel'])

// 页面数据
const pages = ref([
    {
        id: '1',
        name: '首页',
        path: '/pages/index/index',
        components: []
    },
    {
        id: '2',
        name: '分类页',
        path: '/pages/category/index',
        components: []
    }
])

// 当前选中的页面
const currentPage = ref(0)

// 页面组件数据，改为从当前页面获取
const pageComponents = computed({
    get: () => pages.value[currentPage.value]?.components || [],
    set: (val) => {
        if (currentPage.value !== null) {
            pages.value[currentPage.value].components = val
        }
    }
})

// 选择页面
const handlePageSelect = (index) => {
    currentPage.value = index
    currentComponent.value = null
}

// 添加页面
const handleAddPage = () => {
    // 显示添加页面的弹窗
    // TODO: 实现添加页面的逻辑
}

// 编辑页面
const handleEditPage = (index) => {
    // 显示编辑页面的弹窗
    // TODO: 实现编辑页面的逻辑
}

// 删除页面
const handleDeletePage = (index) => {
    // 显示删除确认弹窗
    // TODO: 实现删除页面的逻辑
}

// 页面组件
const currentComponent = ref(null)
const formData = ref({})

// 在组件卸载前清理数据
onBeforeUnmount(() => {
    pageComponents.value = []
    currentComponent.value = null
})

// 修组件渲染逻辑
const getComponent = (type) => {
    if (!type) return null
    return components[type.toLowerCase()] || null
}

// 获取当前组件的配置
const currentComponentConfig = computed(() => {
    if (currentComponent.value === null) return null
    const comp = pageComponents.value[currentComponent.value]
    if (!comp || !comp.type) return null
    return componentConfigs[comp.type] || null
})

// 添加拖拽相关的状态
const draggingIndex = ref(null)
const dragOverIndex = ref(null)

// 修改拖拽开始处理
const handleDragStart = (event, index) => {
    if (typeof index === 'string') {
        event.dataTransfer.setData('componentType', index)
    } else {
        draggingIndex.value = index
        event.dataTransfer.setData('moveComponentIndex', index.toString())
        currentComponent.value = index

        // 设置拖拽时的半透明效果
        event.target.style.opacity = '0.4'
    }
}

// 添加拖拽结束处理
const handleDragEnd = (event) => {
    draggingIndex.value = null
    dragOverIndex.value = null
    event.target.style.opacity = ''
}

// 添加拖拽经过处理
const handleDragOver = (event) => {
    event.preventDefault()
    if (draggingIndex.value !== null) {
        const targetIndex = getDropIndex(event)
        if (targetIndex !== -1) {
            dragOverIndex.value = targetIndex
        }
    }
}

// 添加拖拽离开处理
const handleDragLeave = () => {
    dragOverIndex.value = null
}

// 添加组件拖拽经过处理
const handleItemDragOver = (event, index) => {
    event.preventDefault()
    if (draggingIndex.value !== null && draggingIndex.value !== index) {
        dragOverIndex.value = index
    }
}

// 修改放置处理
const handleDrop = (event) => {
    event.preventDefault()
    const componentType = event.dataTransfer.getData('componentType')
    const moveComponentIndex = event.dataTransfer.getData('moveComponentIndex')

    // 重置拖拽状态
    draggingIndex.value = null
    dragOverIndex.value = null

    // 如果是组件拖拽排序
    if (moveComponentIndex !== '') {
        const sourceIndex = parseInt(moveComponentIndex)
        const targetIndex = getDropIndex(event)
        if (targetIndex !== -1 && sourceIndex !== targetIndex) {
            const component = pageComponents.value[sourceIndex]
            pageComponents.value.splice(sourceIndex, 1)
            pageComponents.value.splice(targetIndex, 0, component)
            currentComponent.value = targetIndex
        }
        return
    }

    // 如果是从组件面板拖入新组件
    if (componentType) {
        const config = componentConfigs[componentType]
        if (!config) return

        // 创建默认属性
        const defaultProps = {}
        Object.entries(config.props).forEach(([key, prop]) => {
            if (prop.type === 'array') {
                defaultProps[key] = []
            } else {
                defaultProps[key] = prop.defaultValue ?? null
            }
        })

        // 添加组件
        const newComponent = {
            type: componentType,
            props: defaultProps
        }

        // 获取放置位置
        const dropIndex = getDropIndex(event)
        if (dropIndex !== -1) {
            pageComponents.value.splice(dropIndex, 0, newComponent)
            currentComponent.value = dropIndex
        } else {
            pageComponents.value.push(newComponent)
            currentComponent.value = pageComponents.value.length - 1
        }

        // 初始化表单数据
        formData.value = { ...defaultProps }
    }
}

// 添加获取放置位置的辅助方法
const getDropIndex = (event) => {
    const phoneContent = event.currentTarget
    const components = phoneContent.getElementsByClassName('design-component')
    const mouseY = event.clientY

    for (let i = 0; i < components.length; i++) {
        const rect = components[i].getBoundingClientRect()
        const centerY = rect.top + rect.height / 2

        if (mouseY < centerY) {
            return i
        }
    }

    return components.length
}

// 选择组件
const selectComponent = (index) => {
    currentComponent.value = index
}

// 移动组件
const handleMoveComponent = (event, index, direction) => {
    event.preventDefault()
    moveComponent(index, direction)
}

// 删除组件
const handleRemoveComponent = (event, index) => {
    event.preventDefault()
    removeComponent(index)
}

// 移动组件的具体实现
const moveComponent = (index, direction) => {
    const newIndex = index + direction
    if (newIndex < 0 || newIndex >= pageComponents.value.length) return

    // 使用数组解构交换位置
    [pageComponents.value[index], pageComponents.value[newIndex]] =
        [pageComponents.value[newIndex], pageComponents.value[index]]

    // 更新当前选中的组件索引
    currentComponent.value = newIndex
}

// 删除组件
const removeComponent = (index) => {
    pageComponents.value.splice(index, 1)
    currentComponent.value = null
}

// 数组属性操作
const addArrayItem = (key, prop) => {
    if (!pageComponents.value[currentComponent.value].props[key]) {
        pageComponents.value[currentComponent.value].props[key] = []
    }
    const defaultItem = {}
    Object.entries(prop.config.item.props).forEach(([itemKey, itemProp]) => {
        defaultItem[itemKey] = ''
    })
    pageComponents.value[currentComponent.value].props[key].push(defaultItem)
}

const removeArrayItem = (key, index) => {
    pageComponents.value[currentComponent.value].props[key].splice(index, 1)
}

// 取消
const handleCancel = () => {
    emit('cancel')
}

// 预览
const handlePreview = () => {
    // 实现预览逻辑
}

// 保存
const handleSave = async () => {
    try {
        await saveAppConfig(props.appInfo.id, pages.value)
        SkMessage.success('保存成功')
        emit('save')
    } catch (error) {
        SkMessage.error('保存失败')
    }
}

// 保存应用配置
const saveAppConfig = async (appId, pages) => {
    // TODO: 实现保存应用配置的逻辑
}

// 监听表单数据变化
watch(formData, (newVal) => {
    if (currentComponent.value !== null &&
        pageComponents.value[currentComponent.value] &&
        pageComponents.value[currentComponent.value].props) {
        // 更新组件属性
        Object.assign(pageComponents.value[currentComponent.value].props, newVal)
    }
})

// 当选中组件变化时，更新表单数据
watch(currentComponent, (newVal) => {
    if (newVal !== null &&
        pageComponents.value[newVal] &&
        pageComponents.value[newVal].props) {
        formData.value = { ...pageComponents.value[newVal].props }
    } else {
        formData.value = {}
    }
})

// 获取分类名称
const getCategoryName = (category) => {
    switch (category) {
        case 'basic': return '基础组件'
        case 'navigation': return '导航组件'
        case 'layout': return '布局组件'
        case 'form': return '表单组件'
        case 'display': return '展示组件'
        default: return ''
    }
}

// 获取分类下的组件
const getCategoryComponents = (category) => {
    return componentCategories[0].components.filter(item => item.category === category)
}

// 添加容器的拖放处理
const handleContainerDrop = (event, containerIndex) => {
    const componentType = event.dataTransfer.getData('componentType')
    const moveComponentIndex = event.dataTransfer.getData('moveComponentIndex')

    // 获取目标容器组件
    const containerComponent = pageComponents.value[containerIndex]
    if (containerComponent.type !== 'sk-container') return

    // 确保容器组件有 children 数组
    if (!containerComponent.children) {
        containerComponent.children = []
    }

    // 如果是组件拖拽排序
    if (moveComponentIndex !== '') {
        const sourceIndex = parseInt(moveComponentIndex)
        if (sourceIndex !== containerIndex) {
            const component = { ...pageComponents.value[sourceIndex] }
            pageComponents.value.splice(sourceIndex, 1)
            containerComponent.children.push(component)
            currentComponent.value = containerIndex
        }
    }
    // 如果是从组件面板拖入新组件
    else if (componentType) {
        const config = componentConfigs[componentType]
        if (!config) return

        const newComponent = {
            type: componentType,
            props: { ...getDefaultProps(config.props) }
        }
        containerComponent.children.push(newComponent)
        currentComponent.value = containerIndex
    }
}
</script>

<style lang="scss" scoped>
.app-designer {
    height: calc(100vh - 200px);
    background: #f0f2f5;

    .designer-container {
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .designer-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 24px;
        background: #fff;
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
        border-radius: 4px;
        margin-bottom: 16px;

        .header-left {
            .app-info {
                display: flex;
                align-items: center;
                gap: 12px;

                .app-icon {
                    width: 32px;
                    height: 32px;
                    border-radius: 8px;
                }

                .app-meta {
                    display: flex;
                    flex-direction: column;

                    .app-name {
                        font-size: 16px;
                        font-weight: 500;
                        color: rgba(0, 0, 0, 0.85);
                        line-height: 1.2;
                    }

                    .app-version {
                        font-size: 12px;
                        color: rgba(0, 0, 0, 0.45);
                    }
                }
            }
        }

        .header-right {
            :deep(.sk-button) {
                height: 32px;
                padding: 0 16px;
            }
        }
    }

    .designer-body {
        flex: 1;
        display: flex;
        gap: 16px;
        margin-top: 16px;
        height: 0;
        padding: 0 16px;

        .pages-panel {
            width: 240px;
            background: #fff;
            border-radius: 4px;
            overflow: hidden;

            .page-list {
                display: flex;
                flex-direction: column;
                gap: 8px;
                padding: 8px;
            }

            .page-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 8px;
                border: 1px solid #f0f0f0;
                border-radius: 4px;
                cursor: pointer;
                transition: all 0.3s;

                &:hover {
                    background: #f5f5f5;
                }

                &.active {
                    border-color: #1890ff;
                    background: rgba(24, 144, 255, 0.1);
                }

                .page-info {
                    .page-name {
                        font-weight: 500;
                        color: rgba(0, 0, 0, 0.85);
                    }

                    .page-path {
                        font-size: 12px;
                        color: rgba(0, 0, 0, 0.45);
                    }
                }

                .page-actions {
                    opacity: 0;
                    transition: opacity 0.3s;
                }

                &:hover .page-actions {
                    opacity: 1;
                }
            }
        }

        .components-panel {
            width: 280px;
            background: #fff;
            border-radius: 4px;
            overflow: hidden;

            .component-list {
                padding: 16px;
            }

            .component-item {
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: 4px;
                padding: 8px;
                cursor: move;
                border: 1px solid #f0f0f0;
                border-radius: 4px;

                &:hover {
                    background: #f5f5f5;
                }

                .component-icon {
                    font-size: 24px;
                }

                .component-name {
                    font-size: 12px;
                }
            }

            .category-title {
                padding: 12px 16px;
                font-size: 14px;
                font-weight: 500;
                color: #333;
                background: #f5f7fa;
            }

            .category-content {
                padding: 12px;
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
                gap: 12px;
            }
        }

        .canvas-area {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            background: #f5f5f5;
            overflow: hidden;

            .phone-simulator {
                width: 300px;
                height: 590px;
                background: #fff;
                border-radius: 32px;
                box-shadow: 0 0 24px rgba(0, 0, 0, 0.1);
                display: flex;
                flex-direction: column;
                overflow: hidden;
                position: relative;

                .phone-header {
                    height: 24px;
                    background: #000;
                }

                .phone-content {
                    flex: 1;
                    background: #fff;
                    position: relative;
                    overflow: hidden;
                    height: 100%;

                    .content-wrapper {
                        position: absolute;
                        top: 0;
                        left: 0;
                        right: 0;
                        bottom: 50px;
                        overflow-y: auto;
                        overflow-x: hidden;
                        height: 100%;
                    }
                }

                .design-component {
                    position: relative;
                    margin-bottom: 8px;
                    border: 1px dashed transparent;
                    transition: all 0.3s;
                    cursor: move;

                    &:hover {
                        border-color: #1890ff;
                    }

                    &.active {
                        border-color: #1890ff;

                        &::before {
                            content: '';
                            position: absolute;
                            top: 0;
                            left: 0;
                            right: 0;
                            bottom: 0;
                            background: rgba(24, 144, 255, 0.1);
                            pointer-events: none;
                        }
                    }

                    .component-actions {
                        position: absolute;
                        right: 8px;
                        top: 8px;
                        background: rgba(255, 255, 255, 0.9);
                        padding: 4px;
                        border-radius: 4px;
                        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                        z-index: 1;
                    }
                }

                .empty-tip {
                    flex: 1;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    color: #999;
                    background: #fafafa;
                    border: 2px dashed #eee;
                    border-radius: 4px;
                    margin: 0;
                    min-height: 100%;
                }

                .phone-footer {
                    height: 16px;
                    background: #000;
                }
            }
        }

        .props-panel {
            width: 320px;
            background: #fff;
            border-radius: 4px;
            overflow: hidden;
            display: flex;
            flex-direction: column;

            :deep(.ant-card) {
                height: 100%;
                display: flex;
                flex-direction: column;

                .ant-card-head {
                    min-height: 48px;
                    padding: 0 16px;
                    border-bottom: 1px solid #f0f0f0;

                    .ant-card-head-title {
                        padding: 12px 0;
                        font-size: 14px;
                        font-weight: 500;
                    }
                }

                .ant-card-body {
                    flex: 1;
                    padding: 16px;
                    overflow: auto;
                }
            }

            .props-content {
                .prop-item {
                    margin-bottom: 16px;

                    :deep(.ant-form-item-label) {
                        padding-bottom: 4px;

                        label {
                            font-size: 13px;
                            color: rgba(0, 0, 0, 0.65);
                        }
                    }

                    &:last-child {
                        margin-bottom: 0;
                    }
                }
            }
        }
    }
}

// 隐藏滚动条（可选）
::-webkit-scrollbar {
    display: none;
}

.design-component {
    &.dragging {
        opacity: 0.4;
        border: 1px dashed #1890ff;
    }

    &.drag-over {
        position: relative;

        &::before {
            content: '';
            position: absolute;
            top: -2px;
            left: 0;
            right: 0;
            height: 4px;
            background: #1890ff;
            border-radius: 2px;
        }
    }

    // 拖拽时的过渡效果
    transition: transform 0.2s ease,
    opacity 0.2s ease;
}

:deep(.help-content) {
    max-width: 400px;

    .help-title {
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 8px;
    }

    .help-text {
        font-size: 12px;
        white-space: pre-wrap;
        line-height: 1.5;
    }
}
</style>