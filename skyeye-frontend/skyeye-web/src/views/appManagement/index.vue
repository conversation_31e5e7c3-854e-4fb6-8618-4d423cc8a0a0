<template>
    <div class="container-manage">
        <SkCard ref="cardRef" :bordered="false">
            <!-- 搜索表单 -->
            <div class="table-search">
                <SkForm layout="inline" v-model="searchForm" @submit="handleSearch" @reset="handleReset" submitText="查询"
                    resetText="重置">
                    <a-form-item label="应用名称" name="appName">
                        <SkInput v-model="searchForm.appName" placeholder="请输入应用名称" allowClear />
                    </a-form-item>
                    <a-form-item label="状态">
                        <SkSelect v-model="searchForm.status" placeholder="请选择状态" style="width: 120px" allowClear>
                        </SkSelect>
                    </a-form-item>
                </SkForm>
            </div>

            <!-- 操作按钮 -->
            <div class="table-operations">
                <SkSpace>
                    <SkButton type="primary" @click.prevent="handleAdd">
                        <template #icon><plus-outlined /></template>
                        新增应用
                    </SkButton>
                </SkSpace>
            </div>

            <!-- 表格 -->
            <SkTable :columns="columns" :data-source="tableData" :loading="loading" :pagination="pagination"
                :ready="tableReady" @change="handleTableChange">
                <template #bodyCell="{ column, record }">
                    <!-- 应用图标 -->
                    <template v-if="column.dataIndex === 'icon'">
                        <div class="app-icon">
                            <SkAvatar :src="record.icon" shape="square" :size="40" />
                        </div>
                    </template>
                    <!-- 状态 -->
                    <template v-if="column.dataIndex === 'status'">
                        <SkTag :color="getStatusColor(record.status)">
                            {{ getStatusText(record.status) }}
                        </SkTag>
                    </template>
                    <!-- 操作列 -->
                    <template v-if="column.dataIndex === 'action'">
                        <SkSpace>
                            <a @click="handleEdit(record)">编辑</a>
                            <SkDivider type="vertical" />
                            <SkPopconfirm title="确定要删除这个应用吗？" @confirm="handleDelete(record)" okText="删除"
                                cancelText="取消">
                                <a class="danger-link">删除</a>
                            </SkPopconfirm>
                            <SkDivider type="vertical" />
                            <a @click="handleDesign(record)">设计</a>
                        </SkSpace>
                    </template>
                </template>
            </SkTable>

            <!-- 设计器弹窗 -->
            <SkModal v-model="designerVisible" :title="`${currentApp?.appName || ''} - 页面设计`" width="90%">
                <AppDesigner v-if="designerVisible" :app-info="currentApp" @save="handleDesignerSave"
                    @cancel="designerVisible = false" />
            </SkModal>
        </SkCard>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick, getCurrentInstance } from 'vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import { SkMessage } from '@/components/SkMessage/index.vue'
import SkButton from '@/components/SkButton/index.vue'
import SkInput from '@/components/SkInput/index.vue'
import SkForm from '@/components/SkForm/index.vue'
import SkTable from '@/components/SkTable/index.vue'
import SkCard from '@/components/SkCard/index.vue'
import SkModal from '@/components/SkModal/index.vue'
import SkSelect from '@/components/SkSelect/index.vue'
import SkSpace from '@/components/SkSpace/index.vue'
import SkAvatar from '@/components/SkAvatar/index.vue'
import SkTag from '@/components/SkTag/index.vue'
import SkDivider from '@/components/SkDivider/index.vue'
import SkPopconfirm from '@/components/SkPopconfirm/index.vue'
import AppDesigner from './components/AppDesigner.vue'

const { proxy } = getCurrentInstance()

// 表格列定义
const columns = [
    {
        title: '应用图标',
        dataIndex: 'icon',
        width: 80,
        align: 'center'
    },
    {
        title: '应用名称',
        dataIndex: 'appName',
        width: 200,
        align: 'center'
    },
    {
        title: '应用描述',
        dataIndex: 'description',
        ellipsis: true,
        width: 300
    },
    {
        title: '版本号',
        dataIndex: 'version',
        width: 100,
        align: 'center'
    },
    {
        title: '状态',
        dataIndex: 'status',
        width: 100,
        align: 'center'
    },
    {
        title: '更新时间',
        dataIndex: 'updateTime',
        width: 180,
        align: 'center',
        sorter: true
    },
    {
        title: '操作',
        dataIndex: 'action',
        width: 200,
        align: 'center',
        fixed: 'right'
    }
]

// 搜索表单数据
const searchForm = reactive({
    appName: '',
    status: undefined
})

// 表格数据
const loading = ref(false)
const tableData = ref([])
const pagination = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
    showTotal: (total) => `共 ${total} 条`
})

// 获取状态颜色
const getStatusColor = (status) => {
    const statusMap = {
        '1': 'success',
        '0': 'warning',
        '-1': 'error'
    }
    return statusMap[status]
}

// 获取状态文本
const getStatusText = (status) => {
    const statusMap = {
        '1': '已上线',
        '0': '未上线',
        '-1': '已下架'
    }
    return statusMap[status]
}

// 查询数据
const fetchData = async () => {
    loading.value = true
    try {
        // 模拟API调用延迟
        await new Promise(resolve => setTimeout(resolve, 500))

        // 模拟数据
        const mockData = [
            {
                id: 1,
                icon: 'https://gw.alipayobjects.com/zos/rmsportal/KDpgvguMpGfqaHPjicRK.svg',
                appName: '智慧社区',
                description: '面向社区居民的综合服务应用，包含物业服务、社区活动、便民服务等功能',
                version: '1.0.0',
                status: '1',
                updateTime: '2024-03-15 14:30:00'
            },
            {
                id: 2,
                icon: 'https://gw.alipayobjects.com/zos/antfincdn/aPkFc8Sj7n/method-draw-image.svg',
                appName: '移动办公',
                description: '企业移动办公解决方案，支持审批、打卡、会议等日常办公场景',
                version: '2.1.5',
                status: '1',
                updateTime: '2024-03-14 09:15:00'
            },
            {
                id: 3,
                icon: 'https://gw.alipayobjects.com/zos/rmsportal/ComBAopevLwENQdKWiIn.png',
                appName: '智慧校园',
                description: '为学校提供智能化管理平台，包含课程管理、考勤系统、家校互动等功能',
                version: '0.9.0',
                status: '0',
                updateTime: '2024-03-13 16:45:00'
            },
            {
                id: 4,
                icon: 'https://gw.alipayobjects.com/zos/rmsportal/nxkuOJlFJuAUhzlMTCEe.png',
                appName: '健康管理',
                description: '个人健康数据管理平台，支持运动记录、饮食建议、健康报告等功能',
                version: '1.2.3',
                status: '-1',
                updateTime: '2024-03-12 11:20:00'
            },
            {
                id: 5,
                icon: 'https://gw.alipayobjects.com/zos/rmsportal/ubnKSIfAJTxIgXOKlciN.png',
                appName: '智慧商超',
                description: '零售商超移动解决方案，支持商品管理、库存管理、会员管理等功能',
                version: '2.0.1',
                status: '1',
                updateTime: '2024-03-11 15:40:00'
            }
        ]

        // 模拟分页和搜索
        let filteredData = [...mockData]

        // 应用名称搜索
        if (searchForm.appName) {
            filteredData = filteredData.filter(item =>
                item.appName.toLowerCase().includes(searchForm.appName.toLowerCase())
            )
        }

        // 状态筛选
        if (searchForm.status !== undefined) {
            filteredData = filteredData.filter(item =>
                item.status === searchForm.status
            )
        }

        // 计算分页
        const total = filteredData.length
        const start = (pagination.current - 1) * pagination.pageSize
        const end = start + pagination.pageSize
        const pageData = filteredData.slice(start, end)

        // 更新数据
        tableData.value = pageData
        pagination.total = total

    } catch (error) {
        SkMessage.error('获取数据失败')
    } finally {
        loading.value = false
    }
}

// 搜索
const handleSearch = () => {
    pagination.current = 1
    fetchData()
}

// 重置
const handleReset = () => {
    searchForm.appName = ''
    searchForm.status = undefined
    handleSearch()
}

// 表格变化处理
const handleTableChange = (pag) => {
    pagination.current = pag.current
    pagination.pageSize = pag.pageSize
    fetchData()
}

// 新增应用
const handleAdd = () => {
    // 实现新增逻辑
}

// 编辑应用
const handleEdit = (record) => {
    // 实现编辑逻辑
}

// 删除应用
const handleDelete = async (record) => {
    try {
        // 这里替换为实际的删除 API 调用
        await fetch(`/api/apps/${record.id}`, {
            method: 'DELETE'
        })
        SkMessage.success('删除成功')
        fetchData()
    } catch (error) {
        SkMessage.error('删除失败')
    }
}

// 设计器相关状态
const designerVisible = ref(false)
const currentApp = ref(null)

// 修改设计应用的处理函数
const handleDesign = (record) => {
    currentApp.value = record
    designerVisible.value = true
}

// 处理设计器保存
const handleDesignerSave = () => {
    designerVisible.value = false
    // 可以在这里刷新列表
    fetchData()
}

const cardRef = ref(null)
const tableReady = ref(false)
// 初始化
onMounted(async () => {
    await nextTick()
    requestAnimationFrame(() => {
        tableReady.value = true
    })
    fetchData()
})
</script>

<style scoped>
.danger-link {
    color: #ff4d4f;
}

.app-icon {
    display: flex;
    justify-content: center;
    align-items: center;
}
</style>
