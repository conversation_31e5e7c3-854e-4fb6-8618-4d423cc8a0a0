// 个人中心
export default {
    path: '/admin-assistant',
    name: 'Admin-assistant',
    meta: { title: '个人中心', icon: 'AdminOutlined' },
    children: [
        {
            path: 'conFerenceRoomReserve',
            name: 'ConFerenceRoomReserveManage',
            component: () => import('@/views/admin-assistant/conFerenceRoomReserve/index.vue'),
            meta: { title: '会议室预定' }
        },
        {
            path: 'conference',
            name: 'ConferenceExample',
            component: () => import('@/views/admin-assistant/conference/index.vue'),
            meta: { title: '会议' }
        },
        {
            path: 'assetReportManage',
            name: 'AssetReportManage',
            component: () => import('@/views/admin-assistant/assetReportManage/index.vue'),
            meta: { title: '资产明细' }
        },
        {
            path: 'vehicleapplication',
            name: 'VehicleApplicationManage',
            component: () => import('@/views/admin-assistant/vehicleapplication/index.vue'),
            meta: { title: '用车申请' }
        },
        {
            path: 'assetArticlesUse/assetArticlesUseList',
            name: 'AssetArticlesUseManage',
            component: () => import('@/views/admin-assistant/assetArticlesUse/assetArticlesUseList.vue'),
            meta: { title: '用品领用' }
        },
        {
            path: 'assetArticlesUse/myAssetArticlesUse',
            name: 'MyAssetArticlesUseManage',
            component: () => import('@/views/admin-assistant/assetArticlesUse/myAssetArticlesUse.vue'),
            meta: { title: '我的用品领用历史' }
        },
        {
            path: 'seal/sealManageBorrow',
            name: 'SealManageBorrowManage',
            component: () => import('@/views/admin-assistant/seal/sealManageBorrow.vue'),
            meta: { title: '印章借用' }
        },
        {
            path: 'seal/sealManageRevert',
            name: 'SealManageRevertManage',
            component: () => import('@/views/admin-assistant/seal/sealManageRevert.vue'),
            meta: { title: '印章归还' }
        },
        {
            path: 'seal/mySealManagement',
            name: 'MySealManagementManage',
            component: () => import('@/views/admin-assistant/seal/mySealManagement.vue'),
            meta: { title: '我借用中的印章' }
        },
        {
            path: 'licence/licenceManageBorrow',
            name: 'LicenceManageBorrowManage',
            component: () => import('@/views/admin-assistant/licence/licenceManageBorrow.vue'),
            meta: { title: '证照借用' }
        },
        {
            path: 'licence/licenceManageRevert',
            name: 'LicenceManageRevertManage',
            component: () => import('@/views/admin-assistant/licence/licenceManageRevert.vue'),
            meta: { title: '证照归还' }
        },
        {
            path: 'licence/myLicenceManage',
            name: 'MyLicenceManageManage',
            component: () => import('@/views/admin-assistant/licence/myLicenceManage.vue'),
            meta: { title: '我借用中的证照' }
        },
        {
            path: 'assetProcurementReturn',
            name: 'assetProcurementReturn',
            component: () => import('@/views/admin-assistant/assetProcurementReturn/index.vue'),
            meta: { title: '资产采购退货' }
        },
        {
            path: 'personalAdministration/regularization',
            name: 'RegularizationManage',
            component: () => import('@/views/admin-assistant/personalAdministration/regularization.vue'),
            meta: { title: '转正申请' }
        },
        {
            path: 'personalAdministration/resignation',
            name: 'ResignationManage',
            component: () => import('@/views/admin-assistant/personalAdministration/resignation.vue'),
            meta: { title: '离职申请' }
        },
        {
            path: 'myEmail/emailSendModelList',
            name: 'myEmailManage',
            component: () => import('@/views/admin-assistant/myEmail/emailSendModelList.vue'),
            meta: { title: '邮件发送模板' }
        },
        {
            path: 'myAttendanceRecord/checkWorkLeave',
            name: 'checkWorkLeaveManage',
            component: () => import('@/views/admin-assistant/myAttendanceRecord/checkWorkLeave.vue'),
            meta: { title: '请假申请' }
        },
        {
            path: 'asset/assetRequisition',
            name: 'AssetRequisitionManage',
            component: () => import('@/views/admin-assistant/asset/assetRequisition.vue'),
            meta: { title: '资产领用' }
        },
        {
            path: 'asset/assetReturn',
            name: 'AssetReturnManage',
            component: () => import('@/views/admin-assistant/asset/assetReturn.vue'),
            meta: { title: '资产归还' }
        },
        {
            path: 'asset/myAssetManagement',
            name: 'MyAssetManagementManage',
            component: () => import('@/views/admin-assistant/asset/myAssetManagement.vue'),
            meta: { title: '我名下的资产' }
        },
        {
            path: 'myAttendanceRecord/businessTravel',
            name: 'businessTravelManage',
            component: () => import('@/views/admin-assistant/myAttendanceRecord/businessTravel.vue'),
            meta: { title: '出差申请' }
        },
        {
            path: 'myAttendanceRecord/workOverTime',
            name: 'workOverTimeManage',
            component: () => import('@/views/admin-assistant/myAttendanceRecord/workOverTime.vue'),
            meta: { title: '加班申请' }
        },
        {
            path: 'myAttendanceRecord/saleOfLeave',
            name: 'saleOfLeaveManage',
            component: () => import('@/views/admin-assistant/myAttendanceRecord/saleOfLeave.vue'),
            meta: { title: '销假申请' }
        },
        {
            path: 'assetInventory',
            name: 'assetInventory',
            component: () => import('@/views/admin-assistant/assetInventory/index.vue'),
            meta: { title: '资产采购入库' }
        },
        {
            path: 'myAttendanceRecord/abnormalAttendance',
            name: 'AbnormalAttendanceManage',
            component: () => import('@/views/admin-assistant/myAttendanceRecord/abnormalAttendance.vue'),
            meta: { title: '加班申请' }
        },
    ]
}