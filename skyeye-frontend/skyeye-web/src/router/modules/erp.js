// ERP
export default {
	path: '/erp',
	name: 'ERP',
	meta: { title: 'ERP', icon: 'AppstoreOutlined' },
	children: [
		{
			path: '/salesOutlet/salesOutletList',
			name: 'salesOutletList',
			component: () => import('@/views/erp/salesOutlet/salesOutletList.vue'),
			meta: { title: '销售出库' }
		},
		{
			path: 'turnIocatorManage',
			name: 'TurnIocatorManage',
			component: () => import('@/views/erp/turnIocatorManage/turnIocatorManage.vue'),
			meta: { title: '库位管理' }
		},
		{
			path: 'otherWarehous',
			name: 'Warehousing',
			component: () => import('@/views/erp/otherWarehous/index.vue'),
			meta: { title: '其他入库' }
		},
		{
			path: 'depotOut',
			name: 'DepotOut',
			component: () => import('@/views/erp/depotOut/index.vue'),
			meta: { title: '出库管理' }
		},
		{
			path: 'purchaseOrder',
			name: 'PurchaseOrder',
			component: () => import('@/views/erp/purchaseOrder/index.vue'),
			meta: { title: '采购订单' }
		},
		{
			path: 'otherOutletsList',
			name: 'OtherOutletsList',
			component: () => import('@/views/erp/otherOutletsList/index.vue'),
			meta: { title: '其他出库' }
		},
		{
			path: 'splitDocList',
			name: 'SplitDocList',
			component: () => import('@/views/erp/splitDocList/index.vue'),
			meta: { title: '拆分单' }
		},
		{
			path: 'purchaseRequest',
			name: 'PurchaseRequest',
			component: () => import('@/views/erp/purchaseRequest/index.vue'),
			meta: { title: '采购申请' }
		},
		{
			path: 'supplierService',
			name: 'SupplierService',
			component: () => import('@/views/erp/supplier/service.vue'),
			meta: { title: '供应商服务' }
		},
		{
			path: 'assemblySheetList',
			name: 'AssemblySheetList',
			component: () => import('@/views/erp/assemblySheetList/index.vue'),
			meta: { title: '组装单' }
		},
		{
			path: 'allocationList',
			name: 'AllocationList',
			component: () => import('@/views/erp/allocationList/index.vue'),
			meta: { title: '调拨' }
		},
		{
			path: 'inventoryTaskList',
			name: 'InventoryTaskList',
			component: () => import('@/views/erp/inventoryTaskList/index.vue'),
			meta: { title: '盘点任务单' }
		},
		{
			path: 'depotOutList',
			name: 'depotOutList',
			component: () => import('@/views/erp/depotOutList/index.vue'),
			meta: { title: '仓库出库单' }
		},
		{
			path: 'depotPut',
			name: 'DepotPut',
			component: () => import('@/views/erp/depotPut/index.vue'),
			meta: { title: '入库管理' }
		},
		{
			path: 'depotPutList',
			name: 'DepotPutList',
			component: () => import('@/views/erp/depotPutList/index.vue'),
			meta: { title: '仓库入库单' }
		},
		{
			path: 'warehousePersonnel',
			name: 'WarehousePersonnel',
			component: () => import('@/views/erp/warehousePersonnel/index.vue'),
			meta: { title: '仓库人员' }
		},
		{
			path: 'warehousingDetails',
			name: 'WarehousingDetails',
			component: () => import('@/views/erp/statistics/warehousingDetails.vue'),
			meta: { title: '入库明细' }
		},
		{
			path: 'outgoingDetails',
			name: 'OutgoingDetails',
			component: () => import('@/views/erp/statistics/outgoingDetails.vue'),
			meta: { title: '出库明细' }
		},
		{
			path: 'incomingStatistics',
			name: 'IncomingStatistics',
			component: () => import('@/views/erp/statistics/incomingStatistics.vue'),
			meta: { title: '进货统计' }
		},
		{
			path: 'salesStatistics',
			name: 'SalesStatistics',
			component: () => import('@/views/erp/statistics/salesStatistics.vue'),
			meta: { title: '销售统计' }
		},
		{
			path: 'customerReconciliation',
			name: 'CustomerReconciliation',
			component: () => import('@/views/erp/statistics/customerReconciliation.vue'),
			meta: { title: '客户对账' }
		},
		{
			path: 'supplierReconciliation',
			name: 'SupplierReconciliation',
			component: () => import('@/views/erp/statistics/supplierReconciliation.vue'),
			meta: { title: '供应商对账' }
		},
		{
			path: 'materialReserve',
			name: 'MaterialReserve',
			component: () => import('@/views/erp/materialReserve/index.vue'),
			meta: { title: '库存管理' }
		},
		{
			path: 'materialInventoryWarning',
			name: 'MaterialInventoryWarning',
			component: () => import('@/views/erp/materialInventoryWarning/index.vue'),
			meta: { title: '库存预警' }
		},
		{
			path: 'materialCodeReportList',
			name: 'MaterialCodeReportList',
			component: () => import('@/views/erp/materialCodeReportList/index.vue'),
			meta: { title: '条形码管理' }
		},
		{
			path: 'myTaskInventoryList',
			name: 'MyTaskInventoryList',
			component: () => import('@/views/erp/myTaskInventoryList/index.vue'),
			meta: { title: '我的任务盘点' }
		},
		{
			path: 'retailOutletList',
			name: 'RetailOutletList',
			component: () => import('@/views/erp/retailOutletList/index.vue'),
			meta: { title: '零售出库' }
		},
		{
			path: 'retailReturnsList',
			name: 'RetailReturnsList',
			component: () => import('@/views/erp/retailReturnsList/index.vue'),
			meta: { title: '零售退货' }
		},
		{
			path: 'purchasePutList',
			name: 'PurchasePutList',
			component: () => import('@/views/erp/purchasePutList/index.vue'),
			meta: { title: '采购入库' }
		},
		{
			path: 'purchaseReturnsList',
			name: 'PurchaseReturnsList',
			component: () => import('@/views/erp/purchaseReturnsList/index.vue'),
			meta: { title: '采购退货' }
		},
		{
			path: 'purchaseDeliveryNoteList',
			name: 'PurchaseDeliveryNoteList',
			component: () => import('@/views/erp/purchaseDeliveryNoteList/index.vue'),
			meta: { title: '采购到货' }
		},
		{
			path: 'salesOrder/salesOrderList',
			name: 'SalesOrderList',
			component: () => import('@/views/erp/salesOrder/salesOrderList.vue'),
			meta: { title: '销售订单' }
		},
		{
			path: 'salesReturns/salesReturnsList',
			name: 'SalesReturnsList',
			component: () => import('@/views/erp/salesReturns/salesReturnsList.vue'),
			meta: { title: '销售退货' }
		},
		{
			path: 'purchaseQualityInspectionList',
			name: 'PurchaseQualityInspectionList',
			component: () => import('@/views/erp/purchaseQualityInspectionList/index.vue'),
			meta: { title: '采购质检' }
		},
		{
			path: 'erpPage',
			name: 'ErpPage',
			component: () => import('@/views/erp/erpPage/index.vue'),
			meta: { title: 'ERP门户首页' }
		}
	]
}