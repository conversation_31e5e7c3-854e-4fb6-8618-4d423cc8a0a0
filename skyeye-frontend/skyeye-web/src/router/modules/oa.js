// 行政
export default {
    path: '/oa',
    name: 'Oa',
    meta: { title: '行政', icon: 'OaOutlined' },
    children: [
        {
            path: 'buySupplies',
            name: 'BuySupplieslManage',
            component: () => import('@/views/oa/buySupplies/index.vue'),
            meta: { title: '用品采购' }
        }, {
            path: 'buyAsset',
            name: 'BuyAssetManage',
            component: () => import('@/views/oa/buyAsset/index.vue'),
            meta: { title: '资产采购' }
        }, {
            path: 'sendDocument',
            name: 'SendDocumentManage',
            component: () => import('@/views/oa/gwDocument/sendDocument.vue'),
            meta: { title: '公文发文' }
        }, {
            path: 'receiveDocument',
            name: 'ReceiveDocumentManage',
            component: () => import('@/views/oa/gwDocument/receiveDocument.vue'),
            meta: { title: '公文收文' }
        }, {
            path: 'seeDocument',
            name: 'SeeDocumentManage',
            component: () => import('@/views/oa/gwDocument/seeDocument.vue'),
            meta: { title: '公文查阅' }
        }, {
            path: 'leavePool',
            name: 'LeavePoolManage',
            component: () => import('@/views/oa/leavePool/index.vue'),
            meta: { title: '假期池' }
        }, {
            path: 'attendance',
            name: 'AttendanceManage',
            component: () => import('@/views/oa/attendance/index.vue'),
            meta: { title: '考勤制度' }
        }, {
            path: 'schedule',
            name: 'ScheduleManage',
            component: () => import('@/views/oa/schedule/index.vue'),
            meta: { title: '我的日程' }
        }, {
            path: 'scheduleTodo',
            name: 'ScheduleTodoManage',
            component: () => import('@/views/oa/schedule/todo.vue'),
            meta: { title: '日程待办' }
        }, {
            path: 'holiday',
            name: 'Holiday',
            component: () => import('@/views/oa/holiday/index.vue'),
            meta: { title: '公司节假日' }
        }, {
            path: 'checkWork',
            name: 'CheckWork',
            component: () => import('@/views/oa/checkWork/report.vue'),
            meta: { title: '考勤报表' }
        }, {
            path: 'capitalPool',
            name: 'CapitalPool',
            component: () => import('@/views/oa/capitalPool/index.vue'),
            meta: { title: '待结算资金池' }
        }, {
            path: 'lightApp',
            name: 'LightApp',
            component: () => import('@/views/oa/lightApp/index.vue'),
            meta: { title: '轻应用' }
        }, {
            path: 'checkWorkTime/loginUserCheckWorkTime',
            name: 'CheckWorkTime',
            component: () => import('@/views/oa/checkWorkTime/loginUserCheckWorkTime.vue'),
            meta: { title: '考勤班次' }
        }, {
            path: 'checkWorkMyMonthReport',
            name: 'CheckWorkMyMonthReport',
            component: () => import('@/views/oa/checkWorkMyMonthReport/index.vue'),
            meta: { title: '考勤打卡' }
        }, {
            path: 'forumShow/forumList',
            name: 'forumShow',
            component: () => import('@/views/oa/forumShow/forumList.vue'),
            meta: { title: '论坛' },
        }, {
            path: 'forumReportCheck/reportCheckList',
            name: 'ForumReportCheck',
            component: () => import('@/views/oa/forumreportcheck/reportchecklist.vue'),
            meta: { title: '举报审核' },
        }
    ]
}