// 财务管理
export default {
    path: '/ifs',
    name: 'Ifs',
    meta: { title: '财务管理', icon: 'AppstoreOutlined' },
    children: [
        {
            path: 'reimbursement',
            name: 'reimbursement',
            component: () => import('@/views/ifs/reimbursement/index.vue'),
            meta: { title: '报销订单' }
        },
        {
            path: 'loan/loanBorrowList',
            name: 'loanBrrowList',
            component: () => import('@/views/ifs/loan/loanBorrowList.vue'),
            meta: { title: '借款单' }
        },
        {
            path: 'loan/loanRepayList',
            name: 'loanRepayList',
            component: () => import('@/views/ifs/loan/loanRepayList.vue'),
            meta: { title: '还款单' }
        },
        {
            path: 'ifsVoucher/ifsVoucherListChoose',
            name: 'ifsVoucherListChoose',
            component: () => import('@/views/ifs/ifsVoucher/ifsVoucherListChoose.vue'),
            meta: { title: '凭证管理' }
        },{
            path: 'income/incomeList',
            name: 'IncomeList',
            component: () => import('@/views/ifs/income/incomeList.vue'),
            meta: { title: '明细账' }
        }
    ]
}
