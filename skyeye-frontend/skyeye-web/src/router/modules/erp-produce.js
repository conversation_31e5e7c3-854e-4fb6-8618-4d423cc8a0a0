// 生产管理
export default {
    path: '/erp-produce',
    name: 'Erp-produce',
    meta: { title: '生产管理', icon: 'AppstoreOutlined' },
    children: [
        {
            path: 'erpPreProductionPlan',
            name: 'ErpPreProductionPlan',
            component: () => import('@/views/erp-produce/erpPreProductionPlan/index.vue'),
            meta: { title: '出货计划' }
        },
        {
            path: 'erpProduction/erpProductionList',
            name: 'ErpProductionList',
            component: () => import('@/views/erp-produce/erpProduction/erpProductionList.vue'),
            meta: { title: '生产计划' }
        },
        {
            path: 'erpPick/erpPatchPickList',
            name: 'ErpPatchPickList',
            component: () => import('@/views/erp-produce/erpPick/erpPatchPickList.vue'),
            meta: { title: '补料单' }
        },
        {
            path: 'erpPick/supplementOutList',
            name: 'SupplementOutList',
            component: () => import('@/views/erp-produce/erpPick/supplementOutList.vue'),
            meta: { title: '补料出库单' }
        },
        {
            path: 'erpPick/erpReturnPickList',
            name: 'ErpReturnPickList',
            component: () => import('@/views/erp-produce/erpPick/erpReturnPickList.vue'),
            meta: { title: '退料单' }
        },
        {
            path: 'erpPick/returnMaterialPut',
            name: 'ReturnMaterialPut',
            component: () => import('@/views/erp-produce/erpPick/returnMaterialPut.vue'),
            meta: { title: '退料入库单' }
        },
        {
            path: 'erpDepartStock/erpDepartStockList',
            name: 'ErpDepartStockList',
            component: () => import('@/views/erp-produce/erpDepartStock/erpDepartStockList.vue'),
            meta: { title: '部门物料库存' }
        },
        {
            path: 'workshopMaterialInventory',
            name: 'WorkshopMaterialInventory',
            component: () => import('@/views/erp-produce/workshopMaterialInventory/index.vue'),
            meta: { title: '车间物料库存' }
        },
        {
            path: 'erpPick/erpRequisitionPickList',
            name: 'ErpRequisitionPickList',
            component: () => import('@/views/erp-produce/erpPick/erpRequisitionPickList.vue'),
            meta: { title: '领料单' }
        },
        {
            path: 'erpPick/materialOutList',
            name: 'MaterialOutList',
            component: () => import('@/views/erp-produce/erpPick/materialOutList.vue'),
            meta: { title: '领料出库单' }
        },
        {
            path: 'materialsAwaitingConfirmation/materialsAwaitingConfirmationList',
            name: 'MaterialsAwaitingConfirmationList',
            component: () => import('@/views/erp-produce/materialsAwaitingConfirmation/materialsAwaitingConfirmationList.vue'),
            meta: { title: '待确认物料' }
        },
        {
            path: 'materialReceiptForm/materialReceiptFormList',
            name: 'MaterialReceiptFormList',
            component: () => import('@/views/erp-produce/materialReceiptForm/materialReceiptFormList.vue'),
            meta: { title: '物料接收单' }
        },
        {
            path: 'materialReturnOrder/materialReturnOrderList',
            name: 'MaterialReturnOrderList',
            component: () => import('@/views/erp-produce/materialReturnOrder/materialReturnOrderList.vue'),
            meta: { title: '物料退货单' }
        },
        {
            path: 'processAcceptance/processAcceptanceList',
            name: 'ProcessAcceptanceList',
            component: () => import('@/views/erp-produce/processAcceptance/processAcceptanceList.vue'),
            meta: { title: '工序验收单' }
        },
        {
            path: 'wholeOut/wholeOutList',
            name: 'WholeOutList',
            component: () => import('@/views/erp-produce/wholeOut/wholeOutList.vue'),
            meta: { title: '整单委外单' }
        },
        {
            path: 'workshopPersonnel',
            name: 'WorkshopPersonnelAllocation',
            component: () => import('@/views/erp-produce/workshopPersonnel/workshopPersonnelAllocation.vue'),
            meta: { title: '车间人员' }
        },
        {
            path: 'machiningWarehouse',
            name: 'MachiningWarehouseList',
            component: () => import('@/views/erp-produce/machiningWarehouse/machiningWarehouseList.vue'),
            meta: { title: '加工入库单' }
        },
        {
            path: 'workShop',
            name: 'WorkShopList',
            component: () => import('@/views/erp-produce/workShop/workShopList.vue'),
            meta: { title: '车间任务' }
        }
    ]
}