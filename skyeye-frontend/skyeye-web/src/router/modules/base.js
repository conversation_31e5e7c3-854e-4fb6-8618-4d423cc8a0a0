// 基础模块 
export default {
    path: '/system',
    name: 'System',
    meta: { title: '基础模块', icon: 'AppstoreOutlined' },
    children: [
        {
            path: 'user',
            name: 'UserManage',
            component: () => import('@/views/system/user/index.vue'),
            meta: { title: '用户管理' }
        }, {
            path: 'menu',
            name: 'MenuManage',
            component: () => import('@/views/system/menu/sysEveMenuList.vue'),
            meta: { title: '菜单管理' }
        }, {
            path: 'business',
            name: 'BusinessManage',
            component: () => import('@/views/system/business/index.vue'),
            meta: { title: '业务对象管理' }
        }, {
            path: 'area',
            name: 'AreaManage',
            component: () => import('@/views/system/area/index.vue'),
            meta: { title: '行政区划管理' }
        }, {
            path: 'icon',
            name: 'IconManage',
            component: () => import('@/views/system/icon/index.vue'),
            meta: { title: '资源图标' }
        }, {
            path: 'sysEveWinBgPic',
            name: 'SysEveWinBgPicManage',
            component: () => import('@/views/system/sysEveWinBgPic/index.vue'),
            meta: { title: '背景图片设置' }
        }, {
            path: 'sysEveWinLockBgPic',
            name: 'SysEveWinLockBgPicManage',
            component: () => import('@/views/system/sysEveWinLockBgPic/index.vue'),
            meta: { title: '锁屏背景图片设置' }
        }, {
            path: 'payChannel',
            name: 'PayChannelManage',
            component: () => import('@/views/system/payChannel/index.vue'),
            meta: { title: '支付渠道管理' }
        }, {
            path: 'fileConfig',
            name: 'FileConfigManage',
            component: () => import('@/views/system/fileConfig/index.vue'),
            meta: { title: '文件配置' }
        }, {
            path: 'modelType',
            name: 'ModelTypeManage',
            component: () => import('@/views/system/modelType/index.vue'),
            meta: { title: '素材分类' }
        }, {
            path: 'model',
            name: 'ModelManage',
            component: () => import('@/views/system/model/index.vue'),
            meta: { title: '素材管理' }
        }, {
            path: 'modelStore',
            name: 'ModelStoreManage',
            component: () => import('@/views/system/model/store.vue'),
            meta: { title: '编辑器素材库' }
        }, {
            path: 'dictData',
            name: 'DictData',
            component: () => import('@/views/system/dictData/index.vue'),
            meta: { title: '数据字典' }
        }, {
            path: 'enclosure',
            name: 'EnclosureManage',
            component: () => import('@/views/system/enclosure/index.vue'),
            meta: { title: '我的附件' }
        }, {
            path: 'companyMation',
            name: 'CompanyMation',
            component: () => import('@/views/system/companyMation/index.vue'),
            meta: { title: '公司管理' }
        }, {
            path: 'department',
            name: 'Department',
            component: () => import('@/views/system/department/index.vue'),
            meta: { title: '部门管理' }
        }, {
            path: 'job',
            name: 'Job',
            component: () => import('@/views/system/job/index.vue'),
            meta: { title: '职位管理' }
        }, {
            path: 'explain',
            name: 'Explain',
            component: () => import('@/views/system/explain/write.vue'),
            meta: { title: '说明文档' }
        }, {
            path: 'component',
            name: 'Component',
            component: () => import('@/views/system/component/index.vue'),
            meta: { title: '组件管理' }
        }, {
            path: 'template',
            name: 'Template',
            component: () => import('@/views/system/template/index.vue'),
            meta: { title: '模板管理' }
        }, {
            path: 'settings',
            name: 'Settings',
            component: () => import('@/views/system/settings/index.vue'),
            meta: { title: '系统设置' }
        }, {
            path: 'appMenu',
            name: 'AppMenu',
            component: () => import('@/views/system/appMenu/index.vue'),
            meta: { title: '应用菜单管理' }
        }, {
            path: 'tenantOrderManagement',
            name: 'TenantOrderManagement',
            component: () => import('@/views/system/tenantOrderManagement/index.vue'),
            meta: { title: '租户订单' }
        }, {
            path: 'tenantApplication/tenantApplicationList',
            name: 'TenantApplicationList',
            component: () => import('@/views/system/tenantApplication/tenantApplicationList.vue'),
            meta: { title: '租户应用列表' }
        }, {
            path: 'importExport',
            name: 'ImportExport',
            component: () => import('@/views/system/importExport/example.vue'),
            meta: { title: '导入导出' }
        }, {
            path: 'databaseBackup',
            name: 'DatabaseBackup',
            component: () => import('@/views/system/databaseBackup/index.vue'),
            meta: { title: '数据库备份' }
        }


    ]
}