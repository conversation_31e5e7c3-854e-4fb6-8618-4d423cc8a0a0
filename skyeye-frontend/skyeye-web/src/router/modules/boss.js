// 招聘
export default {
    path: '/boss',
    name: 'Boss',
    meta: { title: '招聘', icon: 'AppstoreOutlined' },
    children: [
        {
            path: 'bossPersonRequire/bossPersonRequireAllList',
            name: 'Boss<PERSON>ersonRequireAllList',
            component: () => import('@/views/boss/bossPersonRequire/bossPersonRequireAllList.vue'),
            meta: { title: 'HR人员需求申请单' }
        },
        {
            path: 'bossPersonRequire/bossPersonRequireMyChargeList',
            name: 'BossPersonRequireMyChargeList',
            component: () => import('@/views/boss/bossPersonRequire/bossPersonRequireMyChargeList.vue'),
            meta: { title: '我负责的人员需求' }
        },
        {
            path: 'bossInterviewArrangement/bossInterviewArrangementList',
            name: 'BossInterviewArrangementList',
            component: () => import('@/views/boss/bossInterviewArrangement/bossInterviewArrangementList.vue'),
            meta: { title: '面试安排' }
        },
        {
            path: 'bossInterviewJobTransfer',
            name: 'BossInterviewJobTransferList',
            component: () => import('@/views/boss/bossInterviewJobTransfer/bossInterviewJobTransferList.vue'),
            meta: { title: '岗位调动' }
        },
        {
            path: 'bossPersonRequire/bossPersonRequireList',
            name: 'BossPersonRequireList',
            component: () => import('@/views/boss/bossPersonRequire/bossPersonRequireList.vue'),
            meta: { title: '我发起的人员需求' }
        },
        {
            path: 'bossInterviewArrangement/bossInterviewerList',
            name: 'BossInterviewerList',
            component: () => import('@/views/boss/bossInterviewArrangement/bossInterviewerList.vue'),
            meta: { title: '面试管安排' }
        },
        {
            path: 'bossInterviewer',
            name: 'ArrangeInterviewerIsMyList',
            component: () => import('@/views/boss/bossInterviewer/arrangeInterviewerIsMyList.vue'),
            meta: { title: '开始面试' }
        },

    ]
}