// 工作流
export default {
    path: '/activiti',
    name: 'Activiti',
    redirect: '/activiti/process',
    meta: { title: '工作流', icon: 'AppstoreOutlined' },
    children: [
        {
            path: 'actFlow',
            name: 'ActFlow',
            component: () => import('@/views/activiti/actFlow/list.vue'),
            meta: { title: '流程设计' }
        },
        {
            path: 'allactiviti/alltodopossess',
            name: 'alltodopossess',
            component: () => import('@/views/activiti/allactiviti/alltodopossess.vue'),
            meta: { title: '所有待办事宜' }
        },
        {
            path: 'allactiviti/allcomplatepossess',
            name: 'allcomplatepossess',
            component: () => import('@/views/activiti/allactiviti/allcomplatepossess.vue'),
            meta: { title: '所有已完成事务' }
        },
        {
            path: 'myActiviti/processedProcess',
            name: 'processedProcess',
            component: () => import('@/views/activiti/myActiviti/processedProcess.vue'),
            meta: { title: '已办事宜' }
        },
        {
            path: 'myActiviti/initiatedProcess',
            name: 'initiatedProcess',
            component: () => import('@/views/activiti/myActiviti/initiatedProcess.vue'),
            meta: { title: '我的请求' }
        },
        {
            path: 'myActiviti/pendingProcess',
            name: 'pendingProcess',
            component: () => import('@/views/activiti/myActiviti/pendingProcess.vue'),
            meta: { title: '待办事宜' }
        },
        {
            path: 'actGroupList',
            name: 'actGroupList',
            component: () => import('@/views/activiti/actGroupList/index.vue'),
            meta: { title: '待办事宜' }
        }
    ]
}
