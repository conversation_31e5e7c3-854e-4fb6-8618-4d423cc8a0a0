// 智慧门店 
export default {
    path: '/shop',
    name: 'Shop',
    meta: { title: '智慧门店', icon: 'ShopOutlined' },
    children: [
        {
            path: 'outComeStoreIntercourse',
            name: 'StoreFinance',
            component: () => import('@/views/shop/outComeStoreIntercourse/index.vue'),
            meta: { title: '支出来往-缴费门店财务' },
        },
        {
            path: 'inComeStoreIntercourse',
            name: 'maintenanceStore',
            component: () => import('@/views/shop/inComeStoreIntercourse/index.vue'),
            meta: { title: '收入往来-保养门店财务' }
        },
        {
            path: 'orderComment',
            name: 'orderComment',
            component: () => import('@/views/shop/orderComment/index.vue'),
            meta: { title: '商品订单评价管理' }
        },
        {
            path: 'storeStaffDistribute',
            name: 'storeStaffDistribute',
            component: () => import('@/views/shop/storeStaffDistribute/index.vue'),
            meta: { title: '门店人员分配' }
        },
        {
            path: 'storeMaterialsReceipt',
            name: 'storeMaterialsReceipt',
            component: () => import('@/views/shop/storeMaterialsReceipt/index.vue'),
            meta: { title: '门店物料接收单' }
        },
        {
            path: 'storeTypeServiceList',
            name: 'storeTypeServiceList',
            component: () => import('@/views/shop/storeTypeServiceList/index.vue'),
            meta: { title: '门店商品分类管理' }
        },
        {
            path: 'storeInventoryList',
            name: 'storeInventoryList',
            component: () => import('@/views/shop/storeInventoryList/index.vue'),
            meta: { title: '门店商品库存' }
        },
        {
            path: 'storeMembersList',
            name: 'storeMembersList',
            component: () => import('@/views/shop/storeMembersList/index.vue'),
            meta: { title: '门店会员' }
        },
        {
            path: 'shopDeliveryTemplateList',
            name: 'shopDeliveryTemplateList',
            component: () => import('@/views/shop/shopDeliveryTemplateList/index.vue'),
            meta: { title: '快递运费模板' }
        }, {
            path: 'courierCompanyManagementList',
            name: 'courierCompanyManagementList',
            component: () => import('@/views/shop/courierCompanyManagementList/index.vue'),
            meta: { title: '快递公司管理' }
        }, {
            path: 'refundMealOrderList',
            name: 'refundMealOrderList',
            component: () => import('@/views/shop/refundMealOrderList/index.vue'),
            meta: { title: '退款订单' }
        }, {
            path: 'storeMaterialsReturnList',
            name: 'storeMaterialsReturnList',
            component: () => import('@/views/shop/storeMaterialsReturnList/index.vue'),
            meta: { title: '门店物料退货单' }
        }, {
            path: 'storeMaterialsAwaitConfirmationList',
            name: 'storeMaterialsAwaitConfirmationList',
            component: () => import('@/views/shop/storeMaterialsAwaitConfirmationList/index.vue'),
            meta: { title: '门店待确认物料' }
        }, {
            path: 'storeApplicationOrderList',
            name: 'storeApplicationOrderList',
            component: () => import('@/views/shop/storeApplicationOrderList/index.vue'),
            meta: { title: '门店申领单' }
        }, {
            path: 'storeReturnOrderList',
            name: 'storeReturnOrderList',
            component: () => import('@/views/shop/storeReturnOrderList/index.vue'),
            meta: { title: '门店退货单' }
        }, {
            path: 'storeMealOrderList',
            name: 'storeMealOrderList',
            component: () => import('@/views/shop/storeMealOrderList/index.vue'),
            meta: { title: '套餐订单' }
        }, {
            path: 'storeOnlineSetUp',
            name: 'storeOnlineSetUp',
            component: () => import('@/views/shop/storeOnlineSetUp/index.vue'),
            meta: { title: '门店信息设定' }
        }, {
            path: 'storeKeepFitOrderList',
            name: 'storeKeepFitOrderList',
            component: () => import('@/views/shop/storeKeepFitOrderList/index.vue'),
            meta: { title: '保养订单' }
        }
        // 可以添加更多智慧门店相关的功能模块...
    ]
}