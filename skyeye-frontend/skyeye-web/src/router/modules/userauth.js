export default {
    path: 'userauth',
    name: '<PERSON><PERSON><PERSON>',
    meta: { title: '人事管理' },
    children: [
        {
            path: 'sysEveUserStaffList',
            name: 'sysEveUserStaff',
            component: () => import('@/views/userauth/sysEveUserStaff/sysEveUserStaffList.vue'),
            meta: { title: '员工管理' },
        },
        {
            path: 'sysEveUserList',
            name: 'SysEveUser',
            component: () => import('@/views/userauth/sysEveUser/sysEveUserList.vue'),
            meta: { title: '用户管理' },
        },
    ]
} 