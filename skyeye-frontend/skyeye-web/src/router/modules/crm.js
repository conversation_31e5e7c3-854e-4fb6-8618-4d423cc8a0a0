// CRM
export default {
    path: '/crm',
    name: 'CRM',
    meta: { title: 'CRM', icon: 'AppstoreOutlined' },
    children: [
        {
            path: 'service',
            name: 'Service',
            component: () => import('@/views/crm/customer/service.vue'),
            meta: { title: '服务' }
        },
        {
            path: 'pool',
            name: 'Pool',
            component: () => import('@/views/crm/customer/pool.vue'),
            meta: { title: '公海' }
        },
        {
            path: 'statistics/customerGrowth',
            name: 'CustomerGrowth',
            component: () => import('@/views/crm/statistics/customerGrowth.vue'),
            meta: { title: '客户联系人月增量分析' }
        },
        {
            path: 'statistics/documentaryAnalysis',
            name: 'DocumentaryAnalysis',
            component: () => import('@/views/crm/statistics/documentaryAnalysis.vue'),
            meta: { title: '客户跟单方式分析' }
        },
        {
            path: 'statistics/contractGrowth',
            name: 'ContractGrowth',
            component: () => import('@/views/crm/statistics/contractGrowth.vue'),
            meta: { title: '合同月增量分析' }
        },
        {
            path: 'statistics/documentaryGrowth',
            name: 'DocumentaryGrowth',
            component: () => import('@/views/crm/statistics/documentaryGrowth.vue'),
            meta: { title: '员工跟单统计分析' }
        },
        {
            path: 'statistics/customerAnalysis',
            name: 'CustomerAnalysis',
            component: () => import('@/views/crm/statistics/customerAnalysis.vue'),
            meta: { title: '客户总量分析' }
        }
    ]
}