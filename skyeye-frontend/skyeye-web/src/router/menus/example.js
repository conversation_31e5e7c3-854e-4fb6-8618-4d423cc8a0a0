export default [
    {
        id: 'example-base',
        key: 'example-base',
        name: '基础组件',
        childs: [
            {
                id: 'example-sk-tabs',
                key: 'example-sk-tabs',
                name: '选项卡',
                path: '/example/sk-tabs',
                icon: 'TabsOutlined',
                iconType: '1'
            },
            {
                id: 'example-collapse',
                key: 'example-collapse',
                name: '折叠面板',
                path: '/example/collapse',
                icon: 'MenuFoldOutlined',
                iconType: '1'
            },
            {
                id: 'example-badge',
                key: 'example-badge',
                name: '徽标',
                path: '/example/badge',
                icon: 'BadgeOutlined',
                iconType: '1'
            },
            {
                id: 'example-tree',
                key: 'example-tree',
                name: '树形控件',
                path: '/example/tree',
                icon: 'TreeOutlined',
                iconType: '1'
            },
            {
                id: 'example-popover',
                key: 'example-popover',
                name: '气泡卡片',
                path: '/example/popover',
                icon: 'PopoverOutlined',
                iconType: '1'
            },
            {
                id: 'example-segmented',
                key: 'example-segmented',
                name: '分段器',
                path: '/example/segmented',
                icon: 'SegmentedOutlined',
                iconType: '1'
            },
            {
                id: 'example-qrcode',
                key: 'example-qrcode',
                name: '二维码',
                path: '/example/qrcode',
                icon: 'QrcodeOutlined',
                iconType: '1'
            },
            {
                id: 'example-empty',
                key: 'example-empty',
                name: '空状态组件',
                path: '/example/empty',
                icon: 'EmptyOutlined',
                iconType: '1'
            },
            {
                id: 'example-image',
                key: 'example-image',
                name: '图片',
                path: '/example/image',
                icon: 'PictureOutlined',
                iconType: '1'
            },
            {
                id: 'example-avatar',
                key: 'example-avatar',
                name: '头像',
                path: '/example/avatar',
                icon: 'UserOutlined',
                iconType: '1'
            },
            {
                id: 'example-button',
                key: 'example-button',
                name: '按钮',
                path: '/example/button',
                icon: 'ApartmentOutlined',
                iconType: '1'
            }
        ]
    },
    {
        id: 'example-nav',
        key: 'example-nav',
        name: '导航组件',
        childs: [
            {
                id: 'example-anchor',
                key: 'example-anchor',
                name: '锚点',
                path: '/example/anchor',
                icon: 'ApartmentOutlined',
                iconType: '1'
            },
            {
                id: 'example-breadcrumb',
                key: 'example-breadcrumb',
                name: '面包屑',
                path: '/example/breadcrumb',
                icon: 'ApartmentOutlined',
                iconType: '1'
            },
            {
                id: 'example-dropdown',
                key: 'example-dropdown',
                name: '下拉菜单',
                path: '/example/dropdown',
                icon: 'ApartmentOutlined',
                iconType: '1'
            },
            {
                id: 'example-menu',
                key: 'example-menu',
                name: '菜单',
                path: '/example/menu',
                icon: 'ApartmentOutlined',
                iconType: '1'
            },
            {
                id: 'example-pageheader',
                key: 'example-pageheader',
                name: '页面头',
                path: '/example/pageheader',
                icon: 'ApartmentOutlined',
                iconType: '1'
            },
            {
                id: 'example-pagination',
                key: 'example-pagination',
                name: '分页',
                path: '/example/pagination',
                icon: 'ApartmentOutlined',
                iconType: '1'
            },
            {
                id: 'example-steps',
                key: 'example-steps',
                name: '步骤条',
                path: '/example/steps',
                icon: 'ApartmentOutlined',
                iconType: '1'
            }
        ]
    },
    {
        id: 'example-layout',
        key: 'example-layout',
        name: '布局组件',
        childs: [
            {
                id: 'example-divider',
                key: 'example-divider',
                name: '分割线',
                path: '/example/divider',
                icon: 'ApartmentOutlined',
                iconType: '1'
            },
            {
                id: 'example-flex',
                key: 'example-flex',
                name: 'Flex 布局',
                path: '/example/flex',
                icon: 'ApartmentOutlined',
                iconType: '1'
            },
            {
                id: 'example-grid',
                key: 'example-grid',
                name: '栅格布局',
                path: '/example/grid',
                icon: 'ApartmentOutlined',
                iconType: '1'
            },
            {
                id: 'example-layout',
                key: 'example-layout',
                name: '布局组件',
                path: '/example/layout',
                icon: 'ApartmentOutlined',
                iconType: '1'
            },
            {
                id: 'example-space',
                key: 'example-space',
                name: '间距组件',
                path: '/example/space',
                icon: 'ApartmentOutlined',
                iconType: '1'
            }
        ]
    },
    {
        id: 'example-form',
        key: 'example-form',
        name: '表单组件',
        childs: [
            {
                id: 'example-form',
                key: 'example-form',
                name: '表单组件',
                path: '/example/form',
                icon: 'FormOutlined',
                iconType: '1'
            },
            {
                id: 'example-auto-complete',
                key: 'example-auto-complete',
                name: '自动完成',
                path: '/example/auto-complete',
                icon: 'ApartmentOutlined',
                iconType: '1'
            },
            {
                id: 'example-input',
                key: 'example-input',
                name: '输入框',
                path: '/example/input',
                icon: 'FormOutlined',
                iconType: '1'
            },
            {
                id: 'example-input-number',
                key: 'example-input-number',
                name: '数字输入框',
                path: '/example/input-number',
                icon: 'FormOutlined',
                iconType: '1'
            },
            {
                id: 'example-textarea',
                key: 'example-textarea',
                name: '文本域',
                path: '/example/textarea',
                icon: 'FormOutlined',
                iconType: '1'
            },
            {
                id: 'example-select',
                key: 'example-select',
                name: '选择器',
                path: '/example/select',
                icon: 'FormOutlined',
                iconType: '1'
            },
            {
                id: 'example-radio',
                key: 'example-radio',
                name: '单选框',
                path: '/example/radio',
                icon: 'FormOutlined',
                iconType: '1'
            },
            {
                id: 'example-checkbox',
                key: 'example-checkbox',
                name: '多选框',
                path: '/example/checkbox',
                icon: 'FormOutlined',
                iconType: '1'
            },
            {
                id: 'example-date-picker',
                key: 'example-date-picker',
                name: '日期选择框',
                path: '/example/date-picker',
                icon: 'FormOutlined',
                iconType: '1'
            },
            {
                id: 'example-time-picker',
                key: 'example-time-picker',
                name: '时间选择框',
                path: '/example/time-picker',
                icon: 'FormOutlined',
                iconType: '1'
            },
            {
                id: 'example-color-picker',
                key: 'example-color-picker',
                name: '颜色选择器',
                path: '/example/color-picker',
                icon: 'FormOutlined',
                iconType: '1'
            },
            {
                id: 'example-slider',
                key: 'example-slider',
                name: '滑块',
                path: '/example/slider',
                icon: 'FormOutlined',
                iconType: '1'
            },
            {
                id: 'example-rate',
                key: 'example-rate',
                name: '评分',
                path: '/example/rate',
                icon: 'StarOutlined',
                iconType: '1'
            },
            {
                id: 'example-switch',
                key: 'example-switch',
                name: '开关选择器',
                path: '/example/switch',
                icon: 'SwitcherOutlined',
                iconType: '1'
            },
            {
                id: 'example-transfer',
                key: 'example-transfer',
                name: '穿梭框',
                path: '/example/transfer',
                icon: 'RetweetOutlined',
                iconType: '1'
            },
            {
                id: 'example-tree-select',
                key: 'example-tree-select',
                name: '树型选择',
                path: '/example/tree-select',
                icon: 'ApartmentOutlined',
                iconType: '1'
            },
            {
                id: 'example-upload',
                key: 'example-upload',
                name: '上传',
                path: '/example/upload',
                icon: 'ApartmentOutlined',
                iconType: '1'
            },
            {
                id: 'example-hr-title',
                key: 'example-hr-title',
                name: '分隔标题',
                path: '/example/hr-title',
                icon: 'ApartmentOutlined',
                iconType: '1'
            }
        ]
    },
    {
        id: 'example-advance',
        key: 'example-advance',
        name: '高级用法',
        childs: [
            {
                id: 'example-i18n',
                key: 'example-i18n',
                name: '国际化',
                path: '/example/i18n',
                icon: 'GlobalOutlined',
                iconType: '1'
            },
            {
                id: 'example-annotation',
                key: 'example-annotation',
                name: '批注组件',
                path: '/example/annotation',
                icon: 'ApartmentOutlined',
                iconType: '1'
            }
        ]
    },
    {
        id: 'example-business',
        key: 'example-business',
        name: '业务组件',
        childs: [
            {
                id: 'example-sk-account',
                key: 'example-sk-account',
                name: '账户选择',
                path: '/example/account',
                icon: 'AccountBookOutlined',
                iconType: '1'
            }
        ]
    },
    {
        id: 'example-display',
        key: 'example-display',
        name: '数据展示组件',
        childs: [
            {
                id: 'example-statistic',
                key: 'example-statistic',
                name: '统计数值',
                path: '/example/statistic',
                icon: 'FundOutlined',
                iconType: '1'
            },
            {
                id: 'example-tag',
                key: 'example-tag',
                name: '标签',
                path: '/example/tag',
                icon: 'TagOutlined',
                iconType: '1'
            },
            {
                id: 'example-timeline',
                key: 'example-timeline',
                name: '时间轴',
                path: '/example/timeline',
                icon: 'ClockCircleOutlined',
                iconType: '1'
            },
            {
                id: 'example-tooltip',
                key: 'example-tooltip',
                name: '文字提示',
                path: '/example/tooltip',
                icon: 'InfoCircleOutlined',
                iconType: '1'
            },
            {
                id: 'example-tour',
                key: 'example-tour',
                name: '引导页',
                path: '/example/tour',
                icon: 'InfoCircleOutlined',
                iconType: '1'
            },
            {
                id: 'example-table',
                key: 'example-table',
                name: '表格',
                path: '/example/table',
                icon: 'ApartmentOutlined',
                iconType: '1'
            },
            {
                id: 'example-list',
                key: 'example-list',
                name: '列表',
                path: '/example/list',
                icon: 'ApartmentOutlined',
                iconType: '1'
            },
            {
                id: 'example-description',
                key: 'example-description',
                name: '描述列表',
                path: '/example/description',
                icon: 'ApartmentOutlined',
                iconType: '1'
            },
            {
                id: 'example-comment',
                key: 'example-comment',
                name: '评论组件',
                path: '/example/comment',
                icon: 'ApartmentOutlined',
                iconType: '1'
            },
            {
                id: 'example-carousel',
                key: 'example-carousel',
                name: '轮播图',
                path: '/example/carousel',
                icon: 'ApartmentOutlined',
                iconType: '1'
            },
            {
                id: 'example-card',
                key: 'example-card',
                name: '卡片',
                path: '/example/card',
                icon: 'ApartmentOutlined',
                iconType: '1'
            },
            {
                id: 'example-calendar',
                key: 'example-calendar',
                name: '日历',
                path: '/example/calendar',
                icon: 'ApartmentOutlined',
                iconType: '1'
            }
        ]
    },
    {
        id: 'example-feedback',
        key: 'example-feedback',
        name: '反馈组件',
        childs: [
            {
                id: 'example-alert',
                key: 'example-alert',
                name: '提示框',
                path: '/example/alert',
                icon: 'InfoCircleOutlined',
                iconType: '1'
            },
            {
                id: 'example-drawer',
                key: 'example-drawer',
                name: '抽屉',
                path: '/example/drawer',
                icon: 'ApartmentOutlined',
                iconType: '1'
            },
            {
                id: 'example-message',
                key: 'example-message',
                name: '消息提示',
                path: '/example/message',
                icon: 'MessageOutlined',
                iconType: '1'
            },
            {
                id: 'example-modal',
                key: 'example-modal',
                name: '对话框',
                path: '/example/modal',
                icon: 'ApartmentOutlined',
                iconType: '1'
            },
            {
                id: 'example-notification',
                key: 'example-notification',
                name: '通知提醒框',
                path: '/example/notification',
                icon: 'ApartmentOutlined',
                iconType: '1'
            },
            {
                id: 'example-popconfirm',
                key: 'example-popconfirm',
                name: '气泡确认框',
                path: '/example/popconfirm',
                icon: 'ApartmentOutlined',
                iconType: '1'
            },
            {
                id: 'example-progress',
                key: 'example-progress',
                name: '进度条',
                path: '/example/progress',
                icon: 'ApartmentOutlined',
                iconType: '1'
            },
            {
                id: 'example-result',
                key: 'example-result',
                name: '结果页',
                path: '/example/result',
                icon: 'ApartmentOutlined',
                iconType: '1'
            },
            {
                id: 'example-skeleton',
                key: 'example-skeleton',
                name: '骨架屏',
                path: '/example/skeleton',
                icon: 'ApartmentOutlined',
                iconType: '1'
            },
            {
                id: 'example-spin',
                key: 'example-spin',
                name: '加载中',
                path: '/example/spin',
                icon: 'ApartmentOutlined',
                iconType: '1'
            }
        ]
    },
    {
        id: 'uni-example',
        key: 'uni-example',
        name: 'UNI-组件示例',
        childs: [
            {
                id: 'app-management',
                key: 'app-management',
                name: '应用管理',
                path: '/app-management/list',
                icon: 'AppstoreOutlined',
                iconType: '1'
            },
            {
                id: 'threejs',
                key: 'threejs',
                name: 'Threejs',
                path: '/app-management/threejs',
                icon: 'AppstoreOutlined',
                iconType: '1'
            },
            {
                id: 'conference',
                key: 'conference',
                name: '会议',
                path: '/admin-assistant/conference',
                icon: 'AppstoreOutlined',
                iconType: '1'
            },
            {
                id: 'office',
                key: 'office',
                name: 'Office预览',
                path: '/example/office',
                icon: 'AppstoreOutlined',
                iconType: '1'
            },
            {
                id: 'print-template',
                key: 'print-template',
                name: '打印模板',
                path: '/example/print-template',
            },
            {
                id: 'scheduling',
                key: 'scheduling',
                name: '排班管理',
                path: '/example/scheduling',
            },
            {
                id: 'importExport',
                key: 'importExport',
                name: '导入导出',
                path: '/system/importExport',
            }
        ]
    }
]