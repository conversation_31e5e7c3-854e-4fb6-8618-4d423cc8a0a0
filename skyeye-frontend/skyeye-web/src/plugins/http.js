import axios from 'axios'
import { message } from 'ant-design-vue'
import router from '@/router'
import $config from '@/plugins/getConfig'

// 创建 axios 实例
const service = axios.create({
    baseURL: import.meta.env.VITE_API_URL || '/api',
    timeout: 15000,
    headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
    }
})

// 请求拦截器
service.interceptors.request.use(
    config => {
        const token = $config.getUserToken()
        if (token) {
            config.headers['userToken'] = token
        }
        return config
    },
    error => {
        console.error('请求错误：', error)
        return Promise.reject(error)
    }
)

// 响应拦截器
service.interceptors.response.use(
    response => {
        const res = response.data

        if (res.returnCode === 0) {
            return res
        } else {
            let sessionstatus = response.headers.sessionstatus
            let msg = res.returnMessage
            if (sessionstatus === 'TIMEOUT') {
                $config.setUserToken('')
                router.push('/login')
            } else if (sessionstatus == "NOAUTHPOINT") {
                msg = '没有权限访问'
            } else if (sessionstatus == "APINOTFOUND") {
                msg = '接口不存在'
            }

            return Promise.reject(new Error(msg || '请求失败'))
        }
    },
    error => {
        console.error('响应错误：', error)

        if (error.response) {
            switch (error.response.status) {
                case 401:
                    message.error('未登录或登录已过期')
                    localStorage.removeItem('token')
                    router.push('/login')
                    break
                case 403:
                    message.error('没有权限访问')
                    break
                case 404:
                    message.error('请求的资源不存在')
                    break
                case 500:
                    message.error('服务器错误')
                    break
                default:
                    message.error(error.response.data?.message || '请求失败')
            }
        } else {
            message.error('网络错误，请检查网络连接')
        }

        return Promise.reject(error)
    }
)

// 封装请求方法
const http = {
    get(url, params, config = {}) {
        return service.get(url, { params, ...config })
    },

    post(url, data, config = {}) {
        return service.post(url, data, config)
    },

    put(url, data, config = {}) {
        return service.put(url, data, config)
    },

    delete(url, params, config = {}) {
        return service.delete(url, { params, ...config })
    },

    request(config) {
        return service.request(config)
    },

    // 上传文件
    upload(url, file, onProgress) {
        const formData = new FormData()
        formData.append('file', file)

        return service.post(url, formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            },
            onUploadProgress: progressEvent => {
                if (onProgress) {
                    const progress = (progressEvent.loaded / progressEvent.total) * 100
                    onProgress(Math.round(progress))
                }
            }
        })
    },

    // 下载文件
    download(url, params, filename) {
        return service.get(url, {
            params,
            responseType: 'blob'
        }).then(response => {
            const blob = new Blob([response])
            const link = document.createElement('a')
            link.href = window.URL.createObjectURL(blob)
            link.download = filename
            link.click()
            window.URL.revokeObjectURL(link.href)
        })
    },

    cachesIsNull(key) {
        var value = localStorage.getItem(key);
        if (value == null || value == '' || value == 'undefined') {
            return true;
        } else {
            return false;
        }
    }
}

// 注册全局方法
export function setupHttp(app) {
    // 添加到全局属性
    app.config.globalProperties.$http = http
    // 添加到全局对象
    window.$http = http
}

export default http 