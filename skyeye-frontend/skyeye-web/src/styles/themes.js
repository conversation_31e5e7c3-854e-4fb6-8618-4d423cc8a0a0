export const themes = {
  default: {
    name: 'theme.default',
    header: {
      background: '#fff',
      textColor: 'rgba(0, 0, 0, 0.85)',
      shadow: '0 1px 4px rgba(0, 21, 41, 0.08)'
    },
    sider: {
      background: '#001529',
      textColor: 'rgba(255, 255, 255, 0.65)',
      activeTextColor: '#fff',
      activeBg: '#1890ff'
    },
    tabs: {
      background: 'transparent',
      itemBg: 'transparent',
      itemActiveBg: '#e6f4ff',
      itemColor: 'rgba(0, 0, 0, 0.85)',
      itemActiveColor: '#1890ff'
    }
  },
  dark: {
    name: 'theme.dark',
    header: {
      background: '#1f1f1f',
      textColor: 'rgba(255, 255, 255, 0.85)',
      shadow: '0 1px 4px rgba(0, 0, 0, 0.15)'
    },
    sider: {
      background: '#141414',
      textColor: 'rgba(255, 255, 255, 0.65)',
      activeTextColor: '#fff',
      activeBg: '#1890ff'
    },
    tabs: {
      background: 'transparent',
      itemBg: 'transparent',
      itemActiveBg: 'rgba(24, 144, 255, 0.1)',
      itemColor: 'rgba(255, 255, 255, 0.85)',
      itemActiveColor: '#1890ff'
    }
  },
  blue: {
    name: 'theme.blue',
    header: {
      background: '#1e88e5',
      color: '#fff',
      boxShadow: '0 1px 4px rgba(0, 21, 41, 0.08)'
    },
    sider: {
      background: '#0d47a1',
      textColor: 'rgba(255, 255, 255, 0.75)',
      activeTextColor: '#fff',
      activeBg: 'rgba(255, 255, 255, 0.1)'
    },
    tabs: {
      background: 'transparent',
      itemBg: 'transparent',
      itemActiveBg: 'rgba(255, 255, 255, 0.1)',
      itemColor: '#fff',
      itemActiveColor: '#fff'
    }
  },
  green: {
    name: 'theme.green',
    header: {
      background: '#fff',
      color: 'rgba(0, 0, 0, 0.85)',
      boxShadow: '0 1px 4px rgba(0, 21, 41, 0.08)'
    },
    sider: {
      background: '#237804',
      textColor: 'rgba(255, 255, 255, 0.75)',
      activeTextColor: '#fff',
      activeBg: 'rgba(255, 255, 255, 0.1)'
    },
    tabs: {
      background: 'transparent',
      itemBg: 'transparent',
      itemActiveBg: '#f6ffed',
      itemColor: 'rgba(0, 0, 0, 0.85)',
      itemActiveColor: '#52c41a'
    }
  },
  purple: {
    name: 'theme.purple',
    header: {
      background: '#722ed1',
      color: '#fff',
      boxShadow: '0 1px 4px rgba(0, 21, 41, 0.08)'
    },
    sider: {
      background: '#391085',
      textColor: 'rgba(255, 255, 255, 0.75)',
      activeTextColor: '#fff',
      activeBg: 'rgba(255, 255, 255, 0.1)'
    },
    tabs: {
      background: 'transparent',
      itemBg: 'transparent',
      itemActiveBg: 'rgba(255, 255, 255, 0.1)',
      itemColor: '#fff',
      itemActiveColor: '#fff'
    }
  }
}

export const getThemeVariables = (themeName) => {
  const theme = themes[themeName] || themes.default
  return {
    '--header-bg': theme.header.background,
    '--header-color': theme.header.color,
    '--header-shadow': theme.header.boxShadow,
    '--sider-bg': theme.sider.background,
    '--sider-text-color': theme.sider.textColor,
    '--sider-active-text-color': theme.sider.activeTextColor,
    '--sider-active-bg': theme.sider.activeBg,
    '--tabs-bg': theme.tabs.background,
    '--tabs-item-bg': theme.tabs.itemBg,
    '--tabs-item-active-bg': theme.tabs.itemActiveBg,
    '--tabs-item-color': theme.tabs.itemColor,
    '--tabs-item-active-color': theme.tabs.itemActiveColor
  }
} 