import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import Antd from 'ant-design-vue'
import 'viewerjs/dist/viewer.css'

import { setupHttp } from '@/plugins/http'
import { setupConfig } from '@/plugins/getConfig'
import { setupUtil } from '@/plugins/util'
import { setupWebSocket, WebSocketClient } from '@/plugins/websocket'

import 'ant-design-vue/dist/reset.css'
import { setupFontAwesome } from '@/plugins/fontawesome'
import '@/styles/common.css'
import * as Icons from '@ant-design/icons-vue'
import { i18n } from './plugins/i18n'

const app = createApp(App)

// 注册所有图标
for (const i in Icons) {
    app.component(i, Icons[i])
}

// 注册 HTTP 工具
setupHttp(app)

// 注册全局配置
setupConfig(app)

// 注册全局工具
setupUtil(app)

// 设置 Font Awesome
setupFontAwesome(app)

// 注册默认的 WebSocket 实例
if (app.config.globalProperties.$config.getCurrentUserId()) {
    // const baseUrl = 'ws://localhost:8081/talkwebsocket/' + app.config.globalProperties.$config.getCurrentUserId() || ''
    const baseUrl = app.config.globalProperties.$config.getConfig().webSocketPath + '/talkwebsocket/' + app.config.globalProperties.$config.getCurrentUserId() || ''
    setupWebSocket(app, {
        url: `${baseUrl.replace(/^http/, 'ws')}`
    })
}

// WebSocketClient使用示例
// // 创建并注册其他 WebSocket 实例
// const wsNotification = new WebSocketClient()

// // 将实例添加到全局属性
// app.config.globalProperties.$wsNotification = wsNotification

// // 初始化连接
// const token = localStorage.getItem('token')
// const baseUrl = app.config.globalProperties.$config.getConfig().baseUrl || ''

// // 连接不同的 WebSocket 端点
// wsNotification.connect(`${baseUrl.replace(/^http/, 'ws')}/notification`, token)

app.use(router)
app.use(Antd)
app.use(i18n)
app.mount('#app') 