# SkyEye ERP 小程序项目

基于 uni-app + uView Plus 构建的企业资源管理系统移动端小程序。

## 🚀 项目特性

- **🎯 多端支持**: 支持微信小程序、H5、APP等多个平台
- **🎨 现代化UI**: 基于uView Plus UI框架，界面美观易用
- **📱 响应式设计**: 适配各种屏幕尺寸
- **🔐 完善的权限**: 支持多租户、用户权限管理
- **📊 数据可视化**: 丰富的图表和统计功能
- **🔄 实时同步**: 数据实时更新，多端同步

## 📦 技术栈

- **前端框架**: uni-app (Vue 3)
- **UI组件库**: uView Plus
- **状态管理**: Vuex
- **构建工具**: Vite
- **样式预处理**: SCSS
- **时间处理**: dayjs
- **HTTP请求**: 基于uni.request封装

## 🏗️ 项目结构

```
skyeye-frontend/skyeye-miniapp/src/
├── api/                 # API接口定义
│   ├── auth.js         # 认证相关
│   ├── order.js        # 订单管理
│   ├── inventory.js    # 库存管理
│   ├── customer.js     # 客户管理
│   └── ...
├── components/         # 公用组件
├── pages/             # 页面文件
│   ├── login/         # 登录页
│   ├── index/         # 首页
│   ├── profile/       # 个人中心
│   ├── business/      # 业务模块
│   └── common/        # 通用页面
├── static/            # 静态资源
├── store/             # Vuex状态管理
│   ├── app.js         # 应用状态
│   └── user.js        # 用户状态
├── utils/             # 工具函数
│   └── request.js     # HTTP请求封装
├── App.vue            # 应用入口
├── main.js            # 主入口文件
├── manifest.json      # 应用配置
├── pages.json         # 页面路由配置
├── uni.scss           # 全局样式变量
└── vite.config.js     # Vite配置
```

## 🚀 快速开始

### 1. 环境准备

确保已安装以下工具：

- **Node.js**: >= 16.0.0
- **npm**: >= 8.0.0 或 **yarn**: >= 1.22.0
- **HBuilderX**: 最新版本（用于小程序开发）

### 2. 安装依赖

```bash
# 进入项目目录
cd skyeye-frontend/skyeye-miniapp/src

# 安装依赖
npm install
```

### 3. 配置后端接口

修改 `utils/request.js` 文件中的接口地址：

```javascript
getBaseURL() {
  // 修改为您的后端接口地址
  return 'http://your-backend-url:7000'
}
```

### 4. 运行项目

#### 方式一：使用HBuilderX

1. 用HBuilderX打开项目
2. 选择运行 -> 运行到小程序模拟器 -> 微信开发者工具

#### 方式二：命令行运行

```bash
# 开发模式（微信小程序）
npm run dev:mp-weixin

# 开发模式（H5）
npm run dev:h5

# 开发模式（APP）
npm run dev:app
```

### 5. 构建发布

```bash
# 构建微信小程序
npm run build:mp-weixin

# 构建H5
npm run build:h5

# 构建APP
npm run build:app
```

## 📋 功能模块

### 🏠 首页模块
- 数据统计概览
- 快捷功能入口
- 最近订单展示
- 待办事项提醒

### 📋 订单管理
- 订单列表查看
- 订单详情查看
- 订单状态管理
- 订单搜索筛选

### 📦 库存管理
- 库存列表查看
- 库存详情查看
- 库存预警提醒
- 进销存记录

### 👥 客户管理
- 客户信息维护
- 客户分类管理
- 客户关系跟踪

### 👤 个人中心
- 个人信息管理
- 系统设置
- 消息通知
- 帮助中心

## 🔧 配置说明

### API接口配置

在 `api/` 目录下配置各模块的接口地址，所有接口都基于统一的请求封装。

### 主题配置

在 `uni.scss` 文件中配置全局样式变量，支持主题色、间距、字体等自定义。

### 页面路由配置

在 `pages.json` 文件中配置页面路由、tabBar、导航栏等。

## 📱 部署说明

### 微信小程序

1. 在 `manifest.json` 中配置小程序appid
2. 使用微信开发者工具打开dist/build/mp-weixin目录
3. 上传代码并提交审核

### H5部署

1. 构建H5版本：`npm run build:h5`
2. 将dist/build/h5目录部署到web服务器

### APP打包

1. 使用HBuilderX打开项目
2. 发行 -> 原生App-云打包
3. 配置证书和包名信息

## 🤝 开发规范

### 代码规范

- 使用ES6+语法
- 遵循Vue 3 Composition API规范
- 统一使用SCSS编写样式
- 组件命名采用PascalCase
- 文件命名采用kebab-case

### 提交规范

```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动
```

## 🐛 常见问题

### 1. 编译错误

- 检查Node.js版本是否满足要求
- 清除node_modules重新安装依赖
- 检查代码语法是否正确

### 2. 接口请求失败

- 检查后端服务是否启动
- 检查接口地址配置是否正确
- 检查网络连接是否正常

### 3. 小程序运行问题

- 检查微信开发者工具版本
- 检查appid配置是否正确
- 检查小程序权限设置

## 📞 技术支持

如遇到问题，请联系：

- **技术支持**: <EMAIL>
- **问题反馈**: [GitHub Issues](https://github.com/skyeye-team/erp/issues)

## 📄 许可证

本项目基于 MIT 许可证开源 - 查看 [LICENSE](LICENSE) 文件了解详情。

---

**Built with ❤️ by SkyEye Team**