<template>
  <view id="app">
    <!-- 页面内容会在这里渲染 -->
  </view>
</template>

<script>
export default {
  name: 'App',
  onLaunch: function() {
    console.log('SkyEye ERP小程序启动')
    // 应用启动时的逻辑
    this.initApp()
  },
  onShow: function() {
    console.log('App Show')
  },
  onHide: function() {
    console.log('App Hide')
  },
  methods: {
    initApp() {
      // 初始化应用配置
      this.checkLogin()
      this.loadUserInfo()
    },
    checkLogin() {
      // 检查登录状态
      const token = uni.getStorageSync('token')
      if (!token) {
        console.log('用户未登录')
      }
    },
    loadUserInfo() {
      // 加载用户信息
      const userInfo = uni.getStorageSync('userInfo')
      if (userInfo) {
        console.log('用户信息:', userInfo)
      }
    }
  }
}
</script>

<style lang="scss">
/* 引入uView的全局样式 */
@import 'uview-plus/index.scss';

/* 全局样式 */
page {
  font-family: -apple-system,BlinkMacSystemFont,'Helvetica Neue',Helvetica,Segoe UI,Arial,Roboto,'PingFang SC','miui','Hiragino Sans GB','Microsoft Yahei',sans-serif;
  background-color: #f8f8f8;
}

#app {
  height: 100%;
}

/* 通用类 */
.container {
  padding: 20rpx;
}

.text-center {
  text-align: center;
}

.mt-20 {
  margin-top: 20rpx;
}

.mb-20 {
  margin-bottom: 20rpx;
}

.p-20 {
  padding: 20rpx;
}

/* ERP主题色彩 */
.primary-color {
  color: #007AFF;
}

.success-color {
  color: #19BE6B;
}

.warning-color {
  color: #FF9900;
}

.error-color {
  color: #FA3534;
}
</style>