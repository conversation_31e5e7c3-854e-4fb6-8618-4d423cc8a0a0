@use 'sass:math';

:root {
    --design-width: #{$vmDesignWidth}px;
    --design-multiple: #{$vmDesignMultiple};
    --max-window: #{$vmMaxWindow};
    --min-window: #{$vmMinWindow};
}

/* #ifdef H5 */
html {
    --body-width: #{$vmDesignWidth}px;

    // --tabbar-height: 50Px; 直接使用 --tab-bar-height
}

body {
    min-width: var(--min-window);
    font-size: 24px;
    line-height: 1;
    color: #000;
    background: #fff;

    div,
    span {
        box-sizing: border-box;
    }
}

// px2rpx 已将 .uni- 开头的类过滤转换, 所以直接写rpx单位
.uni-tabbar {
    left: 50% !important;
    width: 750rpx !important;
    margin-left: -375rpx !important;
}

.wd-navbar.is-fixed {
    left: 50% !important;
    width: 750px !important;
    margin-left: -375px !important;
}

/* #endif */
view,
text {
    box-sizing: border-box;
}

page {
    box-sizing: border-box;
}

.line-2,
[line-2=''],
.line-3,
[line-3=''],
.line-4,
[line-4=''] {
    white-space: break-spaces;
}

.wrap {
    position: relative;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    width: 750px;
    min-height: 100vh;
    margin: 0 auto;
    overflow: hidden;
    color: #000;
    background: #fefaec;

    &.wrap-tab {
        min-height: calc(100vh - var(--window-bottom));
    }

    &.dark {
        color: #fff;
        background: #121212;
    }

    &.layout-img {
        background: #fefaec url('http://cdn.mmxiaowu.com/upload/layout-home-bg.png') no-repeat;
        background-size: cover;
    }

    &.layout-white {
        background: #fff;
    }

    &.layout-gray {
        background: #f0f0f0;
    }

    &.layout-invite {
        background: #fefaec url('http://cdn.mmxiaowu.com/upload/invite-bg.png') no-repeat;
        background-size: contain;
    }

    &.layout-laird {
        background: #fefaec url('http://cdn.mmxiaowu.com/upload/laird-bg-2.png') no-repeat;
        background-size: contain;
    }
}

.global-loading {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 999;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100vw;
    height: 100vh;
    background: rgb(255, 255, 255, 0.1);
}

.router-loading {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100vw;
    height: 100vh;
    background: rgb(255, 255, 255, 0.1);
}

.fixed-center {
    left: calc((100% - var(--body-width)) / 2) !important;
    width: 100%;
    min-width: var(--min-window);
    max-width: var(--max-window);
}

.load-more {
    margin: 10px 0;
}

.wx-btn {
    display: flex;
    padding: 0;
    margin: 0;
    background: none;
    border: none;
    outline: none;

    &::before,
    &::after {
        display: none;
    }
}

/* 通用样式 */

.slide-left-enter,
.slide-right-leave-active {
    opacity: 0;
    transform: translate(100%, 0);
}

.slide-left-leave-active,
.slide-right-enter {
    opacity: 0;
    transform: translate(-100%, 0);
}

/* .fade-leave-active below version 2.1.8 */
.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.5s;
}

.fade-enter,
.fade-leave-to {
    opacity: 0;
}
