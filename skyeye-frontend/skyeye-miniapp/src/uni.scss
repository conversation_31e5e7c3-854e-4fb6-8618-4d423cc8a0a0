/**
 * SkyEye ERP 全局样式配置
 * 这里定义的样式变量，在全局都可以使用
 */

/* 颜色变量 */
$uni-color-primary: #007AFF;
$uni-color-success: #19BE6B;
$uni-color-warning: #FF9900;
$uni-color-error: #FA3534;
$uni-color-info: #909399;

/* 背景色 */
$uni-bg-color: #FFFFFF;
$uni-bg-color-grey: #F8F8F8;
$uni-bg-color-hover: #F1F1F1;
$uni-bg-color-mask: rgba(0, 0, 0, 0.4);

/* 边框颜色 */
$uni-border-color: #E4E7ED;

/* 文字颜色 */
$uni-text-color: #303133;
$uni-text-color-regular: #606266;
$uni-text-color-secondary: #909399;
$uni-text-color-placeholder: #C0C4CC;

/* 文字大小 */
$uni-font-size-sm: 24rpx;
$uni-font-size-base: 28rpx;
$uni-font-size-lg: 32rpx;

/* 间距 */
$uni-spacing-sm: 16rpx;
$uni-spacing-base: 24rpx;
$uni-spacing-lg: 32rpx;

/* 行高 */
$uni-line-height-sm: 1.2;
$uni-line-height-base: 1.4;
$uni-line-height-lg: 1.6;

/* 圆角 */
$uni-border-radius-sm: 6rpx;
$uni-border-radius-base: 12rpx;
$uni-border-radius-lg: 24rpx;
$uni-border-radius-circle: 50%;

/* 阴影 */
$uni-shadow-sm: 0 4rpx 12rpx rgba(0, 0, 0, 0.04);
$uni-shadow-base: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
$uni-shadow-lg: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);

/* ERP业务相关颜色 */
$erp-color-pending: #FF9900;    // 待处理
$erp-color-processing: #007AFF; // 处理中
$erp-color-completed: #19BE6B;  // 已完成
$erp-color-cancelled: #909399;  // 已取消
$erp-color-overdue: #FA3534;    // 逾期

/* 状态颜色映射 */
$erp-status-colors: (
  'pending': $erp-color-pending,
  'processing': $erp-color-processing,
  'completed': $erp-color-completed,
  'cancelled': $erp-color-cancelled,
  'overdue': $erp-color-overdue
);

/* 优先级颜色 */
$erp-priority-low: #909399;
$erp-priority-normal: #007AFF;
$erp-priority-high: #FF9900;
$erp-priority-urgent: #FA3534;

/* 组件样式变量 */
$card-padding: 32rpx;
$card-margin: 24rpx;
$card-border-radius: $uni-border-radius-base;
$card-shadow: $uni-shadow-sm;

/* 表单相关 */
$form-item-height: 88rpx;
$form-item-margin: 24rpx;
$input-height: 68rpx;
$input-padding: 24rpx;

/* 列表相关 */
$list-item-height: 120rpx;
$list-item-padding: 32rpx 24rpx;
$list-divider-color: #F0F0F0;

/* 按钮相关 */
$button-height-sm: 60rpx;
$button-height-base: 80rpx;
$button-height-lg: 100rpx;
$button-border-radius: $uni-border-radius-base;

/* 头部导航 */
$navbar-height: 88rpx;
$navbar-padding: 24rpx;

/* tabbar相关 */
$tabbar-height: 120rpx;
$tabbar-item-font-size: 24rpx;

/* 媒体查询断点 */
$breakpoint-xs: 480px;
$breakpoint-sm: 768px;
$breakpoint-md: 992px;
$breakpoint-lg: 1200px;