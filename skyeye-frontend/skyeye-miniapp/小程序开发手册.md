# SkyEye ERP 小程序开发文档

## 项目概述

基于现有 SkyEye ERP 系统，构建企业级小程序应用，采用 uni-app + Vue3 + TypeScript + Pinia 技术栈，实现移动端业务功能覆盖。

## 开发里程碑规划

### 🎯 里程碑1：项目初始化与基础框架搭建 (1-2周)
**目标**: 建立标准化的开发环境和基础架构

### 🎯 里程碑2：UI/UX设计与原型开发 (2-3周)  
**目标**: 完成核心页面设计和交互原型

### 🎯 里程碑3：核心功能开发 (3-4周)
**目标**: 实现登录、权限、基础业务功能

### 🎯 里程碑4：组件库适配与迁移 (2-3周)
**目标**: 将现有Sk组件库适配到小程序环境

### 🎯 里程碑5：业务功能深度开发 (4-6周)
**目标**: 实现完整的业务流程和数据交互

### 🎯 里程碑6：测试与优化 (2-3周)
**目标**: 完成功能测试、性能优化和发布准备

---

## 技术架构设计

### 核心技术栈
- **框架**: uni-app (基于 uniapp-vue3-ts-template)
- **前端**: Vue3 + TypeScript + Vite
- **状态管理**: Pinia + 持久化插件
- **样式方案**: UnoCSS + SCSS
- **UI组件**: 适配后的Sk组件库 + uni-ui
- **工程化**: ESLint + Prettier + husky + commitlint

### 项目结构
```
skyeye-miniapp/
├── src/
│   ├── api/              # API接口层
│   ├── components/       # 通用组件
│   ├── pages/           # 页面目录
│   ├── store/           # Pinia状态管理
│   ├── utils/           # 工具函数
│   ├── styles/          # 样式文件
│   └── types/           # TypeScript类型定义
├── static/              # 静态资源
├── uni_modules/         # uni-app插件
└── pages.json           # 页面配置
```

## 各里程碑详细任务清单

### 📋 里程碑1 任务清单：项目初始化与基础框架搭建

#### Phase 1.1: 环境准备
- [ ] 安装 HBuilderX 或配置 VS Code + uni-app 插件
- [ ] 配置 Node.js 开发环境 (v16+)
- [ ] 安装微信开发者工具和小程序调试环境

#### Phase 1.2: 项目初始化
- [ ] 基于 uniapp-vue3-ts-template 创建项目骨架
- [ ] 配置 TypeScript 编译选项和类型声明
- [ ] 集成 Pinia 状态管理和持久化插件
- [ ] 配置 UnoCSS 原子化样式框架

#### Phase 1.3: 工程化配置
- [ ] 配置 ESLint + Prettier 代码规范
- [ ] 设置 husky + lint-staged Git钩子
- [ ] 配置 commitlint 提交规范
- [ ] 建立项目目录结构和命名规范

#### Phase 1.4: 基础配置
- [ ] 配置 pages.json 页面路由
- [ ] 设置 manifest.json 应用配置
- [ ] 配置开发和生产环境变量
- [ ] 建立 API 请求封装和拦截器

### 📋 里程碑2 任务清单：UI/UX设计与原型开发

#### Phase 2.1: 设计规范制定
- [ ] 建立小程序设计系统和色彩规范
- [ ] 制定字体、间距、圆角等设计token
- [ ] 设计响应式布局方案
- [ ] 制定组件设计规范文档

#### Phase 2.2: 核心页面设计
- [ ] 设计登录/注册页面原型
- [ ] 设计首页Dashboard布局
- [ ] 设计主要业务模块导航结构
- [ ] 设计个人中心和设置页面

#### Phase 2.3: 交互原型开发
- [ ] 实现页面导航和路由跳转
- [ ] 开发底部TabBar导航组件
- [ ] 实现下拉刷新和上拉加载更多
- [ ] 开发Toast、Modal等基础交互组件

#### Phase 2.4: 响应式适配
- [ ] 适配不同屏幕尺寸的布局
- [ ] 处理安全区域和刘海屏适配
- [ ] 优化触摸交互体验
- [ ] 测试不同设备的兼容性

### 📋 里程碑3 任务清单：核心功能开发

#### Phase 3.1: 用户认证系统
- [ ] 开发微信登录集成
- [ ] 实现用户信息获取和存储
- [ ] 开发自动登录和token刷新机制
- [ ] 实现登出和账户切换功能

#### Phase 3.2: 权限管理系统
- [ ] 集成现有权限体系API
- [ ] 实现菜单权限控制
- [ ] 开发页面访问权限验证
- [ ] 实现按钮级权限控制

#### Phase 3.3: 基础业务功能
- [ ] 开发工作台和待办事项展示
- [ ] 实现消息通知和推送功能
- [ ] 开发文件预览和下载功能
- [ ] 实现基础的CRUD操作模板

#### Phase 3.4: 数据同步机制
- [ ] 建立离线数据缓存策略
- [ ] 实现数据同步和冲突处理
- [ ] 开发网络状态监听和处理
- [ ] 实现数据版本控制机制

### 📋 里程碑4 任务清单：组件库适配与迁移 ⚠️

> **重要提醒**: 此阶段涉及现有业务代码迁移，需要用户明确指令才能执行

#### Phase 4.1: 组件分析与规划
- [ ] 分析现有Sk组件库的依赖关系
- [ ] 评估哪些组件可以直接适配小程序
- [ ] 制定组件迁移优先级和计划
- [ ] 设计组件适配的技术方案

#### Phase 4.2: 基础组件适配
- [ ] 适配 SkButton、SkInput 等基础表单组件
- [ ] 迁移 SkTable、SkPagination 等数据展示组件
- [ ] 适配 SkModal、SkDrawer 等弹窗组件
- [ ] 迁移 SkUpload、SkImage 等媒体组件

#### Phase 4.3: 业务组件适配
- [ ] 适配 SkUserSelect、SkDepartmentSelect 等业务选择器
- [ ] 迁移 SkAuth 权限控制组件
- [ ] 适配 SkOrganization 组织架构组件
- [ ] 迁移其他核心业务组件

#### Phase 4.4: 组件测试与优化
- [ ] 编写组件单元测试用例
- [ ] 测试组件在小程序环境的兼容性
- [ ] 优化组件性能和用户体验
- [ ] 编写组件使用文档和示例

### 📋 里程碑5 任务清单：业务功能深度开发 ⚠️

> **重要提醒**: 此阶段涉及具体业务逻辑开发，需要用户明确业务需求

#### Phase 5.1: 核心业务模块开发
- [ ] 开发考勤管理移动端功能
- [ ] 实现审批流程移动端操作
- [ ] 开发客户关系管理(CRM)移动功能
- [ ] 实现库存管理移动端查询

#### Phase 5.2: 表单和数据处理
- [ ] 开发动态表单生成和验证
- [ ] 实现复杂数据的移动端展示
- [ ] 开发数据导入导出功能
- [ ] 实现数据统计和报表展示

#### Phase 5.3: 业务流程集成
- [ ] 集成工作流审批功能
- [ ] 实现消息推送和提醒机制
- [ ] 开发离线工作和数据同步
- [ ] 实现多租户数据隔离

#### Phase 5.4: 高级功能开发
- [ ] 集成地图定位和打卡功能
- [ ] 开发扫码和NFC功能集成
- [ ] 实现图片识别和OCR功能
- [ ] 开发语音和视频通话功能

### 📋 里程碑6 任务清单：测试与优化

#### Phase 6.1: 功能测试
- [ ] 编写端到端测试用例
- [ ] 执行功能回归测试
- [ ] 进行多设备兼容性测试
- [ ] 执行用户验收测试

#### Phase 6.2: 性能优化
- [ ] 分析和优化页面加载性能
- [ ] 优化图片和静态资源加载
- [ ] 实现代码分割和懒加载
- [ ] 优化网络请求和缓存策略

#### Phase 6.3: 安全与发布准备
- [ ] 进行安全漏洞扫描和修复
- [ ] 配置生产环境部署流程
- [ ] 准备小程序发布审核材料
- [ ] 编写用户使用手册和文档

#### Phase 6.4: 发布与维护
- [ ] 提交小程序审核和发布
- [ ] 建立监控和日志收集机制
- [ ] 制定版本更新和维护计划
- [ ] 建立用户反馈收集渠道

## 开发规范与最佳实践

### 代码规范
- **命名规范**: 采用 camelCase (变量/函数) 和 PascalCase (组件/类)
- **文件命名**: 页面文件采用 kebab-case，组件采用 PascalCase
- **目录结构**: 按功能模块划分，保持层次清晰
- **注释规范**: 使用 JSDoc 格式，关键逻辑必须添加注释

### 性能优化策略
- **图片优化**: 使用 WebP 格式，实现懒加载
- **代码分割**: 按页面和功能模块进行代码分割
- **缓存策略**: 合理使用本地缓存和HTTP缓存
- **网络优化**: 减少请求次数，使用请求合并

### 安全考虑
- **数据加密**: 敏感数据传输加密
- **权限验证**: 每个API请求都进行权限校验
- **输入验证**: 所有用户输入都进行严格验证
- **日志记录**: 记录关键操作日志，便于追踪

---

## 📝 AI开发助手TodoList

> **使用说明**: 以下TodoList专为AI开发助手设计，按优先级和依赖关系排序。每个阶段完成后需要用户确认才能进入下一阶段。

### 🚀 第一阶段：立即可执行任务

```typescript
// 立即执行优先级: HIGH
const Phase1TodoList = [
  {
    id: "setup-001",
    title: "创建项目基础结构",
    priority: "HIGH",
    dependencies: [],
    tasks: [
      "初始化uni-app项目 (基于uniapp-vue3-ts-template)",
      "配置TypeScript和Vite构建环境", 
      "集成Pinia状态管理",
      "配置UnoCSS样式框架",
      "建立项目目录结构"
    ]
  },
  {
    id: "setup-002", 
    title: "工程化配置",
    priority: "HIGH",
    dependencies: ["setup-001"],
    tasks: [
      "配置ESLint + Prettier代码规范",
      "设置husky + lint-staged",
      "配置commitlint提交规范",
      "建立API请求封装"
    ]
  },
  {
    id: "prototype-001",
    title: "基础原型开发", 
    priority: "MEDIUM",
    dependencies: ["setup-002"],
    tasks: [
      "创建基础页面结构(登录、首页、个人中心)",
      "实现底部TabBar导航",
      "开发基础Layout组件",
      "实现页面路由配置"
    ]
  }
]
```

### ⚠️ 第二阶段：需用户授权任务

```typescript
// 执行前需要明确用户授权: 涉及现有代码迁移
const Phase2TodoList = [
  {
    id: "migration-001",
    title: "Sk组件库适配分析",
    priority: "HIGH", 
    userApprovalRequired: true,
    riskLevel: "MEDIUM",
    description: "分析现有Sk组件库，制定小程序适配方案",
    tasks: [
      "分析src/components/目录下的Sk组件",
      "评估组件的小程序兼容性", 
      "制定组件适配优先级",
      "设计组件适配技术方案"
    ],
    userConfirmation: "是否同意分析和适配现有Sk组件库到小程序环境？"
  },
  {
    id: "migration-002", 
    title: "核心组件迁移",
    priority: "HIGH",
    userApprovalRequired: true,
    riskLevel: "HIGH",
    dependencies: ["migration-001"],
    tasks: [
      "适配SkButton、SkInput等基础组件",
      "迁移SkTable、SkModal等复杂组件", 
      "适配SkUserSelect等业务组件",
      "测试组件在小程序环境的功能"
    ],
    userConfirmation: "是否同意将现有Sk组件库迁移到小程序项目？这将涉及修改组件源码。"
  }
]
```

### 🔒 第三阶段：业务逻辑迁移任务

```typescript
// 执行前必须用户明确指令: 涉及业务逻辑
const Phase3TodoList = [
  {
    id: "business-001",
    title: "业务模块分析与规划",
    priority: "MEDIUM",
    userApprovalRequired: true, 
    riskLevel: "HIGH",
    description: "分析现有业务模块，制定小程序端实现方案",
    tasks: [
      "分析views/目录下的业务页面",
      "识别适合移动端的核心功能",
      "设计业务流程简化方案", 
      "制定API接口适配计划"
    ],
    userConfirmation: "是否同意分析现有业务逻辑并制定小程序实现方案？"
  },
  {
    id: "business-002",
    title: "核心业务功能开发", 
    priority: "MEDIUM",
    userApprovalRequired: true,
    riskLevel: "VERY_HIGH", 
    dependencies: ["business-001", "migration-002"],
    tasks: [
      "开发用户认证和权限管理",
      "实现核心业务流程(待具体指定)",
      "开发数据同步机制",
      "集成消息推送功能"
    ],
    userConfirmation: "请明确指定需要在小程序中实现哪些具体业务功能？"
  }
]
```

---

## ⚡ AI执行指引

### 执行原则
1. **渐进式开发**: 严格按照Phase顺序执行，不可跳跃
2. **用户授权**: Phase2和Phase3必须获得用户明确授权
3. **风险控制**: 高风险任务需要详细说明影响范围
4. **可回滚**: 每个阶段都要保证可以安全回滚

### 当前状态检查
```bash
# AI助手请先执行状态检查
- [ ] 用户是否已确认技术选型 (uniapp-vue3-ts-template)
- [ ] 开发环境是否已准备就绪
- [ ] 是否已获得现有代码库访问权限
- [ ] Phase1任务是否可以立即开始执行
```

### 执行命令模板
```bash
# 当用户准备好时，请使用以下命令启动
AI执行指令: "开始执行Phase1任务，创建小程序基础项目结构"

# 需要授权时的确认模板  
用户确认指令: "我确认授权执行Phase2任务，同意迁移Sk组件库到小程序环境"
```

## 版本历史

- v1.0.0 (2025-01-XX) - 初始版本，完成开发文档制定
- 待续...

---

**文档状态**: ✅ 已完成  
**最后更新**: 2025-01-XX  
**负责人**: AI开发助手  
**审核人**: 待定