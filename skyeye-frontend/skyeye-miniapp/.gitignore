.DS_Store
node_modules/
unpackage/
dist/
dist-ssr/

# local env files
# *.local
.env.local
.env.*.local

# Log files
# logs
# *.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Editor directories and files
.project
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw*
.hbuilderx
# 要提交 .env 来确保 jit v2 的开发 watch mode
!.env

# auto-generated

src/types/auto-imports.d.ts
src/types/components.d.ts
src/types/vitepress-auto-import.d.ts
src/types/vitepress-components.d.ts

# Other

src/ignore
.eslintcache
