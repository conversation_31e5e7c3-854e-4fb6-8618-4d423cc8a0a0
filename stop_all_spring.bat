@echo off
setlocal enabledelayedexpansion
chcp 936 >nul
echo ===================================
echo Spring���̼��͹����� (��ǿ��)
echo ===================================
echo.

:MENU
echo ��ѡ�����:
echo 1. ��鵱ǰ���е�SpringӦ��
echo 2. ��ֹ����SpringӦ�� 
echo 3. ��ָֹ���˿ڵ�Java����
echo 4. ��ʾ����Java��������
echo 5. �˳�
echo.
set /p "choice=������ѡ�� (1-5): "

if "%choice%"=="1" goto CHECK
if "%choice%"=="2" goto KILL_ALL
if "%choice%"=="3" goto KILL_PORT
if "%choice%"=="4" goto SHOW_DETAILS
if "%choice%"=="5" goto EXIT
echo ��Чѡ�������ѡ��
goto MENU

:CHECK
echo.
echo ���ڼ��Spring����...
echo ===================================

rem ������м����˿ڵ�Java����
set found=0
set processed_pids=

for /f "tokens=2,5" %%a in ('netstat -ano ^| findstr LISTENING') do (
    set port=%%a
    set pid=%%b
    
    rem ��ȡ�˿ںţ�ȥ��IP��ַ���֣�
    for /f "tokens=2 delims=:" %%p in ("!port!") do set port_num=%%p
    
    rem ������PID�Ƿ��Ѿ������
    echo !processed_pids! | findstr " !pid! " >nul
    if !errorlevel! neq 0 (
        for /f "tokens=1" %%j in ('tasklist /fi "pid eq !pid!" /fo table /nh 2^>nul') do (
            if "%%j"=="java.exe" (
                call :CHECK_SPRINGBOOT !pid! !port_num!
                set processed_pids=!processed_pids! !pid! 
            )
        )
    )
)

if !found! equ 0 (
    echo δ������ȷ��SpringӦ�ý���
) else (
    echo.
    echo ������ !found! �����ܵ�Spring����
)

echo.
echo �����ɣ�
echo.
goto MENU

:CHECK_PORT
set port=%1
set processed_pids=

for /f "tokens=5" %%i in ('netstat -ano ^| findstr :%port% ^| findstr LISTENING') do (
    rem ������PID�Ƿ��Ѿ������
    echo !processed_pids! | findstr " %%i " >nul
    if !errorlevel! neq 0 (
        for /f "tokens=1" %%j in ('tasklist /fi "pid eq %%i" /fo table /nh 2^>nul') do (
            if "%%j"=="java.exe" (
                call :CHECK_SPRINGBOOT %%i %port%
                set processed_pids=!processed_pids! %%i 
            )
        )
    )
)
goto :eof

:CHECK_SPRINGBOOT
set pid=%1
set port=%2
set is_spring=0

rem �������������Ƿ����Spring����
for /f "delims=" %%a in ('wmic process where "processid=%pid%" get commandline /value ^| findstr "CommandLine" 2^>nul') do (
    set cmdline=%%a
    set cmdline=!cmdline:~12!
    
    rem ���Spring��������ؼ���
    echo !cmdline! | findstr /i "spring-boot" >nul && set is_spring=1
    echo !cmdline! | findstr /i "SpringBootApplication" >nul && set is_spring=1
    echo !cmdline! | findstr /i "org.springframework" >nul && set is_spring=1
    echo !cmdline! | findstr /i "spring-boot-starter" >nul && set is_spring=1
    echo !cmdline! | findstr /i "spring-web" >nul && set is_spring=1
    echo !cmdline! | findstr /i "spring-core" >nul && set is_spring=1
    echo !cmdline! | findstr /i "spring-context" >nul && set is_spring=1
    echo !cmdline! | findstr /i "springframework" >nul && set is_spring=1
    echo !cmdline! | findstr /i "jar.*\.jar" | findstr /i "app\|service\|server\|application" >nul && (
        rem ��һ������Ƿ����Spring�������
        echo !cmdline! | findstr /i "spring" >nul && set is_spring=1
    )
)

rem �������Ƿ����HTTP�˿ڣ���Ϊ�����жϣ�
if "%port%" neq "" (
    if %port% geq 8000 if %port% leq 9000 (
        rem ����8000-9000�˿ڣ����û����ȷSpring���������и����ɵ��ж�
        if !is_spring! equ 0 (
            echo !cmdline! | findstr /i "jar" >nul && set is_spring=1
        )
    )
)

if !is_spring! equ 1 (
    if "%port%" neq "" (
        echo [Springȷ��] �˿�:%port% PID:%pid%
    ) else (
        echo [Springȷ��] PID:%pid%
    )
    set /a found+=1
) else (
    if "%port%" neq "" (
        echo [����Java����] �˿�:%port% PID:%pid% (��SpringӦ��)
    )
)
goto :eof

:SHOW_DETAILS
echo.
echo Java������ϸ��Ϣ:
echo ===================================
for /f "tokens=2" %%i in ('tasklist /fi "imagename eq java.exe" /fo table /nh 2^>nul') do (
    echo PID: %%i
    for /f "delims=" %%a in ('wmic process where "processid=%%i" get commandline /value ^| findstr "CommandLine" 2^>nul') do (
        set cmdline=%%a
        set cmdline=!cmdline:~12!
        echo ������: !cmdline!
    )
    echo -----------------------------------
)
echo.
goto MENU

:KILL_ALL
echo.
echo ����: ������ֹ����ȷ�ϵ�SpringӦ�ã�
echo ֻ����ֹ��ȷʶ��ΪSpring��Java����
set /p "confirm=ȷ�ϼ���? (y/n): "

if /i not "!confirm!"=="y" (
    echo ������ȡ��
    goto MENU
)

echo.
echo ������ֹSpring����...
set killed=0
set processed_pids=

for /f "tokens=2,5" %%a in ('netstat -ano ^| findstr LISTENING') do (
    set port=%%a
    set pid=%%b
    
    rem ��ȡ�˿ںţ�ȥ��IP��ַ���֣�
    for /f "tokens=2 delims=:" %%p in ("!port!") do set port_num=%%p
    
    rem ������PID�Ƿ��Ѿ������
    echo !processed_pids! | findstr " !pid! " >nul
    if !errorlevel! neq 0 (
        for /f "tokens=1" %%j in ('tasklist /fi "pid eq !pid!" /fo table /nh 2^>nul') do (
            if "%%j"=="java.exe" (
                call :IS_SPRING_PROCESS !pid! !port_num!
                if !is_spring_result! equ 1 (
                    echo ������ֹ�˿�!port_num!��Spring���� PID:!pid!
                    taskkill /pid !pid! /f >nul 2>&1
                    if !errorlevel! equ 0 (
                        echo   - �ɹ���ֹ PID:!pid!
                        set /a killed+=1
                    ) else (
                        echo   - ��ֹʧ�� PID:!pid!
                    )
                )
                set processed_pids=!processed_pids! !pid! 
            )
        )
    )
)

echo.
echo ������ɣ�����ֹ !killed! ��Spring����
echo.
goto MENU

:IS_SPRING_PROCESS
set pid=%1
set port=%2
set is_spring_result=0

rem �������������Ƿ����Spring����
for /f "delims=" %%a in ('wmic process where "processid=%pid%" get commandline /value ^| findstr "CommandLine" 2^>nul') do (
    set cmdline=%%a
    set cmdline=!cmdline:~12!
    
    rem ���Spring��������ؼ���
    echo !cmdline! | findstr /i "spring-boot" >nul && set is_spring_result=1
    echo !cmdline! | findstr /i "SpringBootApplication" >nul && set is_spring_result=1
    echo !cmdline! | findstr /i "org.springframework" >nul && set is_spring_result=1
    echo !cmdline! | findstr /i "spring-boot-starter" >nul && set is_spring_result=1
    echo !cmdline! | findstr /i "spring-web" >nul && set is_spring_result=1
    echo !cmdline! | findstr /i "spring-core" >nul && set is_spring_result=1
    echo !cmdline! | findstr /i "spring-context" >nul && set is_spring_result=1
    echo !cmdline! | findstr /i "springframework" >nul && set is_spring_result=1
    echo !cmdline! | findstr /i "jar.*\.jar" | findstr /i "app\|service\|server\|application" >nul && (
        echo !cmdline! | findstr /i "spring" >nul && set is_spring_result=1
    )
)

rem �������Ƿ����HTTP�˿ڣ���Ϊ�����жϣ�
if "%port%" neq "" (
    if %port% geq 8000 if %port% leq 9000 (
        if !is_spring_result! equ 0 (
            echo !cmdline! | findstr /i "jar" >nul && set is_spring_result=1
        )
    )
)

goto :eof

:KILL_PORT
echo.
set /p "port=������Ҫ��ֹ�Ķ˿ں�: "

echo ���ڲ��Ҷ˿�%port%��Java����...
set found=0

for /f "tokens=5" %%i in ('netstat -ano ^| findstr :%port% ^| findstr LISTENING') do (
    for /f "tokens=1" %%j in ('tasklist /fi "pid eq %%i" /fo table /nh 2^>nul') do (
        if "%%j"=="java.exe" (
            echo �ҵ��˿�%port%��Java���� PID:%%i
            
            rem ��ʾ��������
            for /f "delims=" %%a in ('wmic process where "processid=%%i" get commandline /value ^| findstr "CommandLine" 2^>nul') do (
                set cmdline=%%a
                set cmdline=!cmdline:~12!
                echo ������: !cmdline!
            )
            
            set /p "confirm=ȷ����ֹ�˽���? (y/n): "
            if /i "!confirm!"=="y" (
                taskkill /pid %%i /f >nul 2>&1
                if !errorlevel! equ 0 (
                    echo �ɹ���ֹ PID:%%i
                ) else (
                    echo ��ֹʧ�� PID:%%i
                )
            )
            set found=1
        )
    )
)

if !found! equ 0 (
    echo �˿�%port%û���ҵ�Java����
)

echo.
goto MENU

:EXIT
echo �˳�����
exit /b 0