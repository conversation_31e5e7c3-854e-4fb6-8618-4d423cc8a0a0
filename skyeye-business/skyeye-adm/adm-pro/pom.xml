<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>skyeye-adm</artifactId>
        <groupId>com.skyeye</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>adm-pro</artifactId>

    <dependencies>

        <dependency>
            <groupId>com.skyeye</groupId>
            <artifactId>adm-survey</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.skyeye</groupId>
            <artifactId>adm-ai</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.skyeye</groupId>
            <artifactId>adm-ehr</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.deepoove</groupId>
            <artifactId>poi-tl</artifactId>
            <version>1.9.1</version>
        </dependency>

        <dependency>
            <groupId>io.github.draco1023</groupId>
            <artifactId>poi-tl-ext</artifactId>
            <version>0.3.3</version>
            <exclusions>
                <exclusion>
                    <groupId>com.deepoove</groupId>
                    <artifactId>poi-tl</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-io</groupId>
                    <artifactId>commons-io</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 电子书插件 -->
        <dependency>
            <groupId>epublib-core</groupId>
            <artifactId>latest</artifactId>
            <version>1.3.1</version>
            <scope>system</scope>
            <systemPath>${basedir}/lib/epublib-core-latest.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-solr</artifactId>
            <version>2.4.9</version>
        </dependency>

    </dependencies>

</project>