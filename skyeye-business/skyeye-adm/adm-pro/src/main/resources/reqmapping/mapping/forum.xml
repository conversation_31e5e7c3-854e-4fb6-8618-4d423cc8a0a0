<?xml version="1.0" encoding="UTF-8"?>
<controller>
	<!-- 
		- allUse  是否需要登录才能使用   1是   0否  2需要登陆才能访问，但无需授权    默认为否
	 -->
	
	<!-- 论坛标签管理开始 -->
    <url id="forumtag001" path="/post/ForumTagController/queryForumTagList" val="获取论坛标签列表" allUse="1">
        <property id="limit" name="limit" ref="required,num" var="分页参数,每页多少条数据" />
        <property id="page" name="page" ref="required,num" var="分页参数,第几页"/>
        <property id="tagName" name="tagName" ref="" var="论坛标签名称"/>
    </url>
    <url id="forumtag002" path="/post/ForumTagController/insertForumTagMation" val="新增论坛标签" allUse="1">
        <property id="tagName" name="tagName" ref="required" var="论坛标签名称"/>
    </url>
    <url id="forumtag003" path="/post/ForumTagController/deleteForumTagById" val="删除论坛标签" allUse="1">
        <property id="rowId" name="id" ref="required" var="论坛标签名称id"/>
    </url>
    <url id="forumtag004" path="/post/ForumTagController/updateUpForumTagById" val="上线论坛标签" allUse="1">
        <property id="rowId" name="id" ref="required" var="论坛标签名称id"/>
    </url>
    <url id="forumtag005" path="/post/ForumTagController/updateDownForumTagById" val="下线论坛标签" allUse="1">
        <property id="rowId" name="id" ref="required" var="论坛标签名称id"/>
    </url>
    <url id="forumtag006" path="/post/ForumTagController/selectForumTagById" val="获取选中的论坛标签信息" allUse="2">
        <property id="rowId" name="id" ref="required" var="论坛标签名称id"/>
    </url>
    <url id="forumtag007" path="/post/ForumTagController/editForumTagMationById" val="编辑论坛标签信息" allUse="1">
        <property id="rowId" name="id" ref="required" var="论坛标签名称id"/>
        <property id="tagName" name="tagName" ref="required" var="论坛标签名称"/>
    </url>
    <url id="forumtag008" path="/post/ForumTagController/editForumTagMationOrderNumUpById" val="论坛标签上移" allUse="1">
        <property id="rowId" name="id" ref="required" var="分类id" />
    </url>
    <url id="forumtag009" path="/post/ForumTagController/editForumTagMationOrderNumDownById" val="论坛标签下移" allUse="1">
        <property id="rowId" name="id" ref="required" var="分类id" />
    </url>
    <url id="forumtag010" path="/post/ForumTagController/queryForumTagUpStateList" val="获取已经上线的论坛标签列表" allUse="2">
    </url>
    <!-- 论坛标签管理结束 -->
	
    <!-- 论坛敏感词管理开始 -->
<!--    <url id="sensitiveword001" path="/post/ForumSensitiveWordsController/queryForumSensitiveWordsList" val="获取论坛敏感词列表" allUse="1">-->
<!--        <property id="limit" name="limit" ref="required,num" var="分页参数,每页多少条数据" />-->
<!--        <property id="page" name="page" ref="required,num" var="分页参数,第几页"/>-->
<!--        <property id="sensitiveWord" name="sensitiveWord" ref="" var="论坛敏感词名称"/>-->
<!--    </url>-->
<!--    <url id="sensitiveword002" path="/post/ForumSensitiveWordsController/insertForumSensitiveWordsMation" val="新增论坛敏感词" allUse="1">-->
<!--        <property id="sensitiveWord" name="sensitiveWord" ref="required" var="论坛敏感词名称"/>-->
<!--    </url>-->
<!--    <url id="sensitiveword003" path="/post/ForumSensitiveWordsController/deleteForumSensitiveWordsById" val="删除论坛敏感词" allUse="1">-->
<!--        <property id="rowId" name="id" ref="required" var="论坛敏感词名称id"/>-->
<!--    </url>-->
<!--    <url id="sensitiveword004" path="/post/ForumSensitiveWordsController/selectForumSensitiveWordsById" val="获取选中的论坛敏感词信息" allUse="2">-->
<!--        <property id="rowId" name="id" ref="required" var="论坛敏感词名称id"/>-->
<!--    </url>-->
    <url id="sensitiveword005" path="/post/ForumSensitiveWordsController/editForumSensitiveWordsMationById" val="编辑论坛敏感词信息" allUse="1">
        <property id="rowId" name="id" ref="required" var="论坛敏感词名称id"/>
        <property id="sensitiveWord" name="sensitiveWord" ref="required" var="论坛敏感词名称"/>
    </url>
    <!-- 论坛敏感词管理结束 -->
    
    <!-- 论坛帖子管理开始 -->
    <url id="forumcontent001" path="/post/ForumContentController/queryMyForumContentList" val="获取我的帖子列表" allUse="2">
        <property id="limit" name="limit" ref="required,num" var="分页参数,每页多少条数据" />
        <property id="page" name="page" ref="required,num" var="分页参数,第几页"/>
    </url>
<!--    <url id="forumcontent002" path="/post/ForumContentController/insertForumContentMation" val="新增我的帖子" allUse="2">-->
<!--        <property id="title" name="title" ref="required" var="标题"/>-->
<!--        <property id="forumType" name="forumType" ref="required" var="类型"/>-->
<!--        <property id="tagId" name="tagId" ref="required" var="标签"/>-->
<!--        <property id="anonymous" name="anonymous" ref="required" var="是否匿名"/>-->
<!--        <property id="content" name="content" ref="required" var="内容"/>-->
<!--        <property id="textConent" name="textConent" ref="required" var="纯文本内容"/>-->
<!--    </url>-->
    <url id="forumcontent003" path="/post/ForumContentController/deleteForumContentById" val="删除帖子" allUse="2">
        <property id="rowId" name="id" ref="required" var="帖子id"/>
    </url>
    <url id="forumcontent004" path="/post/ForumContentController/queryForumContentMationById" val="查询帖子信息用以编辑" allUse="2">
        <property id="rowId" name="id" ref="required" var="帖子id" />
    </url>
    <url id="forumcontent005" path="/post/ForumContentController/editForumContentMationById" val="编辑帖子信息" allUse="2">
        <property id="rowId" name="id" ref="required" var="帖子id"/>
        <property id="title" name="title" ref="required" var="标题"/>
        <property id="forumType" name="forumType" ref="required" var="类型"/>
        <property id="tagId" name="tagId" ref="required" var="标签"/>
        <property id="anonymous" name="anonymous" ref="required" var="是否匿名"/>
        <property id="content" name="content" ref="required" var="内容"/>
        <property id="textConent" name="textConent" ref="required" var="纯文本内容"/>
    </url>
    <url id="forumcontent006" path="/post/ForumContentController/queryForumContentMationToDetails" val="帖子详情" allUse="2">
        <property id="rowId" name="id" ref="required" var="帖子id" />
    </url>
    <url id="forumcontent007" path="/post/ForumContentController/queryNewForumContentList" val="获取最新帖子" allUse="2">
    </url>
    <url id="forumcontent008" path="/post/ForumContentController/insertForumCommentMation" val="新增帖子评论" allUse="2">
        <property id="forumId" name="forumId" ref="required" var="帖子id" />
        <property id="content" name="content" ref="required" var="评论内容" />
    </url>
    <url id="forumcontent009" path="/post/ForumContentController/queryForumCommentList" val="获取帖子评论信息" allUse="2">
        <property id="forumId" name="forumId" ref="required" var="帖子id" />
    </url>
    <url id="forumcontent010" path="/post/ForumContentController/insertForumReplyMation" val="新增帖子评论回复" allUse="2">
        <property id="forumId" name="forumId" ref="required" var="帖子id" />
        <property id="content" name="content" ref="required" var="评论内容" />
        <property id="belongCommentId" name="belongCommentId" ref="required" var="评论id" />
        <property id="replyId" name="replyId" ref="required" var="回复人id" />
    </url>
    <url id="forumcontent011" path="/post/ForumContentController/queryForumReplyList" val="获取帖子评论回复信息" allUse="2">
        <property id="forumId" name="forumId" ref="required" var="帖子id" />
    </url>
    <url id="forumcontent012" path="/post/ForumContentController/queryForumMyBrowerList" val="获取我的浏览信息" allUse="2">
        <property id="limit" name="limit" ref="required,num" var="分页参数,每页多少条数据" />
        <property id="page" name="page" ref="required,num" var="分页参数,第几页"/>
    </url>
    <url id="forumcontent013" path="/post/ForumContentController/queryNewCommentList" val="获取最新评论" allUse="2">
    </url>
    <url id="forumcontent014" path="/post/ForumContentController/queryForumListByTagId" val="根据标签id获取帖子列表" allUse="2">
        <property id="limit" name="limit" ref="required,num" var="分页参数,每页多少条数据" />
        <property id="page" name="page" ref="required,num" var="分页参数,第几页"/>
        <property id="tagId" name="tagId" ref="" var="标签id" />
    </url>
    <url id="forumcontent015" path="/post/ForumContentController/queryHotTagList" val="获取热门标签" allUse="2">
    </url>
    <url id="forumcontent016" path="/post/ForumContentController/queryActiveUsersList" val="获取活跃用户" allUse="2">
    </url>
    <url id="forumcontent017" path="/post/ForumContentController/queryHotForumList" val="获取热门贴" allUse="2">
    </url>
    <url id="forumcontent018" path="/post/ForumContentController/querySearchForumList" val="获取用户搜索的帖子" allUse="2">
        <property id="searchValue" name="searchValue" ref="" var="搜索内容" />
    </url>
    <url id="forumcontent019" path="/post/ForumContentController/querySolrSynchronousTime" val="获取solr上次同步数据的时间" allUse="2">
    </url>
    <url id="forumcontent020" path="/post/ForumContentController/updateSolrSynchronousData" val="solr同步数据" allUse="2">
    </url>
    <url id="forumcontent021" path="/post/ForumContentController/queryMyCommentList" val="获取我的评论列表" allUse="2">
        <property id="limit" name="limit" ref="required,num" var="分页参数,每页多少条数据" />
        <property id="page" name="page" ref="required,num" var="分页参数,第几页"/>
    </url>
    <url id="forumcontent022" path="/post/ForumContentController/deleteCommentById" val="根据评论id删除评论" allUse="2">
        <property id="rowId" name="id" ref="required" var="评论id" />
    </url>
    <url id="forumcontent023" path="/post/ForumContentController/queryMyNoticeList" val="获取我的通知列表" allUse="2">
        <property id="limit" name="limit" ref="required,num" var="分页参数,每页多少条数据" />
        <property id="page" name="page" ref="required,num" var="分页参数,第几页"/>
    </url>
    <url id="forumcontent024" path="/post/ForumContentController/deleteNoticeById" val="根据通知id删除通知" allUse="2">
        <property id="rowId" name="id" ref="required" var="通知id" />
    </url>
    <!-- 论坛帖子管理开始 -->
    
<!--    &lt;!&ndash; 论坛帖子举报管理开始 &ndash;&gt;-->
<!--    <url id="forumreport001" path="/post/ForumReportController/insertForumReportMation" val="新增论坛举报信息" allUse="2">-->
<!--        <property id="forumId" name="forumId" ref="required" var="帖子id" />-->
<!--        <property id="reportType" name="reportType" ref="required" var="举报类型" />-->
<!--        <property id="reportContent" name="reportContent" ref="" var="举报内容" />-->
<!--        <property id="reportDesc" name="reportDesc" ref="" var="备注" />-->
<!--    </url>-->
<!--    <url id="forumreport002" path="/post/ForumReportController/queryReportNoCheckList" val="获取论坛举报未审核列表" allUse="1">-->
<!--        <property id="limit" name="limit" ref="required,num" var="分页参数,每页多少条数据" />-->
<!--        <property id="page" name="page" ref="required,num" var="分页参数,第几页"/>-->
<!--        <property id="reportTypeId" name="reportTypeId" ref="" var="举报类型" />-->
<!--        <property id="title" name="title" ref="" var="帖子标题" />-->
<!--        <property id="reportstartTime" name="reportstartTime" ref="" var="举报开始时间" />-->
<!--        <property id="reportendTime" name="reportendTime" ref="" var="举报结束时间" />-->
<!--    </url>-->
<!--    <url id="forumreport003" path="/post/ForumReportController/editReportCheckMationById" val="举报信息审核" allUse="1">-->
<!--        <property id="rowId" name="id" ref="required" var="举报id" />-->
<!--        <property id="examineState" name="examineState" ref="required" var="审核结果" />-->
<!--        <property id="examineNopassReason" name="examineNopassReason" ref="" var="审核不通过原因" />-->
<!--    </url>-->
<!--    <url id="forumreport004" path="/post/ForumReportController/queryReportCheckedList" val="获取论坛举报已审核列表" allUse="1">-->
<!--        <property id="limit" name="limit" ref="required,num" var="分页参数,每页多少条数据" />-->
<!--        <property id="page" name="page" ref="required,num" var="分页参数,第几页"/>-->
<!--        <property id="reportTypeId" name="reportTypeId" ref="" var="举报类型" />-->
<!--        <property id="title" name="title" ref="" var="帖子标题" />-->
<!--        <property id="reportstartTime" name="reportstartTime" ref="" var="举报开始时间" />-->
<!--        <property id="reportendTime" name="reportendTime" ref="" var="举报结束时间" />-->
<!--        <property id="examinestartTime" name="examinestartTime" ref="" var="审核开始时间" />-->
<!--        <property id="examineendTime" name="examineendTime" ref="" var="审核结束时间" />-->
<!--    </url>-->
<!--    <url id="forumreport005" path="/post/ForumReportController/queryForumReportMationToDetails" val="举报详情" allUse="2">-->
<!--        <property id="rowId" name="id" ref="required" var="举报id" />-->
<!--    </url>-->
    <!-- 论坛帖子举报管理开始 -->
	
</controller>