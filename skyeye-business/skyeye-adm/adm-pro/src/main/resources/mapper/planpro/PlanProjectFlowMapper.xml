<?xml version="1.0" encoding="UTF-8" ?>  
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.skyeye.eve.businessflow.dao.PlanProjectFlowDao">
	
	<select id="queryPlanProjectFlowList" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT
			a.id,
			a.title title,
			a.p_id pId,
			a.`type`,
			CASE a.type WHEN 1 THEN 1 WHEN 2 THEN 0 ELSE '' END isParent,
		    CASE a.type WHEN 1 THEN '' WHEN 2 THEN '/assets/images/note-folder.png' ELSE '' END icon,
			a.is_share isShare
		FROM
			plan_project_flow a
		WHERE a.project_id = #{projectId}
			ORDER BY a.create_time ASC
	</select>
	
	<select id="queryPlanProjectFlowMationByName" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT
			a.id
		FROM
			plan_project_flow a
		WHERE a.title = #{title}
			AND a.project_id = #{projectId}
	</select>
	
	<insert id="insertPlanProjectFlowMation" parameterType="java.util.Map">
	     INSERT into plan_project_flow
	     (id, project_id, title, p_id, type, json_content, is_share, create_id, create_time)
	     VALUES
	     (#{id}, #{projectId}, #{title}, #{pId}, #{type}, #{jsonContent}, #{isShare}, #{createId}, #{createTime})
	</insert>
	
	<select id="queryChildNumMationById" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT 
			COUNT(*) childNum
		FROM 
			plan_project_flow a 
		WHERE a.p_id = #{id}
	</select>
	
	<delete id="deletePlanProjectFlowMationById" parameterType="java.util.Map">
		DELETE
		FROM
			plan_project_flow
		WHERE
			id = #{id}
	</delete>
	
	<select id="queryPlanProjectFlowMationToEditById" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT
			a.id,
			a.title title,
			a.is_share isShare
		FROM
			plan_project_flow a
		WHERE a.id = #{id}
	</select>
	
	<select id="queryPlanProjectFlowMationByNameAndId" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT
			a.id
		FROM
			plan_project_flow a
		WHERE a.id != #{id}
			AND a.project_id = #{projectId}
			AND a.title = #{title}
	</select>
	
	<update id="editPlanProjectFlowMationById" parameterType="java.util.Map">
		UPDATE plan_project_flow
		<set>
			<if test="title != '' and title != null">
				title = #{title},
			</if>
			<if test="isShare != '' and isShare != null">
				is_share = #{isShare},
			</if>
			<if test="jsonContent != '' and jsonContent != null">
				json_content = #{jsonContent},
			</if>
		</set>
		WHERE id = #{id}
	</update>
	
	<select id="queryPlanProjectFlowJsonContentMationById" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT
			a.id,
			a.json_content jsonContent
		FROM
			plan_project_flow a
		WHERE a.id = #{id}
	</select>
	
	<update id="editPlanProjectFlowJsonContentMationById" parameterType="java.util.Map">
		UPDATE plan_project_flow
		<set>
			<if test="jsonContent != '' and jsonContent != null">
				json_content = #{jsonContent},
			</if>
		</set>
		WHERE id = #{id}
	</update>
	
</mapper>