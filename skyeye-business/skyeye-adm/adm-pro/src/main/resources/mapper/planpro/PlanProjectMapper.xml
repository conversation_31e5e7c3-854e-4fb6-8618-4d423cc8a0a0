<?xml version="1.0" encoding="UTF-8" ?>  
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.skyeye.eve.businessflow.dao.PlanProjectDao">
	
	<select id="queryPlanProjectList" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT
			a.id,
			a.project_name projectName,
			a.project_desc projectDesc,
			a.is_share isShare,
			(SELECT COUNT(*) FROM plan_project_flow b WHERE b.project_id = a.id) childsNum,
			(SELECT COUNT(*) FROM plan_project_flow b WHERE b.project_id = a.id AND b.type = '1') childsTypeOneNum,
			(SELECT COUNT(*) FROM plan_project_flow b WHERE b.project_id = a.id AND b.type = '2') childsTypeTwoNum,
			a.create_id createId,
			CONVERT(a.create_time, char) createTime,
			a.last_update_id lastUpdateId,
			CONVERT(a.last_update_time, char) lastUpdateTime
		FROM
			plan_project a
		<where>
			<if test="projectName != '' and projectName != null">
				AND a.project_name LIKE '%${projectName}%'
			</if>
		</where>
		ORDER BY a.create_time DESC
	</select>
	
	<select id="judgePlanProjectName" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT
			a.id
		FROM
			plan_project a
		WHERE a.project_name = #{projectName}
		<if test="id != '' and id != null">
			AND a.id != #{id}
		</if>
	</select>
	
	<insert id="insertPlanProjectMation" parameterType="java.util.Map">
	     INSERT into plan_project
	     (id, project_name, project_desc, is_share, create_id, create_time, last_update_id, last_update_time)
	     VALUES
	     (#{id}, #{projectName}, #{projectDesc}, #{isShare}, #{createId}, #{createTime}, #{createId}, #{createTime})
	</insert>
	
	<delete id="deletePlanProjectMationById" parameterType="java.util.Map">
		DELETE
		FROM
			plan_project
		WHERE
			id = #{id}
	</delete>
	
	<select id="queryPlanProjectMationToEditById" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT
			a.id,
			a.project_name projectName,
			a.project_desc projectDesc,
			a.is_share isShare
		FROM
			plan_project a
		WHERE a.id = #{id}
	</select>
	
	<update id="editPlanProjectMationById" parameterType="java.util.Map">
		UPDATE plan_project
		<set>
			project_name = #{projectName},
			project_desc = #{projectDesc},
			<if test="isShare != '' and isShare != null">
				is_share = #{isShare},
			</if>
			last_update_id = #{lastUpdateId},
		    last_update_time = #{lastUpdateTime}
		</set>
		WHERE id = #{id}
	</update>
	
</mapper>