<?xml version="1.0" encoding="UTF-8" ?>  
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.skyeye.eve.articles.dao.ArticlesDao">

    <select id="queryArticlesList" resultType="java.util.Map">
        SELECT
            a.id,
            a.`name`,
            a.articles_num articlesNum,
            a.type_id typeId,
            a.specifications,
            a.initial_num initialNum,
            a.residual_num residualNum,
            a.create_id createId,
            CONVERT(a.create_time, char) createTime,
            a.last_update_id lastUpdateId,
            CONVERT(a.last_update_time, char) lastUpdateTime
        FROM
            asset_articles a
        <where>
            <if test="sqlExtract != '' and sqlExtract != null">
                ${sqlExtract}
            </if>
            <if test="keyword != null and keyword != ''">
                AND a.`name` like '%${keyword}%'
            </if>
        </where>
    </select>
    
</mapper>