<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.skyeye.eve.articles.dao.ArticlesUseDao">

    <select id="queryMyUseArticlesMation" resultType="java.util.Map">
        SELECT
            a.id,
            a.title,
            a.odd_number oddNumber,
            IFNULL(a.process_instance_id, '') processInstanceId,
            a.state,
            a.create_id createId,
            CONVERT(a.create_time, char) createTime,
            a.last_update_id lastUpdateId,
            CONVERT(a.last_update_time, char) lastUpdateTime
        FROM
            asset_articles_use a
        <where>
            a.create_id = #{createId}
            <if test="sqlExtract != '' and sqlExtract != null">
                ${sqlExtract}
            </if>
            <if test="keyword != null and keyword != ''">
                AND (a.odd_number like '%${keyword}%' OR a.title like '%${keyword}%')
            </if>
        </where>
        ORDER BY a.create_time DESC
    </select>
    <select id="queryMyArticlesList" resultType="java.util.Map">
        SELECT
            e.id,
            a.`name`,
            a.articles_num articlesNum,
            a.type_id typeId,
            a.specifications,
            d.title title,
            d.odd_number oddNumber,
            CONVERT(d.create_time, char) createTime,
            e.apply_use_num applyUseNum
        FROM
            asset_articles a,
            asset_articles_use d,
            asset_articles_use_goods e
        <where>
            d.create_id = #{createId}
            AND d.id = e.parent_id
            AND e.article_id = a.id
            <if test="sqlExtract != '' and sqlExtract != null">
                ${sqlExtract}
            </if>
            <if test="keyword != null and keyword != ''">
                AND (a.`name` like '%${keyword}%' OR d.odd_number like '%${keyword}%')
            </if>
        </where>
        ORDER BY d.create_time DESC
    </select>

</mapper>