<?xml version="1.0" encoding="UTF-8" ?>  
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.skyeye.eve.lightapp.dao.LightAppTypeDao">

	<select id="queryLightAppTypeList" resultType="java.util.Map">
		SELECT
			a.id,
			a.`name`,
			a.enabled,
			a.order_by orderBy,
			a.icon,
			a.icon_bg iconBg,
			a.icon_color iconColor,
			a.icon_pic iconPic,
			a.icon_type iconType,
			a.create_id createId,
			CONVERT(a.create_time, char) createTime,
			a.last_update_id lastUpdateId,
			CONVERT(a.last_update_time, char) lastUpdateTime
		FROM
			light_app_type a
		<where>
			<if test="sqlExtract != '' and sqlExtract != null">
				${sqlExtract}
			</if>
			<if test="keyword != null and keyword != ''">
				AND a.`name` like '%${keyword}%'
			</if>
		</where>
		ORDER BY a.order_by ASC
	</select>
	
</mapper>