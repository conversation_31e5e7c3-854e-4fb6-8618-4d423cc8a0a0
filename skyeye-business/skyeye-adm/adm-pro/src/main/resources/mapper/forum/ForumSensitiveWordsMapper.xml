<?xml version="1.0" encoding="UTF-8" ?>  
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.skyeye.eve.forum.dao.ForumSensitiveWordsDao">

	<select id="queryForumSensitiveWordsList" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT
			a.id,
			a.sensitive_word sensitiveWord,
			b.user_name createName,
			CONVERT(a.create_time, char) createTime
		FROM
			forum_sensitive_words a,		
			sys_eve_user_staff b
		WHERE 
			a.create_id = b.user_id
			<if test="sensitiveWord != '' and sensitiveWord != null">
				AND a.sensitive_word LIKE '%${sensitiveWord}%'
			</if>
	</select>
	
	<select id="queryForumSensitiveWordsMationByName" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT
			a.id
		FROM
			forum_sensitive_words a
		WHERE a.sensitive_word = #{sensitiveWord}
		<if test="id != '' and id != null">
			AND a.id != #{id}
		</if>
	</select>
	
	<insert id="insertForumSensitiveWordsMation" parameterType="java.util.Map">
	    INSERT into forum_sensitive_words 
	    (id, sensitive_word, create_id, create_time)
	    VALUES(#{id}, #{sensitiveWord}, #{createId}, #{createTime})
	</insert>

	<delete id="deleteForumSensitiveWordsById" parameterType="java.util.Map">
		DELETE
		FROM
			forum_sensitive_words
		WHERE
			id = #{id}
	</delete>

	<select id="selectForumSensitiveWordsById" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT
			a.id,
			a.sensitive_word sensitiveWord
		FROM
			forum_sensitive_words a	
		WHERE 
			a.id = #{id}
	</select>
	
	<update id="editForumSensitiveWordsMationById" parameterType="java.util.Map">
		UPDATE forum_sensitive_words
		<set>
			sensitive_word = #{sensitiveWord}
		</set>
		WHERE id = #{id}
	</update>
	
	<select id="queryForumSensitiveWordsListAll" parameterType="java.util.Map" resultType="java.util.Map">
        SELECT
            a.id,
            a.sensitive_word sensitiveWord
        FROM
            forum_sensitive_words a
    </select>

</mapper>