<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.skyeye.eve.email.dao.EmailDao">

    <select id="queryEmailListByEmailId" resultType="java.util.Map">
        SELECT
            a.id,
            a.title,
            a.from_people fromPeople,
            DATE_FORMAT(a.send_date, '%Y-%m-%d') sendDay,
            DATE_FORMAT(a.send_date, '%H:%i') sendTime,
            CASE a.is_contain_attach WHEN '1' THEN 'block' WHEN '2' THEN 'none' ELSE 'none' END isContainAttach,
            CASE a.is_new WHEN '1' THEN 'font-weight: 100;' WHEN '2' THEN 'font-weight: 700;' ELSE 'font-weight: 100;' END isNew
        FROM
            email_mail a
        <where>
            AND a.state = #{state}
            <if test="type == '' or type == null">
                AND (a.to_people = #{objectId}
                    OR INSTR(CONCAT(',', a.to_cc, ','), CONCAT(',', #{objectId}, ','))
                    OR INSTR(CONCAT(',', a.to_bcc, ','), CONCAT(',', #{objectId}, ',')))
            </if>
            <if test="type == '4'">
                AND a.from_people = #{objectId}
            </if>
            <if test="sqlExtract != '' and sqlExtract != null">
                ${sqlExtract}
            </if>
            <if test="keyword != null and keyword != ''">
                AND a.title LIKE '%${keyword}%'
            </if>
        </where>
        ORDER BY a.send_date DESC
    </select>

    <select id="queryEmailListByEmailAddress" resultType="java.util.Map">
        SELECT
            a.id,
            a.message_id messageId
        FROM
            email_mail a
        WHERE (a.to_people = #{userAddress}
            OR INSTR(CONCAT(',', a.to_cc, ','), CONCAT(',', #{userAddress}, ','))
            OR INSTR(CONCAT(',', a.to_bcc, ','), CONCAT(',', #{userAddress}, ',')))
          AND a.state = #{state}
    </select>

    <insert id="insertEmailListToServer" parameterType="java.util.Map">
        insert into email_mail
        (id, title, send_date, replay_sign, is_new, is_contain_attach, from_people, to_people, to_cc, to_bcc, message_id, content, create_time, state)
        values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.id}, #{item.title}, #{item.sendDate}, #{item.replaySign}, #{item.isNew}, #{item.isContainAttach}, #{item.fromPeople}, #{item.toPeople}, #{item.toCc},
            #{item.toBcc}, #{item.messageId}, #{item.content}, #{item.createTime}, #{item.emailState})
        </foreach>
    </insert>

    <insert id="insertEmailEnclosureListToServer" parameterType="java.util.Map">
        insert into email_enclosure
        (id, file_name, file_ext_name, file_path, file_size, parent_id)
        values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.id}, #{item.fileName}, #{item.fileExtName}, #{item.filePath}, #{item.fileSize}, #{item.parentId})
        </foreach>
    </insert>

    <update id="editEmailMessageIdByEmailId" parameterType="java.util.Map">
        UPDATE email_mail
        <set>
            <if test="messageId != '' and messageId != null">
                message_id = #{messageId},
            </if>
        </set>
        WHERE id = #{id}
    </update>

    <select id="queryEmailListByEmailFromAddress" resultType="java.util.Map">
        SELECT
            a.id,
            a.message_id messageId
        FROM
            email_mail a
        WHERE a.from_people = #{userAddress}
          AND a.state = #{state}
    </select>

</mapper>