<?xml version="1.0" encoding="UTF-8" ?>  
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.skyeye.eve.licence.dao.LicenceDao">

	<select id="queryLicenceList" resultType="java.util.Map">
		SELECT
			a.id,
			a.`name`,
			a.licence_num licenceNum,
			a.issuing_organization issuingOrganization,
			a.issue_time issueTime,
			a.annual_review annualReview,
            a.next_annual_review nextAnnualReview,
			a.term_of_validity termOfValidity,
            a.term_of_validity_time termOfValidityTime,
			a.licence_admin licenceAdmin,
			a.borrow_id borrowId,
			a.create_id createId,
			CONVERT(a.create_time, char) createTime,
			a.last_update_id lastUpdateId,
			CONVERT(a.last_update_time, char) lastUpdateTime
		FROM
			licence a
		<where>
			<if test="sqlExtract != '' and sqlExtract != null">
				${sqlExtract}
			</if>
			<if test="chargePersonId != '' and chargePersonId != null">
				AND a.borrow_id = #{chargePersonId}
			</if>
			<if test="keyword != null and keyword != ''">
				AND (a.`name` like '%${keyword}%' OR a.licence_num like '%${keyword}%')
			</if>
		</where>
		ORDER BY a.create_time DESC
	</select>

</mapper>