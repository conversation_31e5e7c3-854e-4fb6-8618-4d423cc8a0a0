<?xml version="1.0" encoding="UTF-8" ?>  
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.skyeye.eve.notice.dao.UserMessageDao">
	
	<select id="queryUserMessageList" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT
			a.id,
			a.`name`,
			a.remark,
			a.content,
			a.state,
			CONVERT(a.create_time, char) createTime
		FROM
			sys_eve_user_notice a
		<where>
			a.delete_flag = #{deleteFlag}
			<if test="sqlExtract != '' and sqlExtract != null">
				${sqlExtract}
			</if>
			<if test="keyword != null and keyword != ''">
				AND a.`name` like '%${keyword}%'
			</if>
			<if test="objectId != null and objectId != ''">
				AND a.receive_id = #{objectId}
			</if>
		</where>
		ORDER BY a.state ASC, a.create_time DESC
	</select>
	
</mapper>