<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.skyeye.eve.diskcloud.dao.FileCatalogDao">

    <select id="queryFolderAndChildList" resultType="java.util.Map">
        SELECT
            a.id,
            a.`name`,
            a.parent_id parentId,
            'folder' type,
            a.delete_flag deleteFlag
        FROM
            file_catalog a
        WHERE a.delete_flag = #{deleteFlag}
        AND
        <foreach collection="ids" item="id" index="index" open="(" close=")" separator="OR">
            INSTR(CONCAT(',', a.parent_id, ','), CONCAT(',', #{id}, ',')) OR a.id = #{id}
        </foreach>
        ORDER BY LENGTH(a.parent_id) ASC
    </select>

</mapper>