<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.skyeye.eve.knowlg.dao.KnowledgeContentDao">
	
	<select id="queryKnowledgeContentList" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT
			a.id,
			a.`name`,
			a.label,
			a.type_id typeId,
			a.state,
			a.remark,
			a.examine_id examineId,
			CONVERT(a.examine_time, char) examineTime,
			a.create_id createId,
			CONVERT(a.create_time, char) createTime,
			a.last_update_id lastUpdateId,
			CONVERT(a.last_update_time, char) lastUpdateTime
		FROM
			knowlg_content a
		<where>
			<if test="createId != null and createId != ''">
				AND a.create_id = #{createId}
			</if>
			<if test="typeId != null and typeId != ''">
				AND a.type_id = #{typeId}
			</if>
			<if test="sqlExtract != '' and sqlExtract != null">
				${sqlExtract}
			</if>
			<if test="keyword != null and keyword != ''">
				AND a.`name` like '%${keyword}%'
			</if>
			<if test="state != null and state != ''">
				AND a.state = #{state}
			</if>
			<if test="stateList != null and stateList.size() &gt; 0">
				<foreach collection="stateList" item="state" separator="," open=" AND a.state in(" close=")">
					#{state}
				</foreach>
			</if>
		</where>
		ORDER BY a.create_time DESC
	</select>
	
</mapper>