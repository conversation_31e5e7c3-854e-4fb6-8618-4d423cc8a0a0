# 从Nacos迁移到Consul的改动点分析

## 概述

将现有ERP系统从Nacos迁移到Consul的改动量是**中等程度**的。主要涉及依赖替换、配置调整和少量代码修改，不会影响业务逻辑，属于基础设施层面的替换。

---

## 改动范围评估

### 🔴 必须修改的文件（约15个文件）
### 🟡 建议优化的文件（约5个文件）
### 🟢 可选调整的文件（约3个文件）

**总体评估**：改动量适中，风险可控，预计工作量1-2人天

---

## 详细改动清单

### 1. 依赖配置修改 🔴

#### 1.1 父POM依赖管理修改

**文件路径**: `skyeye-foundation/skyeye-parent/pom.xml`

**当前配置**:
```xml
<!-- Spring Cloud Alibaba -->
<dependency>
    <groupId>com.alibaba.cloud</groupId>
    <artifactId>spring-cloud-alibaba-dependencies</artifactId>
    <version>${alibaba-cloud.version}</version>
    <type>pom</type>
    <scope>import</scope>
</dependency>

<!-- Nacos配置中心 -->
<dependency>
    <groupId>com.alibaba.cloud</groupId>
    <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
</dependency>

<!-- Nacos服务发现 -->
<dependency>
    <groupId>com.alibaba.cloud</groupId>
    <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
</dependency>
```

**修改为**:
```xml
<!-- 移除Spring Cloud Alibaba依赖 -->
<!-- 新增Consul依赖 -->
<dependency>
    <groupId>org.springframework.cloud</groupId>
    <artifactId>spring-cloud-starter-consul-discovery</artifactId>
</dependency>

<dependency>
    <groupId>org.springframework.cloud</groupId>
    <artifactId>spring-cloud-starter-consul-config</artifactId>
</dependency>

<!-- Consul健康检查支持 -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-actuator</artifactId>
</dependency>
```

**影响范围**: 所有子模块自动继承新依赖
**风险评估**: 低风险

#### 1.2 XXL-Job模块依赖修改

**文件路径**: `skyeye-infrastructure/xxl-job-2.3.0/xxl-job-admin/pom.xml`

**修改内容**: 同上，替换Nacos依赖为Consul依赖

**影响范围**: XXL-Job管理后台
**风险评估**: 低风险

### 2. 配置文件修改 🔴

#### 2.1 Bootstrap配置文件批量修改

**影响文件列表**（约13个文件）:
- `skyeye-frontend/skyeye-web-layui/web/src/main/resources/bootstrap.yml`
- `skyeye-business/skyeye-*/src/main/resources/bootstrap.yml` (11个业务模块)
- `skyeye-infrastructure/skyeye-zuul/src/main/resources/bootstrap.yml`

**当前Nacos配置**:
```yaml
spring:
  application:
    name: skyeye-web-${spring.profiles.active}
  profiles:
    active: dev
  cloud:
    nacos:
      discovery:
        server-addr: *************:8848
        namespace: ${spring.profiles.active}
        service: ${spring.application.name}
      config:
        server-addr: *************:8848
        file-extension: yml
        namespace: ${spring.profiles.active}
        group: DEFAULT_GROUP
```

**修改为Consul配置**:
```yaml
spring:
  application:
    name: skyeye-web-${spring.profiles.active}
  profiles:
    active: dev
  cloud:
    consul:
      host: *************  # Consul服务器地址
      port: 8500           # Consul默认端口
      discovery:
        enabled: true
        service-name: ${spring.application.name}
        hostname: ${spring.cloud.client.hostname}
        port: ${server.port}
        health-check-path: /actuator/health
        health-check-interval: 10s
        tags:
          - profile=${spring.profiles.active}
      config:
        enabled: true
        format: YAML
        default-context: application
        profile-separator: '-'
        data-key: data
```

**影响范围**: 所有微服务的启动配置
**风险评估**: 中风险，需要仔细测试

### 3. 代码修改 🔴

#### 3.1 Nacos配置服务类删除

**文件路径**: `skyeye-foundation/skyeye-common-module/skyeye-base/src/main/java/com/skyeye/config/nacos/NacosConfigService.java`

**处理方式**: 
- 删除整个NacosConfigService类
- 如果有其他代码依赖此类，需要重构为使用Spring Cloud Config的标准方式

**替代方案**:
```java
// 不再需要自定义ConfigService，Spring Cloud Consul会自动处理
// 直接使用@Value或@ConfigurationProperties注解即可

@Component
public class ConsulConfigProperties {
    
    @Value("${custom.config.value:default}")
    private String configValue;
    
    // getter/setter...
}
```

**影响范围**: 使用Nacos配置服务的业务代码
**风险评估**: 低风险，Spring自动处理

#### 3.2 配置读取代码调整

**潜在影响**: 如果有直接调用NacosConfigService的代码需要调整

**修改示例**:
```java
// 原有代码（需要删除）
@Autowired
private NacosConfigService nacosConfigService;

// 修改为标准Spring方式
@Value("${some.config.key}")
private String configValue;

// 或使用配置类
@ConfigurationProperties(prefix = "custom")
@Component
public class CustomConfig {
    private String value;
    // getter/setter
}
```

### 4. 部署相关修改 🔴

#### 4.1 安装脚本修改

**文件路径**: `scripts/packages/nacos/scripts/nacos-install.sh`

**处理方式**: 
- 创建新的Consul安装脚本
- 修改主安装脚本引用

**新增文件**: `scripts/packages/consul/scripts/consul-install.sh`

```bash
#!/bin/bash
# Consul安装脚本

CONSUL_VERSION="1.17.0"
CONSUL_DIR="/opt/consul"

# 下载Consul
wget https://releases.hashicorp.com/consul/${CONSUL_VERSION}/consul_${CONSUL_VERSION}_linux_amd64.zip

# 解压安装
unzip consul_${CONSUL_VERSION}_linux_amd64.zip
sudo mv consul /usr/local/bin/

# 创建配置目录
sudo mkdir -p ${CONSUL_DIR}/{data,config}

# 创建配置文件
cat > ${CONSUL_DIR}/config/consul.hcl << EOF
datacenter = "dc1"
data_dir = "${CONSUL_DIR}/data"
log_level = "INFO"
server = true
bootstrap_expect = 1
bind_addr = "0.0.0.0"
client_addr = "0.0.0.0"
ui_config {
  enabled = true
}
connect {
  enabled = true
}
EOF

# 创建systemd服务
cat > /etc/systemd/system/consul.service << EOF
[Unit]
Description=Consul
Documentation=https://www.consul.io/
Requires=network-online.target
After=network-online.target
ConditionFileNotEmpty=${CONSUL_DIR}/config/consul.hcl

[Service]
Type=notify
User=consul
Group=consul
ExecStart=/usr/local/bin/consul agent -config-dir=${CONSUL_DIR}/config
ExecReload=/bin/kill -HUP $MAINPID
KillMode=process
Restart=on-failure
LimitNOFILE=65536

[Install]
WantedBy=multi-user.target
EOF

# 启动服务
sudo systemctl enable consul
sudo systemctl start consul
```

#### 4.2 主安装脚本修改

**文件路径**: `scripts/install.sh`

**修改内容**: 
- 将nacos安装替换为consul安装
- 更新依赖检查逻辑

### 5. 数据迁移 🟡

#### 5.1 配置数据迁移

**当前**: Nacos配置存储在MySQL/Derby数据库
**目标**: Consul配置存储在Consul KV存储

**迁移策略**:
1. 从Nacos导出配置数据
2. 转换为Consul KV格式
3. 导入到Consul

**迁移脚本示例**:
```bash
#!/bin/bash
# nacos-to-consul-migration.sh

# 1. 导出Nacos配置
curl -X GET "http://*************:8848/nacos/v1/cs/configs?export=true&tenant=dev" > nacos-configs.zip

# 2. 解析并转换配置格式
python3 convert-config.py nacos-configs.zip consul-configs.json

# 3. 导入到Consul
consul kv import @consul-configs.json
```

### 6. 监控和运维调整 🟡

#### 6.1 健康检查调整

**Nacos**: 内置健康检查
**Consul**: 需要配置Actuator端点

**新增配置**:
```yaml
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always
```

#### 6.2 服务发现客户端调整

**影响**: 如果有代码直接使用Nacos的DiscoveryClient，需要调整

**修改示例**:
```java
// 原有代码保持不变，Spring Cloud抽象层自动适配
@Autowired
private DiscoveryClient discoveryClient;

// 获取服务实例的代码无需修改
List<ServiceInstance> instances = discoveryClient.getInstances("service-name");
```

---

## 迁移工作量评估

### 📊 工作量分解

| 类别 | 文件数量 | 预计工时 | 难度等级 |
|------|----------|----------|----------|
| POM依赖修改 | 2个 | 2小时 | 低 |
| 配置文件修改 | 13个 | 4小时 | 中 |
| Java代码调整 | 1-3个 | 2小时 | 低 |
| 部署脚本 | 3个 | 4小时 | 中 |
| 配置迁移 | - | 2小时 | 中 |
| 测试验证 | - | 4小时 | 高 |
| **总计** | **约20个** | **18小时** | **中等** |

### 🎯 迁移优势

1. **更好的企业级特性**
   - 多数据中心支持
   - 更强的一致性保证
   - 更丰富的健康检查

2. **更好的运维体验**
   - Web UI更友好
   - 监控指标更丰富
   - 社区支持更好

3. **更好的性能**
   - Go语言开发，性能更优
   - 内存占用更少
   - 响应延迟更低

### ⚠️ 迁移风险

1. **配置丢失风险**
   - 需要完整的配置备份和迁移计划
   - 建议先在测试环境验证

2. **服务发现中断**
   - 迁移过程中可能出现短暂的服务发现异常
   - 建议选择低峰期进行迁移

3. **团队学习成本**
   - 运维团队需要学习Consul的使用和维护
   - 开发团队需要了解新的配置方式

---

## 迁移实施建议

### 🚀 实施步骤

#### 阶段一：准备工作（1人天）
1. 在测试环境部署Consul
2. 备份所有Nacos配置
3. 准备配置迁移脚本

#### 阶段二：代码修改（0.5人天）
1. 修改依赖配置
2. 调整配置文件
3. 修改相关Java代码

#### 阶段三：测试验证（0.5人天）
1. 在测试环境验证功能
2. 性能测试
3. 集成测试

#### 阶段四：生产部署（预留1人天）
1. 生产环境部署Consul
2. 迁移配置数据
3. 切换服务注册
4. 监控和验证

### 💡 最佳实践建议

1. **渐进式迁移**
   - 先迁移非核心服务
   - 逐步迁移核心业务服务

2. **双运行方案**
   - 在迁移期间保持Nacos和Consul同时运行
   - 确保可以快速回滚

3. **配置版本管理**
   - 使用Git管理Consul配置
   - 建立配置变更审批流程

---

## 结论

**总体评估**: 从Nacos迁移到Consul的改动量是**中等程度**的，主要是配置层面的调整，不涉及业务逻辑修改。

**建议**:
- ✅ **可以进行迁移**：技术风险可控，收益明显
- ⏰ **最佳时机**：在多租户功能开发完成后进行
- 🔄 **实施方式**：分阶段渐进式迁移，确保系统稳定性

**预期收益**:
- 更好的企业级特性支持
- 更稳定的服务发现和配置管理
- 更优秀的性能表现
- 为未来云原生转型做好准备