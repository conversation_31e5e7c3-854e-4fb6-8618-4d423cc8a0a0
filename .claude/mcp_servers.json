{"mcpServers": {"playwright": {"command": "cmd", "args": ["/c", "npx", "@executeautomation/playwright-mcp-server"], "env": {}}, "screenshot": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-puppeteer"], "env": {}}, "snap-happy": {"command": "npx", "args": ["-y", "@mario<PERSON><PERSON>ner/snap-happy@latest"], "env": {"SNAP_HAPPY_SCREENSHOT_PATH": "Z:\\IdeaProjects\\erp\\screenshots"}}, "context7": {"command": "cmd", "args": ["/c", "npx", "-y", "@upstash/context7-mcp"]}}}