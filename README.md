# SkyEye ERP 企业级应用系统

## 项目简介

SkyEye是一个基于微服务架构的企业级ERP系统，采用Spring Boot + Spring Cloud + Nacos + Vue3的技术栈，包含30多个应用模块、50多种电子流程，涵盖CRM、PM、ERP、MES、ADM、EHR、笔记、知识库、项目、门店、商城、财务、多班次考勤、薪资、招聘、云售后、论坛、公告、问卷、报表设计、工作流、日程、云盘等全面管理功能。

## 技术栈

- **后端**: Spring Boot 2.7.x + Spring Cloud 2021.x + MyBatis Plus
- **前端**: Vue 3 + Vite + Ant Design Vue
- **注册中心**: Nacos 2.5.0
- **数据库**: MySQL 8.0
- **缓存**: Redis 7.4
- **消息队列**: RocketMQ 5.3.3
- **API网关**: Spring Cloud Gateway
- **任务调度**: XXL-Job 2.3.0
- **文档服务**: OnlyOffice DocumentServer

## 项目结构

```
skyeye-root/
├── skyeye-foundation/          # 基础设施模块
│   ├── skyeye-parent/         # 父pom - 统一依赖版本管理
│   ├── skyeye-common-module/  # 通用基础模块
│   └── skyeye-common-rest/    # REST通用模块
├── skyeye-business/           # 业务模块聚合
│   ├── skyeye-project/        # 项目管理 (端口: 8081)
│   ├── skyeye-erp/           # ERP模块 (端口: 8106)
│   ├── skyeye-crm/           # 客户关系管理 (端口: 8102)
│   ├── skyeye-boss/          # 人事管理 (端口: 8104)
│   ├── skyeye-school/        # 学校管理 (端口: 8084)
│   ├── skyeye-shop/          # 商城模块 (端口: 8082)
│   ├── skyeye-flowable/      # 工作流模块 (端口: 8083)
│   ├── skyeye-checkwork/     # 考勤模块
│   ├── skyeye-wages/         # 工资模块
│   ├── skyeye-report/        # 报表模块
│   ├── skyeye-adm/           # 行政助理模块
│   ├── skyeye-ifs/           # 财务系统模块
│   ├── skyeye-seal-service/  # 印章服务模块 (端口: 8108)
│   ├── skyeye-wall/          # 墙服务 (端口: 8110)
│   ├── skyeye-auto/          # 自动化模块 (端口: 8111)
│   ├── skyeye-tms/           # 运输管理 (端口: 8212)
│   └── skyeye-promote/       # 推广模块
├── skyeye-infrastructure/     # 基础设施服务
│   ├── skyeye-zuul/          # API网关 (端口: 7000)
│   └── xxl-job-2.3.0/        # 任务调度
└── skyeye-frontend/          # 前端项目
    └── skyeye-web/           # Vue前端项目 (端口: 5173)
```

## 服务端口分配

| 服务名称 | 端口 | 说明 |
|---------|------|------|
| MySQL | 3306 | 数据库 |
| Redis | 6379 | 缓存 |
| Nacos | 8848/9848/9849 | 注册中心/配置中心 |
| RocketMQ NameServer | 9876 | 消息队列名称服务器 |
| RocketMQ Broker | 10909/10911 | 消息队列代理 |
| OnlyOffice | 8000 | 文档服务 |
| skyeye-zuul | 7000 | API网关 |
| skyeye-flowable | 8083 | 工作流服务 |
| skyeye-school | 8084 | 学校管理 |
| skyeye-crm | 8102 | 客户关系管理 |
| skyeye-boss | 8104 | 人事管理 |
| skyeye-erp | 8106 | ERP核心模块 |
| skyeye-seal-service | 8108 | 印章服务 |
| skyeye-wall | 8110 | 墙服务 |
| skyeye-auto | 8111 | 自动化模块 |
| skyeye-tms | 8212 | 运输管理 |
| skyeye-web | 5173 | 前端服务(开发模式) |

## 快速启动

### 1. 环境准备

确保已安装以下软件：
- Docker & Docker Compose
- JDK 11+
- Maven 3.6+
- Node.js 16+

### 2. 自动化安装（推荐）

```bash
# 进入脚本目录
cd scripts

# 运行自动化安装脚本
./install.sh

# 选择菜单选项：
# 24) 统一部署所有服务
```

### 3. 手动启动

#### 3.1 启动基础设施服务（必须严格按顺序）

```bash
# 进入Docker环境目录
cd /docker/env

# 1. 启动MySQL数据库
docker-compose up -d mysql
sleep 10

# 2. 启动Redis缓存
docker-compose up -d redis

# 3. 初始化Nacos数据库
docker exec -i mysql mysql -uroot -pskyeye123456! << 'EOF'
CREATE DATABASE IF NOT EXISTS nacos_config DEFAULT CHARACTER SET utf8mb4;
EOF

# 4. 导入Nacos初始化SQL（需要先放置SQL文件）
docker exec -i mysql mysql -uroot -pskyeye123456! nacos_config < /docker/env/nacos/sql/nacos-mysql.sql

# 5. 启动Nacos注册中心
docker-compose up -d nacos

# 6. 启动RocketMQ
docker-compose up -d rmqnamesrv
docker-compose up -d rmqbroker

# 7. 启动OnlyOffice文档服务
docker-compose up -d onlyoffice
```

#### 3.2 构建Java项目

```bash
# 在项目根目录执行
mvn clean install
```

#### 3.3 启动应用服务（按顺序启动）

```bash
# 1. 启动API网关（最重要，其他服务的入口）
cd skyeye-infrastructure/skyeye-zuul
java -jar target/skyeye-zuul.jar

# 2. 启动工作流服务（很多业务依赖）
cd skyeye-business/skyeye-flowable/flowable-web
java -jar target/flowable-web.jar

# 3. 启动ERP核心模块
cd skyeye-business/skyeye-erp/erp-web
java -jar target/erp-web.jar

# 4. 启动CRM模块
cd skyeye-business/skyeye-crm/crm-web
java -jar target/crm-web.jar

# 5. 启动其他业务模块（可并行启动）
cd skyeye-business/skyeye-boss/boss-web
java -jar target/boss-web.jar

cd skyeye-business/skyeye-school/school-web
java -jar target/school-web.jar

# ... 其他业务模块类似
```

#### 3.4 启动前端服务

```bash
# 进入前端目录
cd skyeye-frontend/skyeye-web

# 安装依赖
npm install

# 开发模式启动
npm run dev

# 或生产构建
npm run build
```

## 配置说明

### 主要配置文件

- `scripts/config.sh` - 统一配置文件，包含所有组件的配置参数
- `docker-compose.yml` - Docker容器编排配置
- `*/bootstrap.yml` - 各微服务的启动配置

### 重要配置项

```bash
# 基础配置
INSTALL_DIR="/docker/env"                    # Docker数据存储目录
DOCKER_NETWORK="skyeye-net"                  # Docker网络名称
SKYEYE_PASSWORD="skyeye123456!"              # 统一密码

# Nacos配置
NACOS_AUTH_ENABLE="false"                    # 认证开关（开发环境建议关闭）
NACOS_HTTP_PORT="8848"                       # HTTP端口
NACOS_GRPC_PORT="9848"                       # gRPC端口
```

## 验证启动

### 1. 基础设施验证

```bash
# MySQL
docker exec -it mysql mysql -uroot -pskyeye123456! -e "SELECT VERSION();"

# Redis
docker exec -it redis redis-cli ping

# Nacos
curl http://localhost:8848/nacos/

# RocketMQ
docker exec -it rmqnamesrv sh mqadmin clusterList -n localhost:9876

# OnlyOffice
curl http://localhost:8000/healthcheck
```

### 2. 服务注册验证

访问 Nacos 控制台：http://localhost:8848/nacos/
- 用户名：nacos
- 密码：nacos

查看服务列表，确认所有微服务都已注册成功。

### 3. 前端访问

- 开发模式：http://localhost:5173/
- 生产模式：通过API网关访问

## 常见问题

### 1. Nacos连接失败

**问题**：`Client not connected, current status:STARTING`

**解决方案**：
- 检查Nacos是否启动：`docker ps | grep nacos`
- 检查认证配置：确保`NACOS_AUTH_ENABLE="false"`或添加用户名密码
- 检查网络连通性：`telnet ************* 8848`

### 2. 数据库连接失败

**解决方案**：
- 确保MySQL容器已启动
- 检查数据库配置和密码
- 确保数据库已创建

### 3. 服务启动顺序

**重要**：必须严格按照以下顺序启动：
1. 数据存储层（MySQL）
2. 缓存和配置中心（Redis、Nacos）
3. 消息队列（RocketMQ）
4. 基础设施服务（API网关）
5. 业务服务
6. 前端服务

## 开发指南

### 代码结构

- 业务模块采用三层架构：web层、service层、dao层
- 统一使用MyBatis Plus进行数据访问
- 前端采用Vue3 + Composition API

### 开发环境配置

1. 修改各服务的`bootstrap.yml`中的IP地址
2. 确保Nacos中的`skyeye-common.yml`配置正确
3. 数据库连接信息需要与实际环境匹配

### 启动脚本路径

```bash
# 主安装脚本
scripts/install.sh

# 各组件安装脚本
scripts/packages/docker/scripts/docker-install.sh
scripts/packages/mysql/scripts/mysql-install.sh
scripts/packages/redis/scripts/redis-install.sh
scripts/packages/nacos/scripts/nacos-install.sh
scripts/packages/rocketmq/scripts/rocketmq-install.sh
scripts/packages/onlyoffice/scripts/onlyoffice-install.sh

# 配置文件
scripts/config.sh                           # 统一配置文件
/docker/env/docker-compose.yml             # Docker编排文件
```

### 日志查看

```bash
# 查看Docker容器日志
docker logs mysql
docker logs redis
docker logs nacos
docker logs rmqnamesrv
docker logs rmqbroker
docker logs onlyoffice

# 查看应用服务日志
tail -f skyeye-infrastructure/skyeye-zuul/logs/skyeye-zuul.log
tail -f skyeye-business/skyeye-erp/erp-web/logs/erp-web.log
```

### 健康检查命令

```bash
# 检查所有Docker容器状态
docker ps -a

# 检查服务注册情况
curl http://localhost:8848/nacos/v1/ns/service/list?pageNo=1&pageSize=10

# 检查网关路由
curl http://localhost:7000/actuator/gateway/routes
```

## 故障排查

### 1. 容器启动失败

```bash
# 查看容器日志
docker logs <container_name>

# 检查端口占用
netstat -tlnp | grep <port>

# 重启容器
docker restart <container_name>
```

### 2. 服务注册失败

- 检查Nacos是否正常运行
- 确认网络连通性
- 验证认证配置是否正确
- 检查命名空间配置

### 3. 数据库连接问题

- 确认MySQL容器状态
- 检查数据库用户权限
- 验证连接字符串格式
- 确认防火墙设置

## 性能优化

### JVM参数建议

```bash
# 网关服务
java -Xms512m -Xmx1024m -jar skyeye-zuul.jar

# 业务服务
java -Xms256m -Xmx512m -jar erp-web.jar
```

### Docker资源限制

```yaml
# 在docker-compose.yml中添加资源限制
services:
  mysql:
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
```

## 联系方式

- 项目地址：https://gitee.com/dromara/skyeye
- 文档地址：查看项目wiki
- 问题反馈：提交issue
- 二开文档：https://articles.zsxq.com/id_xi3xhacte72g.html
- 视频教程：https://www.bilibili.com/video/BV1w34y1M7ZH/

## 许可证

本项目仅限购买后使用，禁止私自外泄以及用于其他商业目的。

---

**注意事项**：
1. 首次启动前请仔细阅读本文档
2. 严格按照启动顺序执行
3. 确保所有配置文件中的IP地址正确
4. 开发环境建议关闭Nacos认证功能
5. 生产环境请启用认证并修改默认密码


🏗️ 按层次顺序排列的启动类清单
️ Skyeye项目架构层次与启动类对应关系

第1层 - 基础设施层 (4个)

├── xxl-job (任务调度)
│   └── 📱 XxlJobAdminApplication (端口:8200)
├── skyeye-promote (推广服务)
│   └── 📱 SkyEyeApplication (端口:8081)
├── skyeye-zuul (API网关)
│   └── 📱 SkyeyeZuulApplication (端口:7000)
└── skyeye-web (前端工程)
└── ❌ [前端静态资源，无独立启动类]
第2层 - 功能服务模块 (15个)
├── skyeye-report
│   └── 📱 SkyReportApplication (端口:8085)

├── skyeye-flowable
│   └── 📱 SkyFlowableApplication (端口:8083)
│
│   第3层 - 管理功能模块 (7个)
│   ├── skyeye-crm (客户关系)
│   │   └── 📱 CrmApplication (端口:8102)
│   │
│   │   第4层 - 核心业务模块 (2个)
│   │   ├── skyeye-erp (ERP系统)
│   │   │   └── 📱 ErpApplication (端口:8106)
│   │   │
│   │   │   第5层 - 顶层业务模块 (3个)
│   │   │   ├── skyeye-tms (物流)
│   │   │   │   └── 📱 TmsApplication (端口:8212)
│   │   │   ├── skyeye-project (项目管理)
│   │   │   │   └── 📱 ProjectApplication (端口:8109)
│   │   │   └── skyeye-seal-service (云服务)
│   │   │       └── 📱 SealServiceApplication (端口:8108)
│   │   │
│   │   └── skyeye-shop (电商系统)
│   │       └── 📱 SkyShopApplication (端口:8082)
│   │
│   ├── skyeye-wages (薪资管理)
│   │   └── 📱 SkyWagesApplication (端口:8101)
│   ├── skyeye-boss (老板端)
│   │   └── 📱 BossApplication (端口:8104)
│   ├── skyeye-adm (行政管理)
│   │   └── 📱 AdmApplication (端口:8103)
│   ├── skyeye-checkwork (考勤管理)
│   │   └── 📱 CheckWorkApplication (端口:8105)
│   ├── skyeye-ifs (财务管理)
│   │   └── 📱 IfsApplication (端口:8107)
│   └── skyeye-schedule (日程管理)
│       └── ❌ [图中存在，项目中未找到对应Application]
├── skyeye-mail
│   └── ❌ [图中存在，项目中未找到对应Application]
├── skyeye-jobdiary
│   └── ❌ [图中存在，项目中未找到对应Application]
├── skyeye-forum
│   └── ❌ [图中存在，项目中未找到对应Application]
├── skyeye-light-app
│   └── ❌ [图中存在，项目中未找到对应Application]
├── skyeye-business-flow
│   └── ❌ [图中存在，项目中未找到对应Application]
├── skyeye-notice
│   └── ❌ [图中存在，项目中未找到对应Application]
├── skyeye-knowlg
│   └── ❌ [图中存在，项目中未找到对应Application]
├── skyeye-shi
│   └── ❌ [图中存在，项目中未找到对应Application]
├── skyeye-dak-cloud
│   └── ❌ [图中存在，项目中未找到对应Application]
├── skyeye-email
│   └── ❌ [图中存在，项目中未找到对应Application]
├── skyeye-note
│   └── ❌ [图中存在，项目中未找到对应Application]
└── skyeye-survey
└── ❌ [图中存在，项目中未找到对应Application]

📊 额外发现的模块 (项目中存在但架构图中未显示)

├── skyeye-auto (自动化)
│   └── 📱 SkyAutoApplication (端口:8111)
├── skyeye-wall (墙/展示)
│   └── 📱 SkyWallApplication (端口:8110)
└── skyeye-school (学校)
└── 📱 SkySchoolApplication (端口:8084)