#!/bin/bash

# CentOS 7兼容性设置
if [[ ${BASH_VERSION%%.*} -lt 4 ]]; then
    echo "错误: 需要bash 4.0或更高版本，当前版本: $BASH_VERSION"
    exit 1
fi

#=============================================================================
# Redis 7.4安装模块
# 版本: 2.0
# 用途: 在Docker环境中安装Redis 7.4
# 依赖: packages/redis/redis-7.4.tar
#
# 功能特性:
# - Docker容器化部署
# - 完全离线安装
# - 配置文件化管理
# - 数据持久化
# - 密码认证
# - 健康检查
# - 智能检测
# - 完整卸载
#=============================================================================

# 移除 set -e 以避免状态检查时脚本中断
# set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BASE_DIR="$(dirname "$SCRIPT_DIR")"                        # packages/redis
PACKAGES_DIR="$(dirname "$BASE_DIR")"                      # packages
ROOT_DIR="$(dirname "$PACKAGES_DIR")"                      # scripts

# 加载配置文件
if [[ -f "$ROOT_DIR/config.sh" ]]; then
    source "$ROOT_DIR/config.sh"
else
    echo "配置文件不存在: $ROOT_DIR/config.sh"
    exit 1
fi

# 日志函数
log_info() { echo -e "${GREEN}[INFO]${NC} $1"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_title() { echo -e "${BLUE}[TITLE]${NC} $1"; }
log_step() { echo -e "${BLUE}[STEP]${NC} $1"; }

# 检查Docker环境
check_docker_available() {
    if ! command -v docker >/dev/null 2>&1; then
        log_error "Docker未安装，请先安装Docker"
        return 1
    fi

    if ! docker info >/dev/null 2>&1; then
        log_error "Docker服务未启动，请启动Docker服务"
        return 1
    fi

    return 0
}

# 检查Redis安装状态（供主脚本调用）
check_redis_installed_status() {
    # 检查容器是否运行
    if docker ps | grep -q "$REDIS_CONTAINER_NAME"; then
        return 0  # 已启动
    elif docker ps -a | grep -q "$REDIS_CONTAINER_NAME"; then
        return 2  # 已安装但未启动
    else
        return 1  # 未安装
    fi
}

# 检查Redis是否已安装（内部使用）
check_redis_installed() {
    local status
    check_redis_installed_status
    status=$?

    case $status in
        0)
            log_info "Redis已安装且正在运行"
            return 0
            ;;
        2)
            log_info "Redis已安装但未运行"
            return 2
            ;;
        1)
            log_info "Redis未安装"
            return 1
            ;;
    esac
}

# 检查Redis镜像
check_redis_image() {
    log_title "检查Redis镜像"

    # 检查Docker镜像是否已加载
    if docker images | grep -q "redis.*$REDIS_VERSION"; then
        log_info "Redis镜像已存在: redis:$REDIS_VERSION"
        return 0
    fi

    # 检查镜像文件是否存在
    local redis_tar="$BASE_DIR/redis-${REDIS_VERSION}.tar"
    if [[ ! -f "$redis_tar" ]]; then
        log_error "Redis镜像文件不存在: $redis_tar"
        return 1
    fi

    # 加载镜像
    log_step "加载Redis镜像文件"
    if docker load < "$redis_tar"; then
        log_info "Redis镜像加载成功"
        return 0
    else
        log_error "Redis镜像加载失败"
        return 1
    fi
}

# 创建Redis配置文件
create_redis_config() {
    log_title "创建Redis配置文件"

    # 创建数据目录
    log_step "创建Redis数据目录"
    mkdir -p "$REDIS_DATA_DIR"/{conf,data,logs}

    # 创建Redis配置文件（根据手书和config.sh配置）
    log_step "生成Redis配置文件"

    # 使用变量替换生成配置文件
    cat > "$REDIS_DATA_DIR/conf/redis.conf" << EOF
# Redis配置文件
# 由Skyeye ERP部署脚本生成
# 基于手书要求和最佳实践

# 网络配置
bind 0.0.0.0
port 6379
timeout 0
tcp-keepalive 300

# 通用配置
daemonize no
supervised no
loglevel notice
logfile ""

# 数据持久化配置（根据手书要求）
dir /data
dbfilename dump.rdb

# RDB持久化配置
save 900 1
save 300 10
save 60 10000
stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes

# AOF持久化配置
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb
aof-load-truncated yes

# 内存管理
maxmemory $REDIS_MAX_MEMORY
maxmemory-policy $REDIS_MAX_MEMORY_POLICY

# 安全配置
requirepass $REDIS_PASSWORD

# 客户端配置
maxclients $REDIS_MAXCLIENTS
databases $REDIS_DATABASES

# 慢查询日志
slowlog-log-slower-than 10000
slowlog-max-len 128

# 键空间通知（根据手书要求）
notify-keyspace-events Ex
EOF

    log_info "Redis配置文件创建完成: $REDIS_DATA_DIR/conf/redis.conf"

    # 设置目录权限
    log_step "设置目录权限"
    chmod -R 755 "$REDIS_DATA_DIR"

    # 显示配置信息
    if [[ -n "$DEBUG" ]]; then
        log_step "Redis配置信息："
        echo "   容器名: $REDIS_CONTAINER_NAME"
        echo "   版本: $REDIS_VERSION"
        echo "   端口: $REDIS_PORT"
        echo "   数据目录: $REDIS_DATA_DIR"
        echo "   密码: $REDIS_PASSWORD"
        echo "   最大内存: $REDIS_MAX_MEMORY"
    fi
}

# 启动Redis容器
start_redis_container() {
    log_title "启动Redis容器"

    # 检查网络是否存在
    if ! docker network ls | grep -q "$DOCKER_NETWORK"; then
        log_step "创建Docker网络: $DOCKER_NETWORK"
        docker network create "$DOCKER_NETWORK"
    fi

    # 启动Redis容器（根据手书和config.sh配置）
    log_step "启动Redis $REDIS_VERSION 容器"

    # 构建docker run命令
    local docker_cmd="docker run -d"
    docker_cmd="$docker_cmd --name $REDIS_CONTAINER_NAME"
    docker_cmd="$docker_cmd --network $DOCKER_NETWORK"
    docker_cmd="$docker_cmd -p $REDIS_PORT:6379"

    # 数据卷挂载
    docker_cmd="$docker_cmd -v $REDIS_DATA_DIR/conf/redis.conf:/usr/local/etc/redis/redis.conf:ro"
    docker_cmd="$docker_cmd -v $REDIS_DATA_DIR/data:/data"
    docker_cmd="$docker_cmd -v $REDIS_DATA_DIR/logs:/var/log/redis"

    # 资源限制（从config.sh读取）
    if [[ -n "$REDIS_MEMORY_LIMIT" ]]; then
        docker_cmd="$docker_cmd --memory=$REDIS_MEMORY_LIMIT"
    fi
    if [[ -n "$REDIS_CPU_LIMIT" ]]; then
        docker_cmd="$docker_cmd --cpus=$REDIS_CPU_LIMIT"
    fi

    # 重启策略和镜像
    docker_cmd="$docker_cmd --restart unless-stopped"
    docker_cmd="$docker_cmd $REDIS_IMAGE"

    # Redis启动命令（使用配置文件）
    docker_cmd="$docker_cmd redis-server /usr/local/etc/redis/redis.conf"

    # 执行启动命令
    if [[ -n "$DEBUG" ]]; then
        log_step "执行命令: $docker_cmd"
    fi

    eval "$docker_cmd"

    if [[ $? -eq 0 ]]; then
        log_info "Redis容器启动成功"
    else
        log_error "Redis容器启动失败"
        return 1
    fi
}

# 等待Redis启动（参考MySQL的成功经验）
wait_for_redis() {
    log_title "等待Redis启动"

    log_step "等待Redis容器启动 (最多等待30秒)"
    local max_attempts=30
    local attempt=1

    # 第一阶段：等待容器启动
    while [[ $attempt -le 10 ]]; do
        if docker ps | grep -q "$REDIS_CONTAINER_NAME"; then
            log_info "✅ Redis容器已启动"
            break
        fi
        echo -n "."
        sleep 1
        attempt=$((attempt + 1))
    done

    if [[ $attempt -gt 10 ]]; then
        log_error "Redis容器启动失败"
        return 1
    fi

    # 第二阶段：等待Redis服务就绪
    log_step "等待Redis服务初始化完成 (最多等待20秒)"
    attempt=1
    while [[ $attempt -le 20 ]]; do
        # 使用redis-cli ping检测Redis是否真正就绪
        if docker exec "$REDIS_CONTAINER_NAME" redis-cli -a "$REDIS_PASSWORD" ping 2>/dev/null | grep -q "PONG"; then
            log_info "✅ Redis服务就绪"
            # 额外等待1秒确保完全就绪
            sleep 1
            return 0
        fi

        # 检查容器是否还在运行
        if ! docker ps | grep -q "$REDIS_CONTAINER_NAME"; then
            log_error "Redis容器意外停止"
            docker logs "$REDIS_CONTAINER_NAME" | tail -10
            return 1
        fi

        echo -n "."
        sleep 1
        attempt=$((attempt + 1))
    done

    log_error "Redis服务初始化超时"
    log_info "查看Redis日志："
    docker logs "$REDIS_CONTAINER_NAME" | tail -20
    return 1
}

# 验证Redis安装
verify_redis_installation() {
    log_title "验证Redis安装"

    # 检查容器状态
    log_step "检查容器状态"
    if ! docker ps | grep -q "$REDIS_CONTAINER_NAME"; then
        log_error "Redis容器未运行"
        return 1
    fi
    log_info "✅ Redis容器运行正常"

    # 检查Redis服务连通性
    log_step "检查Redis服务连通性"
    if docker exec "$REDIS_CONTAINER_NAME" redis-cli -a "$REDIS_PASSWORD" ping 2>/dev/null | grep -q "PONG"; then
        log_info "✅ Redis服务连通性正常"
    else
        log_error "❌ Redis服务连通性失败"
        return 1
    fi

    # 检查Redis版本和认证
    log_step "检查Redis版本和认证"
    local redis_version
    redis_version=$(docker exec "$REDIS_CONTAINER_NAME" redis-cli -a "$REDIS_PASSWORD" info server 2>/dev/null | grep "redis_version" | cut -d: -f2 | tr -d '\r')
    if [[ -n "$redis_version" ]]; then
        log_info "✅ Redis版本: $redis_version"
        log_info "✅ Redis认证正常"
    else
        log_warn "⚠️  Redis版本检查失败，可能仍在初始化中"
        # 给Redis更多时间初始化
        log_step "等待Redis完全初始化..."
        sleep 3
        redis_version=$(docker exec "$REDIS_CONTAINER_NAME" redis-cli -a "$REDIS_PASSWORD" info server 2>/dev/null | grep "redis_version" | cut -d: -f2 | tr -d '\r')
        if [[ -n "$redis_version" ]]; then
            log_info "✅ Redis版本: $redis_version"
        else
            log_warn "⚠️  Redis认证仍有问题，但容器运行正常"
        fi
    fi

    # 检查数据持久化
    log_step "检查数据持久化"
    if docker exec "$REDIS_CONTAINER_NAME" redis-cli -a "$REDIS_PASSWORD" config get save 2>/dev/null | grep -q "900"; then
        log_info "✅ RDB持久化配置正确"
    else
        log_warn "⚠️  RDB持久化配置可能异常"
    fi

    if docker exec "$REDIS_CONTAINER_NAME" redis-cli -a "$REDIS_PASSWORD" config get appendonly 2>/dev/null | grep -q "yes"; then
        log_info "✅ AOF持久化已启用"
    else
        log_warn "⚠️  AOF持久化未启用"
    fi

    # 检查内存配置
    log_step "检查内存配置"
    local max_memory
    max_memory=$(docker exec "$REDIS_CONTAINER_NAME" redis-cli -a "$REDIS_PASSWORD" config get maxmemory 2>/dev/null | tail -1)
    if [[ -n "$max_memory" && "$max_memory" != "0" ]]; then
        log_info "✅ 内存限制配置: $max_memory bytes"
    else
        log_warn "⚠️  未设置内存限制"
    fi

    # 测试基本操作
    log_step "测试基本操作"
    if docker exec "$REDIS_CONTAINER_NAME" redis-cli -a "$REDIS_PASSWORD" set test_key "test_value" 2>/dev/null | grep -q "OK"; then
        if docker exec "$REDIS_CONTAINER_NAME" redis-cli -a "$REDIS_PASSWORD" get test_key 2>/dev/null | grep -q "test_value"; then
            docker exec "$REDIS_CONTAINER_NAME" redis-cli -a "$REDIS_PASSWORD" del test_key >/dev/null 2>&1
            log_info "✅ Redis基本操作测试通过"
        else
            log_error "❌ Redis读取操作失败"
            return 1
        fi
    else
        log_error "❌ Redis写入操作失败"
        return 1
    fi

    log_info "✅ Redis安装验证完成"
    return 0
}

# 显示安装后信息
show_post_install_info() {
    log_title "Redis安装完成"

    local server_ip
    server_ip=$(hostname -I | awk '{print $1}')

    echo ""
    echo "✅ Redis $REDIS_VERSION 安装完成！"
    echo ""
    echo "📋 连接信息："
    echo "   • 服务器地址: $server_ip"
    echo "   • 端口: $REDIS_PORT"
    echo "   • 密码: $REDIS_PASSWORD"
    echo "   • 连接字符串: redis://:$REDIS_PASSWORD@$server_ip:$REDIS_PORT"
    echo ""
    echo "📂 数据目录："
    echo "   • 配置文件: $REDIS_DATA_DIR/conf/redis.conf"
    echo "   • 数据目录: $REDIS_DATA_DIR/data"
    echo "   • 日志目录: $REDIS_DATA_DIR/logs"
    echo ""
    echo "🔧 常用命令："
    echo "   • 进入Redis: docker exec -it $REDIS_CONTAINER_NAME redis-cli -a $REDIS_PASSWORD"
    echo "   • 查看日志: docker logs $REDIS_CONTAINER_NAME"
    echo "   • 重启服务: docker restart $REDIS_CONTAINER_NAME"
    echo "   • 停止服务: docker stop $REDIS_CONTAINER_NAME"
    echo ""
    echo "🔗 验证连接："
    echo "   • 本地验证: docker exec -it $REDIS_CONTAINER_NAME redis-cli -a $REDIS_PASSWORD ping"
    echo "   • 远程验证: redis-cli -h $server_ip -p $REDIS_PORT -a $REDIS_PASSWORD ping"
    echo ""
    echo "📊 服务状态："
    echo "   • 容器状态: $(docker ps --format 'table {{.Status}}' --filter name=$REDIS_CONTAINER_NAME | tail -n 1)"
    echo "   • 网络: $DOCKER_NETWORK"
    echo "   • 持久化: RDB + AOF"
    echo "   • 最大内存: $REDIS_MAX_MEMORY"
    echo ""
}

# 检查Redis相关包（供主脚本调用）
check_redis_packages() {
    local errors=0
    
    # 检查Redis Docker镜像文件
    local redis_tar="$BASE_DIR/redis-${REDIS_VERSION}.tar"
    if [[ -f "$redis_tar" ]]; then
        local file_size
        file_size=$(du -h "$redis_tar" | cut -f1)
        echo "✓ Redis ${REDIS_VERSION} Docker镜像 ($file_size)"
        
        # 检查文件大小
        local size_bytes=$(stat -c%s "$redis_tar")
        local size_mb=$((size_bytes / 1024 / 1024))
        if [[ $size_mb -lt 50 ]]; then
            echo "⚠ Redis镜像文件似乎过小 (${size_mb}MB)，请检查完整性"
            errors=$((errors + 1))
        fi
    else
        echo "✗ 缺失: Redis ${REDIS_VERSION} Docker镜像"
        errors=$((errors + 1))
    fi
    
    # 检查脚本本身
    if [[ -x "${BASH_SOURCE[0]}" ]]; then
        echo "✓ Redis安装脚本具有执行权限"
    else
        echo "⚠ Redis安装脚本缺少执行权限"
        errors=$((errors + 1))
    fi
    
    exit $errors
}

# 主安装函数
main() {
    log_title "Redis 7.4安装开始"
    echo "目标版本: Redis 7.4"
    echo "安装方式: Docker容器"
    echo ""

    # 检查Docker环境
    if ! check_docker_available; then
        exit 1
    fi

    # 检查是否已安装
    check_redis_installed
    local install_status=$?

    case $install_status in
        0)
            log_info "Redis已安装且正在运行，跳过安装"
            show_post_install_info
            exit 0
            ;;
        2)
            log_info "Redis已安装但未运行，尝试启动"
            if docker start "$REDIS_CONTAINER_NAME"; then
                log_info "Redis容器启动成功"
                show_post_install_info
                exit 0
            else
                log_warn "Redis容器启动失败，将重新安装"
                # 删除有问题的容器
                docker rm -f "$REDIS_CONTAINER_NAME" 2>/dev/null || true
            fi
            ;;
        1)
            log_info "Redis未安装，开始安装"
            ;;
    esac

    # 执行安装流程
    if ! check_redis_image; then
        exit 1
    fi

    if ! create_redis_config; then
        exit 1
    fi

    if ! start_redis_container; then
        exit 1
    fi

    if ! wait_for_redis; then
        exit 1
    fi

    if ! verify_redis_installation; then
        log_error "Redis安装验证失败"
        exit 1
    fi

    # 显示安装完成信息
    show_post_install_info

    log_info "✅ Redis 7.4安装完成"
}

# 卸载Redis
uninstall_redis() {
    log_title "卸载Redis 7.4"

    # 确认卸载
    echo ""
    echo "⚠️  即将卸载Redis，此操作将："
    echo "   1. 停止并删除Redis容器"
    echo "   2. 删除Redis镜像"
    echo "   3. 询问是否删除数据目录"
    echo ""

    while true; do
        read -p "确认卸载Redis？(y/N): " confirm
        case $confirm in
            [Yy]*)
                break
                ;;
            [Nn]*|"")
                log_info "用户取消卸载"
                exit 0
                ;;
            *)
                echo "请输入 y 或 n"
                ;;
        esac
    done

    # 停止并删除容器
    log_step "停止并删除Redis容器"
    if docker ps | grep -q "$REDIS_CONTAINER_NAME"; then
        docker stop "$REDIS_CONTAINER_NAME"
        log_info "Redis容器已停止"
    fi

    if docker ps -a | grep -q "$REDIS_CONTAINER_NAME"; then
        docker rm "$REDIS_CONTAINER_NAME"
        log_info "Redis容器已删除"
    fi

    # 删除镜像
    log_step "删除Redis镜像"
    if docker images | grep -q "redis.*$REDIS_VERSION"; then
        docker rmi "redis:$REDIS_VERSION" 2>/dev/null || true
        log_info "Redis镜像已删除"
    fi

    # 询问是否删除数据目录
    echo ""
    echo "⚠️  是否删除Redis数据目录？"
    echo "   数据目录: $REDIS_DATA_DIR"
    echo "   警告: 删除后所有Redis数据将丢失！"
    echo ""

    while true; do
        read -p "是否删除数据目录？(y/N): " delete_data
        case $delete_data in
            [Yy]*)
                if [[ -d "$REDIS_DATA_DIR" ]]; then
                    rm -rf "$REDIS_DATA_DIR"
                    log_info "Redis数据目录已删除: $REDIS_DATA_DIR"
                else
                    log_info "Redis数据目录不存在，跳过删除"
                fi
                break
                ;;
            [Nn]*|"")
                log_info "保留Redis数据目录: $REDIS_DATA_DIR"
                break
                ;;
            *)
                echo "请输入 y 或 n"
                ;;
        esac
    done

    log_info "✅ Redis 7.4卸载完成"
}

# 启动Redis服务
start_redis_service() {
    log_title "启动Redis服务"

    # 检查容器是否存在
    if ! docker ps -a | grep -q "$REDIS_CONTAINER_NAME"; then
        log_error "Redis容器不存在，请先安装Redis"
        return 1
    fi

    # 检查容器是否已经运行
    if docker ps | grep -q "$REDIS_CONTAINER_NAME"; then
        log_info "Redis容器已经在运行"
        return 0
    fi

    # 启动容器
    log_step "启动Redis容器"
    if docker start "$REDIS_CONTAINER_NAME"; then
        log_info "Redis容器启动成功"

        # 等待服务就绪
        if wait_for_redis; then
            log_info "✅ Redis服务启动完成"
            return 0
        else
            log_error "Redis服务启动超时"
            return 1
        fi
    else
        log_error "Redis容器启动失败"
        return 1
    fi
}

# 停止Redis服务
stop_redis_service() {
    log_title "停止Redis服务"

    # 检查容器是否运行
    if ! docker ps | grep -q "$REDIS_CONTAINER_NAME"; then
        log_info "Redis容器未运行"
        return 0
    fi

    # 停止容器
    log_step "停止Redis容器"
    if docker stop "$REDIS_CONTAINER_NAME"; then
        log_info "✅ Redis服务停止成功"
        return 0
    else
        log_error "Redis服务停止失败"
        return 1
    fi
}

# Redis诊断函数（参考MySQL的成功经验）
diagnose_redis_issues() {
    log_title "Redis状态诊断"

    # 1. 检查容器状态
    log_step "1. 容器状态检查"
    if docker ps | grep -q "$REDIS_CONTAINER_NAME"; then
        log_info "✅ Redis容器正在运行"
        local container_status=$(docker ps --format "table {{.Status}}" --filter name="$REDIS_CONTAINER_NAME" | tail -n 1)
        log_info "   容器状态: $container_status"
    else
        log_error "❌ Redis容器未运行"
        if docker ps -a | grep -q "$REDIS_CONTAINER_NAME"; then
            log_warn "   容器存在但已停止"
            docker ps -a --filter name="$REDIS_CONTAINER_NAME"
        else
            log_error "   容器不存在"
        fi
    fi

    # 2. 检查端口监听
    log_step "2. 端口监听检查"
    if docker port "$REDIS_CONTAINER_NAME" 2>/dev/null | grep -q "6379"; then
        log_info "✅ Redis端口映射正常"
        docker port "$REDIS_CONTAINER_NAME"
    else
        log_warn "⚠️  Redis端口映射异常"
    fi

    # 3. 检查容器日志
    log_step "3. 容器日志检查"
    log_info "最近的Redis日志："
    docker logs "$REDIS_CONTAINER_NAME" --tail 20 2>/dev/null || log_warn "无法获取容器日志"

    # 4. 检查网络连接
    log_step "4. 网络连接检查"
    if docker network ls | grep -q "$DOCKER_NETWORK"; then
        log_info "✅ Docker网络存在: $DOCKER_NETWORK"
    else
        log_warn "⚠️  Docker网络不存在: $DOCKER_NETWORK"
    fi

    # 5. 检查数据目录
    log_step "5. 数据目录检查"
    if [[ -d "$REDIS_DATA_DIR" ]]; then
        log_info "✅ 数据目录存在: $REDIS_DATA_DIR"
        local dir_size=$(du -sh "$REDIS_DATA_DIR" 2>/dev/null | cut -f1)
        log_info "   目录大小: $dir_size"
    else
        log_warn "⚠️  数据目录不存在: $REDIS_DATA_DIR"
    fi

    # 6. 检查Redis连接
    log_step "6. Redis连接检查"
    if docker exec "$REDIS_CONTAINER_NAME" redis-cli -a "$REDIS_PASSWORD" ping 2>/dev/null | grep -q "PONG"; then
        log_info "✅ Redis连接正常"

        # 检查Redis信息
        local redis_info=$(docker exec "$REDIS_CONTAINER_NAME" redis-cli -a "$REDIS_PASSWORD" info server 2>/dev/null | grep "redis_version" | cut -d: -f2 | tr -d '\r')
        if [[ -n "$redis_info" ]]; then
            log_info "   Redis版本: $redis_info"
        fi

        local connected_clients=$(docker exec "$REDIS_CONTAINER_NAME" redis-cli -a "$REDIS_PASSWORD" info clients 2>/dev/null | grep "connected_clients" | cut -d: -f2 | tr -d '\r')
        if [[ -n "$connected_clients" ]]; then
            log_info "   连接客户端数: $connected_clients"
        fi
    else
        log_error "❌ Redis连接失败"
    fi
}

# 更新Redis配置
update_redis_config() {
    log_title "更新Redis配置"
    
    # 检查Docker是否可用
    if ! check_docker_available; then
        return 1
    fi
    
    # 检查Redis容器是否存在
    if ! docker ps -a --format "table {{.Names}}" | grep -q "^$REDIS_CONTAINER_NAME$"; then
        log_error "Redis容器不存在，请先安装Redis"
        return 1
    fi
    
    # 检查配置文件目录是否存在
    if [[ ! -d "$REDIS_DATA_DIR/conf" ]]; then
        log_error "Redis配置目录不存在: $REDIS_DATA_DIR/conf"
        return 1
    fi
    
    # 备份现有配置
    local config_file="$REDIS_DATA_DIR/conf/redis.conf"
    local backup_config="$REDIS_DATA_DIR/conf/redis.conf.backup.$(date +%Y%m%d_%H%M%S)"
    
    if [[ -f "$config_file" ]]; then
        log_step "备份现有配置文件"
        cp "$config_file" "$backup_config"
        log_info "配置已备份到: $backup_config"
    fi
    
    # 记录容器运行状态
    local was_running=false
    if docker ps --format "table {{.Names}}" | grep -q "^$REDIS_CONTAINER_NAME$"; then
        was_running=true
        log_info "Redis容器当前正在运行"
    fi
    
    # 重新生成配置文件
    log_step "生成新的Redis配置文件"
    create_redis_config
    
    # 如果容器正在运行，需要重新创建容器以应用新的环境变量
    if [[ "$was_running" == true ]]; then
        log_step "重新创建Redis容器以应用新配置（包括环境变量）"
        log_warn "注意：Docker容器的环境变量只能在创建时设置，需要重新创建容器"

        # 停止并删除旧容器
        if docker stop "$REDIS_CONTAINER_NAME"; then
            log_info "Redis容器已停止"
            sleep 3

            if docker rm "$REDIS_CONTAINER_NAME"; then
                log_info "旧Redis容器已删除"
            else
                log_error "删除旧容器失败"
                return 1
            fi
        else
            log_error "停止Redis容器失败"
            # 恢复备份配置
            if [[ -f "$backup_config" ]]; then
                cp "$backup_config" "$config_file"
                log_info "已恢复备份配置"
            fi
            return 1
        fi

        # 重新创建并启动容器（使用新的环境变量）
        if start_redis_container; then
            log_info "新Redis容器已创建并启动"
            
            # 等待Redis服务就绪
            if wait_for_redis; then
                log_info "✅ Redis配置更新成功，新配置已生效"
                
                # 显示新配置内容摘要
                log_step "当前Redis配置摘要："
                if [[ -f "$config_file" ]]; then
                    echo "端口: $(grep "^port" "$config_file" | cut -d' ' -f2)"
                    echo "最大内存: $(grep "^maxmemory" "$config_file" | cut -d' ' -f2)"
                    echo "内存策略: $(grep "^maxmemory-policy" "$config_file" | cut -d' ' -f2)"
                    echo "配置文件路径: $config_file"
                fi
                
                return 0
            else
                log_error "Redis服务启动后等待超时，恢复备份配置"
                # 停止容器
                docker stop "$REDIS_CONTAINER_NAME" >/dev/null 2>&1
                
                # 恢复备份配置
                if [[ -f "$backup_config" ]]; then
                    cp "$backup_config" "$config_file"
                    log_info "已恢复备份配置"
                    
                    # 重新启动容器
                    docker start "$REDIS_CONTAINER_NAME" >/dev/null 2>&1
                    log_info "已使用备份配置重启容器"
                fi
                return 1
            fi
        else
            log_error "启动Redis容器失败，恢复备份配置"
            # 恢复备份配置
            if [[ -f "$backup_config" ]]; then
                cp "$backup_config" "$config_file"
                docker start "$REDIS_CONTAINER_NAME" >/dev/null 2>&1
                log_info "已恢复备份配置并重启容器"
            fi
            return 1
        fi
    else
        log_info "✅ Redis配置更新完成"
        log_warn "Redis容器未运行，请手动重启容器以应用新配置"
        log_info "重启命令: docker restart $REDIS_CONTAINER_NAME"
        
        # 显示新配置内容摘要
        log_step "新配置内容摘要："
        if [[ -f "$config_file" ]]; then
            echo "端口: $(grep "^port" "$config_file" | cut -d' ' -f2)"
            echo "最大内存: $(grep "^maxmemory" "$config_file" | cut -d' ' -f2)"
            echo "内存策略: $(grep "^maxmemory-policy" "$config_file" | cut -d' ' -f2)"
            echo "配置文件路径: $config_file"
        fi
        
        return 0
    fi
}

# 显示帮助信息
show_help() {
    echo "Redis 7.4安装脚本使用说明:"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  (无参数)          执行Redis安装"
    echo "  --check-installed 检查Redis是否已安装"
    echo "  --check-packages  检查Redis安装包完整性"
    echo "  --start           启动Redis服务"
    echo "  --stop            停止Redis服务"
    echo "  --update-config   更新Redis配置文件"
    echo "  --uninstall       卸载Redis"
    echo "  --diagnose        诊断Redis问题"
    echo "  --help           显示此帮助信息"
    echo ""
    echo "说明:"
    echo "  • --update-config会重新生成redis.conf并重启容器"
    echo "  • 配置更新前会自动备份现有配置文件"
    echo ""
    echo "功能特性:"
    echo "  • Docker容器化部署"
    echo "  • 完全离线安装"
    echo "  • 配置文件化管理"
    echo "  • 数据持久化 (RDB + AOF)"
    echo "  • 密码认证"
    echo "  • 健康检查"
    echo "  • 智能检测"
    echo "  • 完整卸载"
    echo ""
}

# 处理命令行参数
handle_arguments() {
    case "$1" in
        --check-installed)
            check_redis_installed_status
            ;;
        --check-packages)
            check_redis_packages
            ;;
        --uninstall)
            uninstall_redis
            ;;
        --start)
            start_redis_service
            ;;
        --stop)
            stop_redis_service
            ;;
        --update-config)
            update_redis_config
            ;;
        --diagnose)
            diagnose_redis_issues
            ;;
        --help)
            show_help
            ;;
        *)
            main "$@"
            ;;
    esac
}

# 错误处理
trap 'echo -e "\nRedis脚本执行过程中出现错误"; exit 1' ERR

# 执行参数处理
handle_arguments "$@"