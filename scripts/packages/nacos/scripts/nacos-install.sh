#!/bin/bash

# CentOS 7兼容性设置
if [[ ${BASH_VERSION%%.*} -lt 4 ]]; then
    echo "错误: 需要bash 4.0或更高版本，当前版本: $BASH_VERSION"
    exit 1
fi

#=============================================================================
# Nacos 2.5.0安装模块
# 版本: 1.0
# 用途: 在Docker环境中安装Nacos 2.5.0
# 依赖: packages/nacos/nacos-2.5.0.tar
#
# 功能:
# - 检查依赖（Docker、MySQL）
# - 创建目录结构
# - 生成配置文件
# - 初始化数据库
# - 启动Nacos容器
# - 健康检查
# - 卸载功能
# - 启动/停止功能
#=============================================================================

# 移除 set -e 以避免状态检查时脚本中断
# set -e

# 调试模式支持
if [[ -n "$DEBUG" ]]; then
    set -x
fi

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BASE_DIR="$(dirname "$SCRIPT_DIR")"                        # packages/nacos
PACKAGES_DIR="$(dirname "$BASE_DIR")"                      # packages
ROOT_DIR="$(dirname "$PACKAGES_DIR")"                      # scripts

# 加载配置文件
if [[ -f "$ROOT_DIR/config.sh" ]]; then
    source "$ROOT_DIR/config.sh"
else
    echo "配置文件不存在: $ROOT_DIR/config.sh"
    exit 1
fi

# 加载通用函数库
if [[ -f "$ROOT_DIR/utils/common.sh" ]]; then
    source "$ROOT_DIR/utils/common.sh"
else
    # 基本的日志函数
    log_error() { echo -e "\033[0;31m[ERROR]\033[0m $1"; }
    log_info() { echo -e "\033[0;32m[INFO]\033[0m $1"; }
    log_warn() { echo -e "\033[1;33m[WARN]\033[0m $1"; }
    log_step() { echo -e "\033[0;35m[STEP]\033[0m $1"; }
    log_success() { echo -e "\033[0;32m[SUCCESS]\033[0m $1"; }
    log_title() { echo -e "\033[0;34m[TITLE]\033[0m $1"; }
fi

# ============================================================================
# 依赖检查函数
# ============================================================================

# 检查Docker依赖
check_docker_dependency() {
    log_step "检查Docker依赖"

    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        return 1
    fi

    if ! systemctl is-active docker &> /dev/null; then
        log_error "Docker服务未启动，请先启动Docker服务"
        log_info "启动命令: systemctl start docker"
        return 1
    fi

    if ! docker info &> /dev/null; then
        log_error "Docker守护进程无响应"
        return 1
    fi

    log_info "✅ Docker环境检查通过"
    return 0
}

# 检查MySQL依赖
check_mysql_dependency() {
    log_step "检查MySQL依赖"

    # 检查MySQL容器是否运行
    if ! docker ps --format "{{.Names}}" 2>/dev/null | grep -q "^${MYSQL_CONTAINER_NAME}$"; then
        log_error "MySQL容器未运行，Nacos需要MySQL数据库支持"
        log_info "请先安装并启动MySQL: 选择菜单 2"
        return 1
    fi

    # 测试MySQL连接
    if ! docker exec "$MYSQL_CONTAINER_NAME" mysql -uroot -p"$MYSQL_PASSWORD" -e "SELECT 1;" &> /dev/null; then
        log_error "MySQL连接测试失败"
        return 1
    fi

    log_info "✅ MySQL依赖检查通过"
    return 0
}

# 检查网络依赖
check_network_dependency() {
    log_step "检查Docker网络"

    if ! docker network ls | grep -q "$DOCKER_NETWORK"; then
        log_warn "Docker网络不存在，将自动创建: $DOCKER_NETWORK"
        if ! docker network create "$DOCKER_NETWORK"; then
            log_error "Docker网络创建失败"
            return 1
        fi
        log_info "✅ Docker网络创建成功: $DOCKER_NETWORK"
    else
        log_info "✅ Docker网络已存在: $DOCKER_NETWORK"
    fi

    return 0
}

# 检查Nacos是否已安装
check_nacos_installed() {
    # 使用通用函数库中的容器状态检查函数
    if declare -f check_container_status >/dev/null 2>&1; then
        check_container_status "$NACOS_CONTAINER_NAME" "curl -f http://localhost:${NACOS_HTTP_PORT}/nacos/actuator/health"
        return $?
    fi

    # 备用检查逻辑（如果通用函数库不可用）
    # 先检查Docker是否可用
    if ! command -v docker &> /dev/null; then
        return 1  # Docker未安装，Nacos肯定未安装
    fi

    if ! systemctl is-active docker &> /dev/null; then
        return 1  # Docker服务未运行，无法检查Nacos状态
    fi

    # 检查配置变量是否已加载
    if [[ -z "$NACOS_CONTAINER_NAME" ]]; then
        return 1  # 配置未加载，返回未安装
    fi

    # 检查Nacos容器状态
    if docker ps --format "{{.Names}}" 2>/dev/null | grep -q "^$NACOS_CONTAINER_NAME$"; then
        return 0  # 已安装并运行
    elif docker ps -a --format "{{.Names}}" 2>/dev/null | grep -q "^$NACOS_CONTAINER_NAME$"; then
        return 2  # 已安装但未运行
    else
        return 1  # 未安装
    fi
}

# ============================================================================
# 目录和文件准备函数
# ============================================================================

# 创建Nacos目录结构
create_nacos_directories() {
    log_step "创建Nacos目录结构"

    local directories=(
        "$NACOS_DATA_DIR"
        "$NACOS_DATA_DIR/conf"
        "$NACOS_DATA_DIR/data"
        "$NACOS_DATA_DIR/logs"
        "$NACOS_DATA_DIR/sql"
    )

    for dir in "${directories[@]}"; do
        if [[ ! -d "$dir" ]]; then
            mkdir -p "$dir"
            log_info "创建目录: $dir"
        fi
    done

    # 设置目录权限
    chmod -R 755 "$NACOS_DATA_DIR"
    log_info "✅ Nacos目录结构创建完成"
    return 0
}

# 准备Nacos SQL文件（离线安装）
prepare_nacos_sql() {
    log_step "准备Nacos初始化SQL文件"

    local sql_file="$NACOS_DATA_DIR/sql/mysql-schema.sql"
    local nacos_sql_in_package="$BASE_DIR/mysql-schema.sql"

    # 检查是否已有SQL文件
    if [[ -f "$sql_file" ]]; then
        log_info "SQL文件已存在: $sql_file"
        return 0
    fi

    # 检查包目录中是否有SQL文件（离线安装必需）
    if [[ -f "$nacos_sql_in_package" ]]; then
        log_info "复制SQL文件: $nacos_sql_in_package -> $sql_file"
        cp "$nacos_sql_in_package" "$sql_file"
        log_info "✅ SQL文件复制成功"
        return 0
    fi

    # 离线安装模式：SQL文件缺失
    log_error "SQL文件不存在: $nacos_sql_in_package"
    echo ""
    echo "📥 离线安装需要手动下载SQL文件："
    echo ""
    echo "🔗 下载地址："
    echo "   https://raw.githubusercontent.com/alibaba/nacos/2.5.0/config/src/main/resources/META-INF/nacos-db.sql"
    echo ""
    echo "📁 保存位置："
    echo "   $nacos_sql_in_package"
    echo ""
    echo "💡 操作步骤："
    echo "   1. 从上述地址下载SQL文件"
    echo "   2. 重命名为 mysql-schema.sql"
    echo "   3. 放置到 $BASE_DIR/ 目录"
    echo "   4. 重新运行安装脚本"
    echo ""
    return 1
}



# 检查Nacos安装状态（供主脚本调用）
check_nacos_installed_status() {
    check_nacos_installed
    exit $?
}

# 检查Nacos相关包（供主脚本调用）
check_nacos_packages() {
    local errors=0

    # 检查Nacos Docker镜像文件
    local nacos_tar="$BASE_DIR/nacos-${NACOS_VERSION}.tar"
    if [[ -f "$nacos_tar" ]]; then
        local file_size=$(du -h "$nacos_tar" | cut -f1)
        echo "✓ Nacos ${NACOS_VERSION} Docker镜像 ($file_size)"

        # 检查文件大小
        local size_bytes=$(stat -c%s "$nacos_tar")
        local size_mb=$((size_bytes / 1024 / 1024))
        if [[ $size_mb -lt 200 ]]; then
            echo "⚠ Nacos镜像文件似乎过小 (${size_mb}MB)，请检查完整性"
            errors=$((errors + 1))
        fi
    else
        echo "✗ 缺失: Nacos ${NACOS_VERSION} Docker镜像"
        errors=$((errors + 1))
    fi

    # 检查Nacos SQL文件（离线安装必需）
    local nacos_sql="$BASE_DIR/mysql-schema.sql"
    if [[ -f "$nacos_sql" ]]; then
        local file_size=$(du -h "$nacos_sql" | cut -f1)
        echo "✓ Nacos MySQL初始化SQL文件 ($file_size)"

        # 检查SQL文件内容
        if grep -q "config_info" "$nacos_sql" && grep -q "CREATE TABLE" "$nacos_sql"; then
            echo "✓ SQL文件内容验证通过"
        else
            echo "⚠ SQL文件内容可能不完整"
            errors=$((errors + 1))
        fi
    else
        echo "✗ 缺失: Nacos MySQL初始化SQL文件"
        echo "   下载地址: https://raw.githubusercontent.com/alibaba/nacos/2.5.0/config/src/main/resources/META-INF/nacos-db.sql"
        echo "   保存为: $nacos_sql"
        errors=$((errors + 1))
    fi

    # 检查脚本本身
    if [[ -x "${BASH_SOURCE[0]}" ]]; then
        echo "✓ Nacos安装脚本具有执行权限"
    else
        echo "⚠ Nacos安装脚本缺少执行权限"
        errors=$((errors + 1))
    fi

    exit $errors
}

# ============================================================================
# 配置文件生成函数
# ============================================================================

# 生成Nacos配置文件
generate_nacos_config() {
    log_step "生成Nacos配置文件"

    local config_file="$NACOS_DATA_DIR/conf/application.properties"

    cat > "$config_file" << EOF
# Nacos配置文件
# 生成时间: $(date)

# 数据库配置
spring.datasource.platform=mysql
db.num=1
db.url.0=jdbc:mysql://${NACOS_DB_HOST}:${NACOS_DB_PORT}/${NACOS_DB_NAME}?characterEncoding=utf8&connectTimeout=1000&socketTimeout=3000&autoReconnect=true&useUnicode=true&useSSL=false&serverTimezone=UTC&allowPublicKeyRetrieval=true
db.user=${NACOS_DB_USER}
db.password=${NACOS_DB_PASSWORD}

# 单机模式
nacos.standalone=true

# 认证配置
nacos.core.auth.enabled=${NACOS_AUTH_ENABLE}
nacos.core.auth.server.identity.key=${NACOS_AUTH_IDENTITY_KEY}
nacos.core.auth.server.identity.value=${NACOS_AUTH_IDENTITY_VALUE}
nacos.core.auth.plugin.nacos.token.secret.key=${NACOS_AUTH_TOKEN}

# 日志配置
nacos.logs.path=/home/<USER>/logs

# 管理端点配置
management.endpoints.web.exposure.include=*
management.endpoint.health.show-details=always

# 服务器配置
server.servlet.contextPath=/nacos
server.port=${NACOS_HTTP_PORT}

# gRPC配置
nacos.inetutils.ip-address=${SERVER_IP}
EOF

    log_info "✅ Nacos配置文件生成完成: $config_file"
    return 0
}

# ============================================================================
# 数据库初始化函数
# ============================================================================

# 初始化Nacos数据库
initialize_nacos_database() {
    log_step "初始化Nacos数据库"

    # 检查数据库是否已存在
    if docker exec "$MYSQL_CONTAINER_NAME" mysql -uroot -p"$MYSQL_PASSWORD" -e "USE $NACOS_DB_NAME;" &> /dev/null; then
        log_info "数据库 $NACOS_DB_NAME 已存在，检查表结构"

        # 检查关键表是否存在
        if docker exec "$MYSQL_CONTAINER_NAME" mysql -uroot -p"$MYSQL_PASSWORD" -e "USE $NACOS_DB_NAME; SHOW TABLES LIKE 'config_info';" | grep -q "config_info"; then
            log_info "✅ Nacos数据库已初始化，跳过"
            return 0
        fi
    fi

    # 创建数据库
    log_info "创建数据库: $NACOS_DB_NAME"
    if ! docker exec "$MYSQL_CONTAINER_NAME" mysql -uroot -p"$MYSQL_PASSWORD" -e "CREATE DATABASE IF NOT EXISTS $NACOS_DB_NAME DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"; then
        log_error "数据库创建失败"
        return 1
    fi

    # 执行SQL初始化
    local sql_file="$NACOS_DATA_DIR/sql/mysql-schema.sql"
    if [[ -f "$sql_file" ]]; then
        log_info "执行SQL初始化文件: $sql_file"
        if docker exec -i "$MYSQL_CONTAINER_NAME" mysql -uroot -p"$MYSQL_PASSWORD" "$NACOS_DB_NAME" < "$sql_file"; then
            log_info "✅ Nacos数据库初始化完成"
            return 0
        else
            log_error "SQL文件执行失败"
            return 1
        fi
    else
        log_error "SQL文件不存在: $sql_file"
        return 1
    fi
}

# ============================================================================
# Docker镜像和容器管理函数
# ============================================================================

# 加载Nacos Docker镜像
load_nacos_image() {
    log_step "加载Nacos Docker镜像"

    # 检查镜像是否已存在
    if docker images --format "{{.Repository}}:{{.Tag}}" | grep -q "^$NACOS_IMAGE$"; then
        log_info "✅ Nacos镜像已存在: $NACOS_IMAGE"
        return 0
    fi

    # 加载镜像文件
    local nacos_tar="$BASE_DIR/nacos-${NACOS_VERSION}.tar"
    if [[ -f "$nacos_tar" ]]; then
        log_info "加载镜像文件: $nacos_tar"
        if docker load -i "$nacos_tar"; then
            log_info "✅ Nacos镜像加载成功"
            return 0
        else
            log_error "镜像加载失败"
            return 1
        fi
    else
        log_error "镜像文件不存在: $nacos_tar"
        log_error "离线安装模式不支持在线拉取镜像"
        echo ""
        echo "📥 请确保镜像文件存在："
        echo "   文件路径: $nacos_tar"
        echo "   镜像名称: $NACOS_IMAGE"
        echo ""
        return 1
    fi
}

# 启动Nacos容器
start_nacos_container() {
    log_step "启动Nacos容器"

    # 检查容器是否已存在
    if docker ps -a --format "{{.Names}}" | grep -q "^$NACOS_CONTAINER_NAME$"; then
        log_info "容器已存在，先删除旧容器"
        docker stop "$NACOS_CONTAINER_NAME" &> /dev/null
        docker rm "$NACOS_CONTAINER_NAME" &> /dev/null
    fi

    # 启动新容器
    log_info "启动Nacos容器: $NACOS_CONTAINER_NAME"

    local docker_run_cmd=(
        docker run -d
        --name "$NACOS_CONTAINER_NAME"
        --network "$DOCKER_NETWORK"
        -p "${NACOS_HTTP_PORT}:${NACOS_HTTP_PORT}"
        -p "${NACOS_GRPC_PORT}:${NACOS_GRPC_PORT}"
        -p "${NACOS_GRPC_PORT2}:${NACOS_GRPC_PORT2}"
        -v "$NACOS_DATA_DIR/conf/application.properties:/home/<USER>/conf/application.properties"
        -v "$NACOS_DATA_DIR/logs:/home/<USER>/logs"
        -e MODE=standalone
        -e SPRING_DATASOURCE_PLATFORM=mysql
        -e MYSQL_SERVICE_HOST="$NACOS_DB_HOST"
        -e MYSQL_SERVICE_PORT="$NACOS_DB_PORT"
        -e MYSQL_SERVICE_USER="$NACOS_DB_USER"
        -e MYSQL_SERVICE_PASSWORD="$NACOS_DB_PASSWORD"
        -e MYSQL_SERVICE_DB_NAME="$NACOS_DB_NAME"
        -e NACOS_AUTH_ENABLE="$NACOS_AUTH_ENABLE"
        -e NACOS_AUTH_TOKEN="$NACOS_AUTH_TOKEN"
        -e NACOS_AUTH_IDENTITY_KEY="$NACOS_AUTH_IDENTITY_KEY"
        -e NACOS_AUTH_IDENTITY_VALUE="$NACOS_AUTH_IDENTITY_VALUE"
        --memory="$NACOS_MEMORY_LIMIT"
        --cpus="$NACOS_CPU_LIMIT"
        --restart=unless-stopped
        "$NACOS_IMAGE"
    )

    if "${docker_run_cmd[@]}"; then
        log_info "✅ Nacos容器启动成功"
        return 0
    else
        log_error "Nacos容器启动失败"
        return 1
    fi
}

# 主安装函数
main() {
    log_title "开始安装Nacos ${NACOS_VERSION}"

    # 1. 检查依赖
    if ! check_docker_dependency; then
        return 1
    fi

    if ! check_mysql_dependency; then
        return 1
    fi

    if ! check_network_dependency; then
        return 1
    fi

    # 2. 检查是否已安装
    check_nacos_installed
    local install_status=$?
    case $install_status in
        0)
            log_info "✅ Nacos已安装并运行中"
            log_info "访问地址: http://${SERVER_IP}:${NACOS_HTTP_PORT}/nacos/"
            log_info "默认用户名/密码: nacos/nacos"
            return 0
            ;;
        2)
            log_info "Nacos已安装但未运行，尝试启动"
            if start_nacos; then
                return 0
            else
                log_warn "启动失败，将重新安装"
            fi
            ;;
    esac

    # 3. 创建目录结构
    if ! create_nacos_directories; then
        return 1
    fi

    # 4. 准备SQL文件
    if ! prepare_nacos_sql; then
        return 1
    fi

    # 5. 生成配置文件
    if ! generate_nacos_config; then
        return 1
    fi

    # 6. 初始化数据库
    if ! initialize_nacos_database; then
        return 1
    fi

    # 7. 加载Docker镜像
    if ! load_nacos_image; then
        return 1
    fi

    # 8. 启动容器
    if ! start_nacos_container; then
        return 1
    fi

    # 9. 等待服务启动
    log_step "等待Nacos服务启动"
    sleep 10

    # 10. 健康检查
    if perform_nacos_health_check; then
        log_success "🎉 Nacos ${NACOS_VERSION} 安装成功！"
        echo ""
        echo "📋 服务信息："
        echo "   • 访问地址: http://${SERVER_IP}:${NACOS_HTTP_PORT}/nacos/"
        echo "   • 默认用户名: nacos"
        echo "   • 默认密码: nacos"
        echo "   • 数据库: ${NACOS_DB_NAME}"
        echo "   • 配置目录: ${NACOS_DATA_DIR}/conf"
        echo "   • 日志目录: ${NACOS_DATA_DIR}/logs"
        echo ""
        echo "💡 提示："
        echo "   • 首次登录后请修改默认密码"
        echo "   • 配置文件位置: ${NACOS_DATA_DIR}/conf/application.properties"
        echo ""
        return 0
    else
        log_error "Nacos健康检查失败，请检查日志"
        return 1
    fi
}

# 卸载函数
uninstall_nacos() {
    log_title "卸载Nacos ${NACOS_VERSION}"

    echo ""
    echo "⚠️  警告: 此操作将："
    echo "   • 停止并删除Nacos容器"
    echo "   • 删除Nacos Docker镜像"
    echo "   • 保留配置文件和日志（可选择删除）"
    echo "   • 保留数据库数据（可选择删除）"
    echo ""

    echo -n "确认卸载Nacos? (y/N): "
    read -r confirm_uninstall
    if [[ ! "$confirm_uninstall" =~ ^[Yy]$ ]]; then
        log_info "卸载取消"
        return 0
    fi

    # 停止并删除容器
    log_step "停止并删除Nacos容器"
    if docker ps --format "{{.Names}}" | grep -q "^$NACOS_CONTAINER_NAME$"; then
        docker stop "$NACOS_CONTAINER_NAME"
        log_info "Nacos容器已停止"
    fi

    if docker ps -a --format "{{.Names}}" | grep -q "^$NACOS_CONTAINER_NAME$"; then
        docker rm "$NACOS_CONTAINER_NAME"
        log_info "Nacos容器已删除"
    fi

    # 删除Docker镜像
    echo -n "是否删除Nacos Docker镜像? (y/N): "
    read -r remove_image
    if [[ "$remove_image" =~ ^[Yy]$ ]]; then
        log_step "删除Nacos Docker镜像"
        if docker images --format "{{.Repository}}:{{.Tag}}" | grep -q "^$NACOS_IMAGE$"; then
            docker rmi "$NACOS_IMAGE"
            log_info "Nacos镜像已删除"
        fi
    fi

    # 删除数据库
    echo -n "是否删除Nacos数据库? (y/N): "
    read -r remove_database
    if [[ "$remove_database" =~ ^[Yy]$ ]]; then
        log_step "删除Nacos数据库"
        if docker exec "$MYSQL_CONTAINER_NAME" mysql -uroot -p"$MYSQL_PASSWORD" -e "DROP DATABASE IF EXISTS $NACOS_DB_NAME;" &> /dev/null; then
            log_info "Nacos数据库已删除"
        fi
    fi

    # 删除配置文件和日志
    echo -n "是否删除配置文件和日志? (y/N): "
    read -r remove_data
    if [[ "$remove_data" =~ ^[Yy]$ ]]; then
        log_step "删除配置文件和日志"
        if [[ -d "$NACOS_DATA_DIR" ]]; then
            rm -rf "$NACOS_DATA_DIR"
            log_info "Nacos数据目录已删除"
        fi
    fi

    log_success "✅ Nacos卸载完成"
    return 0
}

# ============================================================================
# 健康检查和服务管理函数
# ============================================================================

# 执行Nacos健康检查
perform_nacos_health_check() {
    log_step "执行Nacos健康检查"

    local max_attempts=30
    local attempt=1

    while [[ $attempt -le $max_attempts ]]; do
        log_info "健康检查尝试 $attempt/$max_attempts"

        # 检查容器是否运行
        if ! docker ps --format "{{.Names}}" | grep -q "^$NACOS_CONTAINER_NAME$"; then
            log_error "Nacos容器未运行"
            return 1
        fi

        # 检查HTTP端口
        if curl -f -s "http://localhost:${NACOS_HTTP_PORT}/nacos/actuator/health" &> /dev/null; then
            log_info "✅ Nacos HTTP服务健康检查通过"

            # 检查登录页面
            if curl -f -s "http://localhost:${NACOS_HTTP_PORT}/nacos/" | grep -q "Nacos"; then
                log_info "✅ Nacos Web控制台可访问"
                return 0
            fi
        fi

        log_info "等待Nacos服务启动... (${attempt}/${max_attempts})"
        sleep 5
        attempt=$((attempt + 1))
    done

    log_error "Nacos健康检查超时"
    log_info "请检查容器日志: docker logs $NACOS_CONTAINER_NAME"
    return 1
}

# 启动Nacos服务
start_nacos() {
    log_title "启动Nacos服务"

    # 检查容器是否存在
    if ! docker ps -a --format "{{.Names}}" | grep -q "^$NACOS_CONTAINER_NAME$"; then
        log_error "Nacos容器不存在，请先安装"
        return 1
    fi

    # 检查容器是否已运行
    if docker ps --format "{{.Names}}" | grep -q "^$NACOS_CONTAINER_NAME$"; then
        log_info "✅ Nacos服务已在运行"
        return 0
    fi

    # 启动容器
    log_step "启动Nacos容器"
    if docker start "$NACOS_CONTAINER_NAME"; then
        log_info "Nacos容器启动成功"

        # 等待服务启动
        sleep 10

        # 健康检查
        if perform_nacos_health_check; then
            log_success "✅ Nacos服务启动成功"
            echo "访问地址: http://${SERVER_IP}:${NACOS_HTTP_PORT}/nacos/"
            return 0
        else
            log_error "Nacos服务启动后健康检查失败"
            return 1
        fi
    else
        log_error "Nacos容器启动失败"
        return 1
    fi
}

# 停止Nacos服务
stop_nacos() {
    log_title "停止Nacos服务"

    # 检查容器是否运行
    if ! docker ps --format "{{.Names}}" | grep -q "^$NACOS_CONTAINER_NAME$"; then
        log_info "✅ Nacos服务已停止"
        return 0
    fi

    # 停止容器
    log_step "停止Nacos容器"
    if docker stop "$NACOS_CONTAINER_NAME"; then
        log_success "✅ Nacos服务停止成功"
        return 0
    else
        log_error "Nacos服务停止失败"
        return 1
    fi
}

# 重启Nacos服务
restart_nacos() {
    log_title "重启Nacos服务"

    if stop_nacos && start_nacos; then
        log_success "✅ Nacos服务重启成功"
        return 0
    else
        log_error "Nacos服务重启失败"
        return 1
    fi
}

# 显示Nacos状态
show_nacos_status() {
    log_title "Nacos服务状态"

    echo ""
    echo "📋 容器状态："
    if docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep "$NACOS_CONTAINER_NAME"; then
        echo ""
    else
        echo "   容器未运行"
    fi

    echo "🔗 访问信息："
    echo "   • Web控制台: http://${SERVER_IP}:${NACOS_HTTP_PORT}/nacos/"
    echo "   • HTTP端口: ${NACOS_HTTP_PORT}"
    echo "   • gRPC端口: ${NACOS_GRPC_PORT}, ${NACOS_GRPC_PORT2}"
    echo "   • 数据库: ${NACOS_DB_NAME}"
    echo ""

    echo "📁 目录信息："
    echo "   • 配置目录: ${NACOS_DATA_DIR}/conf"
    echo "   • 日志目录: ${NACOS_DATA_DIR}/logs"
    echo "   • 数据目录: ${NACOS_DATA_DIR}/data"
    echo ""
}

# 更新Nacos配置
update_nacos_config() {
    log_title "更新Nacos配置"
    
    # 检查Docker依赖
    if ! check_docker_dependency; then
        return 1
    fi
    
    # 检查MySQL依赖
    if ! check_mysql_dependency; then
        return 1
    fi
    
    # 检查Nacos容器是否存在
    if ! docker ps -a --format "{{.Names}}" | grep -q "^$NACOS_CONTAINER_NAME$"; then
        log_error "Nacos容器不存在，请先安装Nacos"
        return 1
    fi
    
    # 检查配置文件目录是否存在
    if [[ ! -d "$NACOS_DATA_DIR/conf" ]]; then
        log_error "Nacos配置目录不存在: $NACOS_DATA_DIR/conf"
        return 1
    fi
    
    # 备份现有配置
    local config_file="$NACOS_DATA_DIR/conf/application.properties"
    local backup_config="$NACOS_DATA_DIR/conf/application.properties.backup.$(date +%Y%m%d_%H%M%S)"
    
    if [[ -f "$config_file" ]]; then
        log_step "备份现有配置文件"
        cp "$config_file" "$backup_config"
        log_info "配置已备份到: $backup_config"
    fi
    
    # 记录容器运行状态
    local was_running=false
    if docker ps --format "{{.Names}}" | grep -q "^$NACOS_CONTAINER_NAME$"; then
        was_running=true
        log_info "Nacos容器当前正在运行"
    fi
    
    # 重新生成配置文件
    log_step "生成新的Nacos配置文件"
    if ! generate_nacos_config; then
        log_error "生成Nacos配置文件失败"
        # 恢复备份配置
        if [[ -f "$backup_config" ]]; then
            cp "$backup_config" "$config_file"
            log_info "已恢复备份配置"
        fi
        return 1
    fi
    
    # 如果容器正在运行，需要重新创建容器以应用新的环境变量
    if [[ "$was_running" == true ]]; then
        log_step "重新创建Nacos容器以应用新配置（包括环境变量）"
        log_warn "注意：Docker容器的环境变量只能在创建时设置，需要重新创建容器"

        # 停止并删除旧容器
        if docker stop "$NACOS_CONTAINER_NAME"; then
            log_info "Nacos容器已停止"
            sleep 3

            if docker rm "$NACOS_CONTAINER_NAME"; then
                log_info "旧Nacos容器已删除"
            else
                log_error "删除旧容器失败"
                return 1
            fi
        else
            log_error "停止Nacos容器失败"
            # 恢复备份配置
            if [[ -f "$backup_config" ]]; then
                cp "$backup_config" "$config_file"
                log_info "已恢复备份配置"
            fi
            return 1
        fi

        # 重新创建并启动容器（使用新的环境变量）
        if start_nacos_container; then
            log_info "新Nacos容器已创建并启动"

            # 等待服务启动
            sleep 15

            # 健康检查
            if perform_nacos_health_check; then
                log_success "✅ Nacos配置更新成功，新配置和环境变量已生效"

                # 显示新配置内容摘要
                log_step "当前Nacos配置摘要："
                if [[ -f "$config_file" ]]; then
                    echo "数据库地址: $(grep "^db.url.0" "$config_file" | cut -d'=' -f2- | cut -d'/' -f3)"
                    echo "认证启用: $(grep "^nacos.core.auth.enabled" "$config_file" | cut -d'=' -f2)"
                    echo "服务端口: $(grep "^server.port" "$config_file" | cut -d'=' -f2)"
                    echo "配置文件路径: $config_file"
                fi

                # 验证环境变量是否生效
                log_step "验证环境变量配置："
                docker logs "$NACOS_CONTAINER_NAME" 2>&1 | grep -E "(auth\.enabled|NACOS_AUTH_ENABLE)" | tail -3

                return 0
            else
                log_error "Nacos服务启动后健康检查失败，恢复备份配置"
                # 停止并删除失败的容器
                docker stop "$NACOS_CONTAINER_NAME" >/dev/null 2>&1
                docker rm "$NACOS_CONTAINER_NAME" >/dev/null 2>&1

                # 恢复备份配置
                if [[ -f "$backup_config" ]]; then
                    cp "$backup_config" "$config_file"
                    log_info "已恢复备份配置"

                    # 重新创建容器使用备份配置
                    start_nacos_container >/dev/null 2>&1
                    log_info "已使用备份配置重新创建容器"
                fi
                return 1
            fi
        else
            log_error "创建新Nacos容器失败，恢复备份配置"
            # 恢复备份配置
            if [[ -f "$backup_config" ]]; then
                cp "$backup_config" "$config_file"
                start_nacos_container >/dev/null 2>&1
                log_info "已恢复备份配置并重新创建容器"
            fi
            return 1
        fi
    else
        log_info "✅ Nacos配置文件更新完成"
        log_warn "Nacos容器未运行，环境变量配置需要重新创建容器才能生效"
        log_info "建议操作："
        echo "  1. 删除旧容器: docker rm $NACOS_CONTAINER_NAME"
        echo "  2. 重新安装: 运行安装脚本重新创建容器"
        echo "  或者直接启动: docker start $NACOS_CONTAINER_NAME (仅配置文件生效)"

        # 显示新配置内容摘要
        log_step "新配置内容摘要："
        if [[ -f "$config_file" ]]; then
            echo "数据库地址: $(grep "^db.url.0" "$config_file" | cut -d'=' -f2- | cut -d'/' -f3)"
            echo "认证启用: $(grep "^nacos.core.auth.enabled" "$config_file" | cut -d'=' -f2)"
            echo "服务端口: $(grep "^server.port" "$config_file" | cut -d'=' -f2)"
            echo "配置文件路径: $config_file"
        fi
        
        return 0
    fi
}

# 显示帮助信息
show_help() {
    echo "Nacos ${NACOS_VERSION} 安装脚本使用说明:"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  (无参数)          执行Nacos安装"
    echo "  --check-installed 检查Nacos是否已安装"
    echo "  --check-packages  检查Nacos安装包完整性"
    echo "  --install         执行Nacos安装"
    echo "  --uninstall       卸载Nacos"
    echo "  --start           启动Nacos服务"
    echo "  --stop            停止Nacos服务"
    echo "  --restart         重启Nacos服务"
    echo "  --update-config   更新Nacos配置文件"
    echo "  --status          显示Nacos状态"
    echo "  --health-check    执行健康检查"
    echo "  --help            显示此帮助信息"
    echo ""
    echo "说明:"
    echo "  • --update-config会重新生成application.properties并重启容器"
    echo "  • 配置更新前会自动备份现有配置文件"
    echo ""
    echo "示例:"
    echo "  $0                # 安装Nacos"
    echo "  $0 --start        # 启动Nacos服务"
    echo "  $0 --status       # 查看服务状态"
    echo ""
}

# 处理命令行参数
handle_arguments() {
    case "$1" in
        --check-installed)
            check_nacos_installed_status
            ;;
        --check-packages)
            check_nacos_packages
            ;;
        --install)
            main
            ;;
        --uninstall)
            uninstall_nacos
            ;;
        --start)
            start_nacos
            ;;
        --stop)
            stop_nacos
            ;;
        --restart)
            restart_nacos
            ;;
        --update-config)
            update_nacos_config
            ;;
        --status)
            show_nacos_status
            ;;
        --health-check)
            perform_nacos_health_check
            ;;
        --help)
            show_help
            ;;
        "")
            main
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 错误处理
trap 'echo -e "\n❌ Nacos脚本执行过程中出现错误"; exit 1' ERR

# 执行参数处理
handle_arguments "$@"