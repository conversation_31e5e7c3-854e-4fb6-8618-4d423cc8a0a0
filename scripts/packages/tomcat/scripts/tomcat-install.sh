#!/bin/bash

# CentOS 7兼容性设置
if [[ ${BASH_VERSION%%.*} -lt 4 ]]; then
    echo "错误: 需要bash 4.0或更高版本，当前版本: $BASH_VERSION"
    exit 1
fi

#=============================================================================
# Tomcat 11.0.9安装模块
# 版本: 1.0
# 用途: 在Docker环境中安装Tomcat 11.0.9
# 依赖: packages/tomcat/tomcat-9.0.107.tar
# 
# 重要提醒: install.sh只需要确保整体流程OK，主要负责用户操作，
#          具体逻辑由各自组件的Install来完成！！！
#=============================================================================

# 移除 set -e 以避免状态检查时脚本中断
# set -e

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BASE_DIR="$(dirname "$SCRIPT_DIR")"                        # packages/tomcat
PACKAGES_DIR="$(dirname "$BASE_DIR")"                      # packages
ROOT_DIR="$(dirname "$PACKAGES_DIR")"                      # scripts

# 加载配置文件
if [[ -f "$ROOT_DIR/config.sh" ]]; then
    source "$ROOT_DIR/config.sh"
else
    echo "配置文件不存在: $ROOT_DIR/config.sh"
    exit 1
fi

# 基本的日志函数
log_error() { echo -e "\033[0;31m[ERROR]\033[0m $1"; }
log_info() { echo -e "\033[0;32m[INFO]\033[0m $1"; }

# 检查Tomcat是否已安装
check_tomcat_installed() {
    # 先检查Docker是否可用
    if ! command -v docker &> /dev/null; then
        return 1  # Docker未安装，Tomcat肯定未安装
    fi
    
    if ! systemctl is-active docker &> /dev/null; then
        return 1  # Docker服务未运行，无法检查Tomcat状态
    fi
    
    # 检查配置变量是否已加载
    if [[ -z "$TOMCAT_CONTAINER_NAME" ]]; then
        return 1  # 配置未加载，返回未安装
    fi
    
    # 检查Tomcat容器状态
    if docker ps --format "{{.Names}}" 2>/dev/null | grep -q "^$TOMCAT_CONTAINER_NAME$"; then
        return 0  # 已安装并运行
    elif docker ps -a --format "{{.Names}}" 2>/dev/null | grep -q "^$TOMCAT_CONTAINER_NAME$"; then
        return 2  # 已安装但未运行
    else
        return 1  # 未安装
    fi
}

# 检查Tomcat安装状态（供主脚本调用）
check_tomcat_installed_status() {
    check_tomcat_installed
    exit $?
}

# 更新Tomcat配置（未实现）
update_tomcat_config() {
    log_error "Tomcat配置更新功能尚未实现"
    log_info "当前Tomcat组件还在开发中，配置更新功能将在后续版本中提供"
    log_info "请等待后续版本更新"
    return 1
}

# 显示帮助信息
show_help() {
    echo "Tomcat 11.0.9 安装脚本使用说明:"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  (无参数)          执行Tomcat安装（未实现）"
    echo "  --check-installed 检查Tomcat是否已安装"
    echo "  --update-config   更新Tomcat配置文件（未实现）"
    echo "  --help            显示此帮助信息"
    echo ""
    echo "说明:"
    echo "  • 该组件目前尚未完全实现，敬请期待后续版本"
    echo "  • --update-config功能将在后续版本中提供"
    echo ""
}

# 主安装函数（未实现）
main() {
    log_info "Tomcat 11.0.9安装功能尚未实现"
    log_info "请等待后续版本更新"
    exit 1
}

# 处理命令行参数
handle_arguments() {
    case "$1" in
        --check-installed)
            check_tomcat_installed_status
            ;;
        --update-config)
            update_tomcat_config
            ;;
        --help)
            show_help
            ;;
        *)
            main "$@"
            ;;
    esac
}

# 执行参数处理
handle_arguments "$@"