#!/bin/bash

# CentOS 7兼容性设置
if [[ ${BASH_VERSION%%.*} -lt 4 ]]; then
    echo "错误: 需要bash 4.0或更高版本，当前版本: $BASH_VERSION"
    exit 1
fi

#=============================================================================
# MySQL 8.0.41安装模块
# 版本: 1.0
# 用途: 在Docker环境中安装MySQL 8.0.41
# 依赖: packages/mysql/mysql-8.0.41.tar
#=============================================================================

# 移除 set -e 以避免状态检查时脚本中断
# set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_title() {
    echo -e "${BLUE}[TITLE]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BASE_DIR="$(dirname "$SCRIPT_DIR")"                        # packages/mysql
PACKAGES_DIR="$(dirname "$BASE_DIR")"                      # packages
ROOT_DIR="$(dirname "$PACKAGES_DIR")"                      # scripts

# 加载配置文件
if [[ -f "$ROOT_DIR/config.sh" ]]; then
    source "$ROOT_DIR/config.sh"
else
    log_error "配置文件不存在: $ROOT_DIR/config.sh"
    exit 1
fi

# 检查Docker是否可用
check_docker_available() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        return 1
    fi
    
    if ! systemctl is-active docker &> /dev/null; then
        log_error "Docker服务未运行，请先启动Docker服务"
        return 1
    fi
    
    return 0
}

# 检查MySQL是否已安装
check_mysql_installed() {
    # 先检查Docker是否可用，如果不可用则返回未安装状态
    if ! command -v docker &> /dev/null; then
        return 1  # Docker未安装，MySQL肯定未安装
    fi
    
    if ! systemctl is-active docker &> /dev/null; then
        return 1  # Docker服务未运行，无法检查MySQL状态，返回未安装
    fi
    
    # 检查配置变量是否已加载
    if [[ -z "$MYSQL_CONTAINER_NAME" ]]; then
        return 1  # 配置未加载，返回未安装
    fi
    
    # 检查MySQL容器状态
    if docker ps --format "table {{.Names}}" 2>/dev/null | grep -q "^$MYSQL_CONTAINER_NAME$"; then
        return 0  # 已安装并运行
    elif docker ps -a --format "table {{.Names}}" 2>/dev/null | grep -q "^$MYSQL_CONTAINER_NAME$"; then
        return 2  # 已安装但未运行
    else
        return 1  # 未安装
    fi
}

# 检查MySQL镜像
check_mysql_image() {
    log_title "检查MySQL镜像"
    
    local mysql_tar="$BASE_DIR/mysql-${MYSQL_VERSION}.tar"
    
    # 检查Docker镜像是否存在
    if docker images --format "table {{.Repository}}:{{.Tag}}" | grep -q "^$MYSQL_IMAGE$"; then
        log_info "MySQL镜像已存在: $MYSQL_IMAGE"
        return 0
    fi
    
    # 检查镜像文件是否存在
    if [[ ! -f "$mysql_tar" ]]; then
        log_error "MySQL镜像文件不存在: $mysql_tar"
        log_info "请先下载MySQL镜像文件"
        return 1
    fi
    
    local file_size
    file_size=$(du -h "$mysql_tar" | cut -f1)
    log_info "找到MySQL镜像文件: mysql-${MYSQL_VERSION}.tar ($file_size)"
    
    # 加载镜像
    log_step "加载MySQL镜像"
    if docker load -i "$mysql_tar"; then
        log_info "MySQL镜像加载成功"
        return 0
    else
        log_error "MySQL镜像加载失败"
        return 1
    fi
}

# 创建MySQL配置文件
create_mysql_config() {
    log_title "创建MySQL配置文件"

    # 创建数据目录
    log_step "创建MySQL数据目录"
    mkdir -p "$MYSQL_DATA_DIR"/{conf,data,logs}

    # 创建MySQL配置文件（根据手书和config.sh配置）
    log_step "生成MySQL配置文件"
    cat > "$MYSQL_DATA_DIR/conf/my.cnf" << EOF
[mysqld]
# 基本配置
user = mysql
port = 3306
server-id = 1
socket = /var/run/mysqld/mysqld.sock
pid-file = /var/run/mysqld/mysqld.pid

# 数据目录
datadir = /var/lib/mysql

# 网络配置
bind-address = 0.0.0.0
skip-name-resolve = ON

# 字符集配置（从config.sh读取）
character-set-server = $MYSQL_CHARACTER_SET
collation-server = $MYSQL_COLLATION
init_connect = 'SET NAMES $MYSQL_CHARACTER_SET'

# 表名大小写配置（从config.sh读取）
lower_case_table_names = $MYSQL_LOWER_CASE_TABLE_NAMES

# InnoDB配置（从config.sh读取）
default-storage-engine = INNODB
innodb_buffer_pool_size = $MYSQL_INNODB_BUFFER_POOL_SIZE
innodb_log_file_size = $MYSQL_INNODB_LOG_FILE_SIZE
innodb_file_per_table = ON
innodb_flush_log_at_trx_commit = 1

# 日志配置
log-error = /var/log/mysql/error.log
slow_query_log = ON
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2

# 连接配置（从config.sh读取）
max_connections = $MYSQL_MAX_CONNECTIONS
max_connect_errors = 100000

# SQL模式（从config.sh读取）
sql_mode = $MYSQL_SQL_MODE

[mysql]
default-character-set = $MYSQL_CHARACTER_SET

[client]
default-character-set = $MYSQL_CHARACTER_SET
EOF

    log_info "MySQL配置文件创建完成: $MYSQL_DATA_DIR/conf/my.cnf"

    # 设置目录权限
    log_step "设置目录权限"
    chmod -R 755 "$MYSQL_DATA_DIR"

    # 显示配置信息
    if [[ -n "$DEBUG" ]]; then
        log_step "MySQL配置信息："
        echo "   容器名: $MYSQL_CONTAINER_NAME"
        echo "   版本: $MYSQL_VERSION"
        echo "   端口: $MYSQL_PORT"
        echo "   数据目录: $MYSQL_DATA_DIR"
        echo "   字符集: $MYSQL_CHARACTER_SET"
        echo "   最大连接数: $MYSQL_MAX_CONNECTIONS"
    fi
}

# 启动MySQL容器
start_mysql_container() {
    log_title "启动MySQL容器"

    # 检查网络是否存在
    if ! docker network ls | grep -q "$DOCKER_NETWORK"; then
        log_step "创建Docker网络: $DOCKER_NETWORK"
        docker network create "$DOCKER_NETWORK"
    fi

    # 启动MySQL容器（根据手书和config.sh配置）
    log_step "启动MySQL $MYSQL_VERSION 容器"

    # 构建docker run命令
    local docker_cmd="docker run -d"
    docker_cmd="$docker_cmd --name $MYSQL_CONTAINER_NAME"
    docker_cmd="$docker_cmd --network $DOCKER_NETWORK"
    docker_cmd="$docker_cmd -p $MYSQL_PORT:3306"

    # 环境变量配置（关键：MYSQL_ROOT_HOST=%自动配置远程访问）
    docker_cmd="$docker_cmd -e MYSQL_ROOT_PASSWORD=$MYSQL_PASSWORD"
    docker_cmd="$docker_cmd -e MYSQL_ROOT_HOST=%"
    docker_cmd="$docker_cmd -e MYSQL_CHARACTER_SET_SERVER=$MYSQL_CHARACTER_SET"
    docker_cmd="$docker_cmd -e MYSQL_COLLATION_SERVER=$MYSQL_COLLATION"
    docker_cmd="$docker_cmd -e MYSQL_DATABASE=$MYSQL_DATABASE"

    # 数据卷挂载
    docker_cmd="$docker_cmd -v $MYSQL_DATA_DIR/conf/my.cnf:/etc/mysql/my.cnf:ro"
    docker_cmd="$docker_cmd -v $MYSQL_DATA_DIR/data:/var/lib/mysql"
    docker_cmd="$docker_cmd -v $MYSQL_DATA_DIR/logs:/var/log/mysql"

    # 资源限制（从config.sh读取）
    if [[ -n "$MYSQL_MEMORY_LIMIT" ]]; then
        docker_cmd="$docker_cmd --memory=$MYSQL_MEMORY_LIMIT"
    fi
    if [[ -n "$MYSQL_CPU_LIMIT" ]]; then
        docker_cmd="$docker_cmd --cpus=$MYSQL_CPU_LIMIT"
    fi

    # 重启策略和镜像
    docker_cmd="$docker_cmd --restart unless-stopped"
    docker_cmd="$docker_cmd $MYSQL_IMAGE"

    # 认证插件命令（根据手书要求）
    docker_cmd="$docker_cmd --default-authentication-plugin=mysql_native_password"

    # 执行启动命令
    if [[ -n "$DEBUG" ]]; then
        log_step "执行命令: $docker_cmd"
    fi

    eval "$docker_cmd"

    if [[ $? -eq 0 ]]; then
        log_info "MySQL容器启动成功"
    else
        log_error "MySQL容器启动失败"
        return 1
    fi
}

# 等待MySQL启动
wait_for_mysql() {
    log_title "等待MySQL启动"
    
    local retry_count=0
    local max_retries=30
    
    log_step "等待MySQL服务就绪 (最多等待${max_retries}秒)"
    
    while [[ $retry_count -lt $max_retries ]]; do
        # 关键修复：不使用密码检测，因为MySQL可能还在初始化中
        if docker exec "$MYSQL_CONTAINER_NAME" mysqladmin ping -h localhost --silent 2>/dev/null; then
            log_info "✅ MySQL服务就绪"
            return 0
        fi
        
        retry_count=$((retry_count + 1))
        echo -n "."
        sleep 1
    done
    
    echo ""
    log_error "MySQL启动超时"
    return 1
}

# 配置MySQL权限（简化版，利用Docker自动配置）
configure_mysql_permissions() {
    log_title "配置MySQL权限"

    # 由于设置了MYSQL_ROOT_HOST=%，Docker已自动配置远程访问
    log_step "验证远程访问权限（Docker自动配置）"

    # 简单验证连接是否正常
    if docker exec "$MYSQL_CONTAINER_NAME" mysql -u root -p"$MYSQL_PASSWORD" -e "SELECT 'MySQL连接正常' as status;" >/dev/null 2>&1; then
        log_info "✅ MySQL连接验证成功"
    else
        log_warn "⚠️  MySQL连接验证失败，但这可能是正常的"
        log_info "Docker的MYSQL_ROOT_HOST=%会自动配置远程访问权限"
    fi

    # 创建Skyeye数据库（如果需要）
    if [[ -n "$MYSQL_DATABASE" ]]; then
        log_step "创建Skyeye数据库"
        if docker exec "$MYSQL_CONTAINER_NAME" mysql -u root -p"$MYSQL_PASSWORD" -e "
            CREATE DATABASE IF NOT EXISTS $MYSQL_DATABASE DEFAULT CHARACTER SET $MYSQL_CHARACTER_SET COLLATE $MYSQL_COLLATION;
            SHOW DATABASES LIKE '$MYSQL_DATABASE';
        " 2>/dev/null | grep -q "$MYSQL_DATABASE"; then
            log_info "✅ Skyeye数据库创建成功"
        else
            log_warn "⚠️  Skyeye数据库创建可能失败，但不影响基本功能"
        fi
    fi

    # 设置一些有用的MySQL参数
    log_step "配置MySQL参数"
    docker exec "$MYSQL_CONTAINER_NAME" mysql -u root -p"$MYSQL_PASSWORD" -e "
        SET GLOBAL log_bin_trust_function_creators = 1;
    " 2>/dev/null || log_warn "MySQL参数配置失败（可忽略）"

    log_info "✅ MySQL权限配置完成"
    log_info "💡 提示：MYSQL_ROOT_HOST=%已自动配置root用户的远程访问权限"
}

# 验证MySQL安装
verify_mysql_installation() {
    log_title "验证MySQL安装"
    
    # 检查容器状态
    log_step "检查容器状态"
    if docker ps --format "table {{.Names}}\t{{.Status}}" | grep "$MYSQL_CONTAINER_NAME"; then
        log_info "✅ MySQL容器运行正常"
    else
        log_error "❌ MySQL容器状态异常"
        return 1
    fi
    
    # 检查MySQL服务连通性（不使用密码）
    log_step "检查MySQL服务连通性"
    if docker exec "$MYSQL_CONTAINER_NAME" mysqladmin ping -h localhost --silent 2>/dev/null; then
        log_info "✅ MySQL服务连通性正常"
    else
        log_error "❌ MySQL服务连通性失败"
        return 1
    fi

    # 检查MySQL版本和认证
    log_step "检查MySQL版本和认证"
    local mysql_version
    mysql_version=$(docker exec "$MYSQL_CONTAINER_NAME" mysql -u root -p"$MYSQL_PASSWORD" -e "SELECT VERSION();" --silent --skip-column-names 2>/dev/null)
    if [[ -n "$mysql_version" ]]; then
        log_info "✅ MySQL版本: $mysql_version"
        log_info "✅ MySQL认证正常"
    else
        log_warn "⚠️  MySQL版本检查失败，可能仍在初始化中"
        # 给MySQL更多时间初始化
        log_step "等待MySQL完全初始化..."
        sleep 5
        mysql_version=$(docker exec "$MYSQL_CONTAINER_NAME" mysql -u root -p"$MYSQL_PASSWORD" -e "SELECT VERSION();" --silent --skip-column-names 2>/dev/null)
        if [[ -n "$mysql_version" ]]; then
            log_info "✅ MySQL版本: $mysql_version"
        else
            log_warn "⚠️  MySQL认证仍有问题，但容器运行正常"
        fi
    fi

    # 检查数据库访问
    log_step "检查数据库访问"
    if docker exec "$MYSQL_CONTAINER_NAME" mysql -u root -p"$MYSQL_PASSWORD" -e "SHOW DATABASES;" >/dev/null 2>&1; then
        log_info "✅ MySQL数据库访问正常"

        # 检查远程访问权限
        log_step "检查远程访问权限"
        local remote_users
        remote_users=$(docker exec "$MYSQL_CONTAINER_NAME" mysql -u root -p"$MYSQL_PASSWORD" -e "SELECT Host FROM mysql.user WHERE User='root' AND Host='%';" --silent --skip-column-names 2>/dev/null)
        if [[ -n "$remote_users" ]]; then
            log_info "✅ 远程访问权限配置正确"
        else
            log_warn "⚠️  远程访问权限可能未正确配置"
        fi

        # 检查Skyeye数据库
        if [[ -n "$MYSQL_DATABASE" ]]; then
            log_step "检查Skyeye数据库"
            if docker exec "$MYSQL_CONTAINER_NAME" mysql -u root -p"$MYSQL_PASSWORD" -e "SHOW DATABASES LIKE '$MYSQL_DATABASE';" --silent --skip-column-names 2>/dev/null | grep -q "$MYSQL_DATABASE"; then
                log_info "✅ Skyeye数据库存在"
            else
                log_warn "⚠️  Skyeye数据库不存在"
            fi
        fi
    else
        log_error "❌ MySQL数据库访问异常"
        log_info "查看MySQL日志："
        docker logs "$MYSQL_CONTAINER_NAME" | tail -10
        return 1
    fi
    
    log_info "✅ MySQL安装验证完成"
    return 0
}

# MySQL诊断函数（参考非Docker脚本）
diagnose_mysql_issues() {
    log_title "MySQL状态诊断"

    # 1. 检查容器状态
    log_step "1. 容器状态检查"
    if docker ps | grep -q "$MYSQL_CONTAINER_NAME"; then
        log_info "✅ MySQL容器正在运行"
        local container_status=$(docker ps --format "table {{.Status}}" --filter name="$MYSQL_CONTAINER_NAME" | tail -n 1)
        log_info "   容器状态: $container_status"
    else
        log_error "❌ MySQL容器未运行"
        if docker ps -a | grep -q "$MYSQL_CONTAINER_NAME"; then
            log_warn "   容器存在但已停止"
            docker ps -a --filter name="$MYSQL_CONTAINER_NAME"
        else
            log_error "   容器不存在"
        fi
    fi

    # 2. 检查端口监听
    log_step "2. 端口监听检查"
    if docker port "$MYSQL_CONTAINER_NAME" 2>/dev/null | grep -q "3306"; then
        log_info "✅ MySQL端口映射正常"
        docker port "$MYSQL_CONTAINER_NAME"
    else
        log_warn "⚠️  MySQL端口映射异常"
    fi

    # 3. 检查容器日志
    log_step "3. 容器日志检查"
    log_info "最近的MySQL日志："
    docker logs "$MYSQL_CONTAINER_NAME" --tail 20 2>/dev/null || log_warn "无法获取容器日志"

    # 4. 检查网络连接
    log_step "4. 网络连接检查"
    if docker network ls | grep -q "$DOCKER_NETWORK"; then
        log_info "✅ Docker网络存在: $DOCKER_NETWORK"
    else
        log_warn "⚠️  Docker网络不存在: $DOCKER_NETWORK"
    fi

    # 5. 检查数据目录
    log_step "5. 数据目录检查"
    if [[ -d "$MYSQL_DATA_DIR" ]]; then
        log_info "✅ 数据目录存在: $MYSQL_DATA_DIR"
        local dir_size=$(du -sh "$MYSQL_DATA_DIR" 2>/dev/null | cut -f1)
        log_info "   目录大小: $dir_size"
    else
        log_warn "⚠️  数据目录不存在: $MYSQL_DATA_DIR"
    fi

    # 6. 检查磁盘空间
    log_step "6. 磁盘空间检查"
    local disk_usage=$(df -h "$MYSQL_DATA_DIR" 2>/dev/null | tail -1 | awk '{print $5}' | sed 's/%//')
    if [[ -n "$disk_usage" ]] && [[ "$disk_usage" -lt 90 ]]; then
        log_info "✅ 磁盘使用率: ${disk_usage}%"
    else
        log_warn "⚠️  磁盘使用率: ${disk_usage}%（可能不足）"
    fi
}

# 显示安装后信息
show_post_install_info() {
    log_title "MySQL安装完成"

    local server_ip
    server_ip=$(hostname -I | awk '{print $1}')

    echo ""
    echo "✅ MySQL $MYSQL_VERSION 安装完成！"
    echo ""
    echo "📋 连接信息："
    echo "   • 服务器地址: $server_ip"
    echo "   • 端口: $MYSQL_PORT"
    echo "   • 用户名: root"
    echo "   • 密码: $MYSQL_PASSWORD"
    echo "   • 字符集: $MYSQL_CHARACTER_SET"
    echo "   • 连接字符串: mysql://root:$MYSQL_PASSWORD@$server_ip:$MYSQL_PORT/"
    echo ""
    echo "📂 数据目录："
    echo "   • 配置文件: $MYSQL_DATA_DIR/conf/my.cnf"
    echo "   • 数据目录: $MYSQL_DATA_DIR/data"
    echo "   • 日志目录: $MYSQL_DATA_DIR/logs"
    echo ""
    echo "🔧 常用命令："
    echo "   • 进入MySQL: docker exec -it $MYSQL_CONTAINER_NAME mysql -u root -p"
    echo "   • 查看日志: docker logs $MYSQL_CONTAINER_NAME"
    echo "   • 重启服务: docker restart $MYSQL_CONTAINER_NAME"
    echo "   • 停止服务: docker stop $MYSQL_CONTAINER_NAME"
    echo ""
    echo "🔗 验证连接："
    echo "   • 本地验证: docker exec -it $MYSQL_CONTAINER_NAME mysql -uroot -p$MYSQL_PASSWORD -e \"SELECT VERSION();\""
    echo "   • 远程验证: mysql -h$server_ip -P$MYSQL_PORT -uroot -p$MYSQL_PASSWORD"
    echo ""
    echo "📊 服务状态："
    echo "   • 容器状态: $(docker ps --format 'table {{.Status}}' --filter name=$MYSQL_CONTAINER_NAME | tail -n 1)"
    echo "   • 网络: $DOCKER_NETWORK"
    if [[ -n "$MYSQL_DATABASE" ]]; then
        echo "   • 默认数据库: $MYSQL_DATABASE"
    fi
    echo ""
}

# 卸载MySQL
uninstall_mysql() {
    log_title "MySQL卸载"
    
    echo ""
    echo "⚠️  警告: 此操作将卸载MySQL，包括："
    echo "   • 停止并删除MySQL容器"
    echo "   • 删除MySQL镜像"
    echo "   • 可选择删除数据目录"
    echo ""
    
    echo -n "确认卸载MySQL? (y/N): "
    read -r confirm_uninstall
    if [[ ! "$confirm_uninstall" =~ ^[Yy]$ ]]; then
        log_info "卸载取消"
        return 0
    fi
    
    # 停止并删除容器
    log_step "停止并删除MySQL容器"
    if docker ps -a --format "table {{.Names}}" | grep -q "^$MYSQL_CONTAINER_NAME$"; then
        docker stop "$MYSQL_CONTAINER_NAME" 2>/dev/null || true
        docker rm "$MYSQL_CONTAINER_NAME" 2>/dev/null || true
        log_info "MySQL容器已删除"
    else
        log_info "MySQL容器不存在，跳过"
    fi
    
    # 删除镜像
    log_step "删除MySQL镜像"
    if docker images --format "table {{.Repository}}:{{.Tag}}" | grep -q "^$MYSQL_IMAGE$"; then
        docker rmi "$MYSQL_IMAGE" 2>/dev/null || true
        log_info "MySQL镜像已删除"
    else
        log_info "MySQL镜像不存在，跳过"
    fi
    
    # 询问是否删除数据目录
    echo ""
    echo -n "是否删除MySQL数据目录? (y/N): "
    read -r delete_data
    if [[ "$delete_data" =~ ^[Yy]$ ]]; then
        if [[ -d "$MYSQL_DATA_DIR" ]]; then
            rm -rf "$MYSQL_DATA_DIR"
            log_info "MySQL数据目录已删除: $MYSQL_DATA_DIR"
        fi
    else
        log_info "保留MySQL数据目录: $MYSQL_DATA_DIR"
    fi
    
    log_info "✅ MySQL卸载完成"
}

# 启动MySQL服务
start_mysql_service() {
    log_title "启动MySQL服务"

    # 检查容器是否存在
    if ! docker ps -a | grep -q "$MYSQL_CONTAINER_NAME"; then
        log_error "MySQL容器不存在，请先安装MySQL"
        return 1
    fi

    # 检查容器是否已经运行
    if docker ps | grep -q "$MYSQL_CONTAINER_NAME"; then
        log_info "MySQL容器已经在运行"
        return 0
    fi

    # 启动容器
    log_step "启动MySQL容器"
    if docker start "$MYSQL_CONTAINER_NAME"; then
        log_info "MySQL容器启动成功"

        # 等待服务就绪
        if wait_for_mysql; then
            log_info "✅ MySQL服务启动完成"
            return 0
        else
            log_error "MySQL服务启动超时"
            return 1
        fi
    else
        log_error "MySQL容器启动失败"
        return 1
    fi
}

# 停止MySQL服务
stop_mysql_service() {
    log_title "停止MySQL服务"

    # 检查容器是否运行
    if ! docker ps | grep -q "$MYSQL_CONTAINER_NAME"; then
        log_info "MySQL容器未运行"
        return 0
    fi

    # 停止容器
    log_step "停止MySQL容器"
    if docker stop "$MYSQL_CONTAINER_NAME"; then
        log_info "✅ MySQL服务停止成功"
        return 0
    else
        log_error "MySQL服务停止失败"
        return 1
    fi
}

# 检查MySQL安装状态（供主脚本调用）
check_mysql_installed_status() {
    check_mysql_installed
    exit $?
}

# 检查MySQL相关包（供主脚本调用）
check_mysql_packages() {
    local errors=0
    
    # 检查MySQL Docker镜像文件
    local mysql_tar="$BASE_DIR/mysql-${MYSQL_VERSION}.tar"
    if [[ -f "$mysql_tar" ]]; then
        local file_size
        file_size=$(du -h "$mysql_tar" | cut -f1)
        echo "✓ MySQL ${MYSQL_VERSION} Docker镜像 ($file_size)"
        
        # 检查文件大小（MySQL 8.0镜像应该比较大）
        local size_bytes=$(stat -c%s "$mysql_tar")
        local size_mb=$((size_bytes / 1024 / 1024))
        if [[ $size_mb -lt 300 ]]; then
            echo "⚠ MySQL镜像文件似乎过小 (${size_mb}MB)，请检查完整性"
            errors=$((errors + 1))
        fi
    else
        echo "✗ 缺失: MySQL ${MYSQL_VERSION} Docker镜像"
        errors=$((errors + 1))
    fi
    
    # 检查MySQL配置文件（可选）
    local mysql_config="$BASE_DIR/my.cnf"
    if [[ -f "$mysql_config" ]]; then
        echo "✓ MySQL配置文件"
    else
        echo "- MySQL配置文件 (可选，将使用默认配置)"
    fi
    
    # 检查脚本本身
    if [[ -x "${BASH_SOURCE[0]}" ]]; then
        echo "✓ MySQL安装脚本具有执行权限"
    else
        echo "⚠ MySQL安装脚本缺少执行权限"
        errors=$((errors + 1))
    fi
    
    exit $errors
}

# 显示帮助信息
show_help() {
    echo "MySQL安装脚本使用说明:"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  (无参数)          执行MySQL安装"
    echo "  --check-installed 检查MySQL是否已安装"
    echo "  --check-packages  检查MySQL安装包"
    echo "  --start           启动MySQL服务"
    echo "  --stop            停止MySQL服务"
    echo "  --update-config   更新MySQL配置文件"
    echo "  --uninstall       卸载MySQL"
    echo "  --diagnose        诊断MySQL问题"
    echo "  --help           显示此帮助信息"
    echo ""
    echo "说明:"
    echo "  • --update-config会重新生成my.cnf并重启容器"
    echo "  • 配置更新前会自动备份现有配置文件"
    echo ""
}

# 主安装函数
main() {
    log_title "MySQL 8.0.41安装开始"
    echo "目标版本: MySQL 8.0.41"
    echo "安装方式: Docker容器"
    echo ""
    
    # 检查Docker环境
    if ! check_docker_available; then
        exit 1
    fi
    
    # 检查是否已安装
    check_mysql_installed
    local install_status=$?
    
    case $install_status in
        0)
            log_info "MySQL已安装并运行中"
            echo -n "是否重新安装? (y/N): "
            read -r reinstall
            if [[ ! "$reinstall" =~ ^[Yy]$ ]]; then
                log_info "跳过MySQL安装"
                show_post_install_info
                return 0
            fi
            # 先卸载再重装
            uninstall_mysql
            ;;
        2)
            log_info "MySQL已安装但未运行"
            echo -n "是否启动现有容器? (Y/n): "
            read -r start_existing
            if [[ ! "$start_existing" =~ ^[Nn]$ ]]; then
                docker start "$MYSQL_CONTAINER_NAME"
                log_info "MySQL容器已启动"
                show_post_install_info
                return 0
            fi
            ;;
        1)
            log_info "MySQL未安装，开始安装"
            ;;
    esac
    
    # 执行安装步骤
    check_mysql_image || exit 1
    create_mysql_config
    start_mysql_container || exit 1
    wait_for_mysql || exit 1
    configure_mysql_permissions
    verify_mysql_installation || exit 1
    show_post_install_info
    
    log_title "MySQL安装完成"
}

# 更新MySQL配置
update_mysql_config() {
    log_title "更新MySQL配置"
    
    # 检查Docker是否可用
    if ! check_docker_available; then
        return 1
    fi
    
    # 检查MySQL容器是否存在
    if ! docker ps -a --format "table {{.Names}}" | grep -q "^$MYSQL_CONTAINER_NAME$"; then
        log_error "MySQL容器不存在，请先安装MySQL"
        return 1
    fi
    
    # 检查配置文件目录是否存在
    if [[ ! -d "$MYSQL_DATA_DIR/conf" ]]; then
        log_error "MySQL配置目录不存在: $MYSQL_DATA_DIR/conf"
        return 1
    fi
    
    # 备份现有配置
    local config_file="$MYSQL_DATA_DIR/conf/my.cnf"
    local backup_config="$MYSQL_DATA_DIR/conf/my.cnf.backup.$(date +%Y%m%d_%H%M%S)"
    
    if [[ -f "$config_file" ]]; then
        log_step "备份现有配置文件"
        cp "$config_file" "$backup_config"
        log_info "配置已备份到: $backup_config"
    fi
    
    # 记录容器运行状态
    local was_running=false
    if docker ps --format "table {{.Names}}" | grep -q "^$MYSQL_CONTAINER_NAME$"; then
        was_running=true
        log_info "MySQL容器当前正在运行"
    fi
    
    # 重新生成配置文件
    log_step "生成新的MySQL配置文件"
    create_mysql_config
    
    # 如果容器正在运行，需要重新创建容器以应用新的环境变量
    if [[ "$was_running" == true ]]; then
        log_step "重新创建MySQL容器以应用新配置（包括环境变量）"
        log_warn "注意：Docker容器的环境变量只能在创建时设置，需要重新创建容器"

        # 停止并删除旧容器
        if docker stop "$MYSQL_CONTAINER_NAME"; then
            log_info "MySQL容器已停止"
            sleep 3

            if docker rm "$MYSQL_CONTAINER_NAME"; then
                log_info "旧MySQL容器已删除"
            else
                log_error "删除旧容器失败"
                return 1
            fi
        else
            log_error "停止MySQL容器失败"
            # 恢复备份配置
            if [[ -f "$backup_config" ]]; then
                cp "$backup_config" "$config_file"
                log_info "已恢复备份配置"
            fi
            return 1
        fi

        # 重新创建并启动容器（使用新的环境变量）
        if start_mysql_container; then
            log_info "新MySQL容器已创建并启动"
            
            # 等待MySQL服务就绪
            if wait_for_mysql; then
                log_info "✅ MySQL配置更新成功，新配置已生效"
                
                # 显示新配置内容摘要
                log_step "当前MySQL配置摘要："
                if [[ -f "$config_file" ]]; then
                    echo "字符集: $(grep "character-set-server" "$config_file" | cut -d'=' -f2 | tr -d ' ')"
                    echo "最大连接数: $(grep "max_connections" "$config_file" | cut -d'=' -f2 | tr -d ' ')"
                    echo "缓冲池大小: $(grep "innodb_buffer_pool_size" "$config_file" | cut -d'=' -f2 | tr -d ' ')"
                    echo "配置文件路径: $config_file"
                fi
                
                return 0
            else
                log_error "MySQL服务启动后等待超时，恢复备份配置"
                # 停止容器
                docker stop "$MYSQL_CONTAINER_NAME" >/dev/null 2>&1
                
                # 恢复备份配置
                if [[ -f "$backup_config" ]]; then
                    cp "$backup_config" "$config_file"
                    log_info "已恢复备份配置"
                    
                    # 重新启动容器
                    docker start "$MYSQL_CONTAINER_NAME" >/dev/null 2>&1
                    log_info "已使用备份配置重启容器"
                fi
                return 1
            fi
        else
            log_error "启动MySQL容器失败，恢复备份配置"
            # 恢复备份配置
            if [[ -f "$backup_config" ]]; then
                cp "$backup_config" "$config_file"
                docker start "$MYSQL_CONTAINER_NAME" >/dev/null 2>&1
                log_info "已恢复备份配置并重启容器"
            fi
            return 1
        fi
    else
        log_info "✅ MySQL配置文件更新完成"
        log_warn "MySQL容器未运行，环境变量配置需要重新创建容器才能生效"
        log_info "建议操作："
        echo "  1. 删除旧容器: docker rm $MYSQL_CONTAINER_NAME"
        echo "  2. 重新安装: 运行安装脚本重新创建容器"
        echo "  或者直接启动: docker start $MYSQL_CONTAINER_NAME (仅配置文件生效)"
        
        # 显示新配置内容摘要
        log_step "新配置内容摘要："
        if [[ -f "$config_file" ]]; then
            echo "字符集: $(grep "character-set-server" "$config_file" | cut -d'=' -f2 | tr -d ' ')"
            echo "最大连接数: $(grep "max_connections" "$config_file" | cut -d'=' -f2 | tr -d ' ')"
            echo "缓冲池大小: $(grep "innodb_buffer_pool_size" "$config_file" | cut -d'=' -f2 | tr -d ' ')"
            echo "配置文件路径: $config_file"
        fi
        
        return 0
    fi
}

# 处理命令行参数
handle_arguments() {
    case "$1" in
        --check-installed)
            check_mysql_installed_status
            ;;
        --check-packages)
            check_mysql_packages
            ;;
        --uninstall)
            uninstall_mysql
            ;;
        --start)
            start_mysql_service
            ;;
        --stop)
            stop_mysql_service
            ;;
        --update-config)
            update_mysql_config
            ;;
        --diagnose)
            diagnose_mysql_issues
            ;;
        --help)
            show_help
            ;;
        *)
            main "$@"
            ;;
    esac
}

# 错误处理
trap 'echo -e "\n${RED}MySQL安装过程中出现错误${NC}"; exit 1' ERR

# 执行参数处理
handle_arguments "$@"