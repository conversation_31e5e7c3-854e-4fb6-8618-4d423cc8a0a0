#!/bin/bash

# CentOS 7兼容性设置
if [[ ${BASH_VERSION%%.*} -lt 4 ]]; then
    echo "错误: 需要bash 4.0或更高版本，当前版本: $BASH_VERSION"
    exit 1
fi

#=============================================================================
# OnlyOffice DocumentServer 8.2.3.1安装模块
# 版本: 2.0
# 用途: 在Docker环境中安装OnlyOffice DocumentServer 8.2.3.1
# 依赖: packages/onlyoffice/onlyoffice-documentserver-8.2.3.1.tar
#
# 功能特性:
# - Docker容器化部署
# - 完全离线安装
# - 配置文件化管理
# - 自动配置文件生成
# - 数据持久化
# - 健康检查
# - 智能检测
# - 完整卸载
#=============================================================================

# 移除 set -e 以避免状态检查时脚本中断
# set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_title() {
    echo -e "${BLUE}[TITLE]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BASE_DIR="$(dirname "$SCRIPT_DIR")"                        # packages/onlyoffice
PACKAGES_DIR="$(dirname "$BASE_DIR")"                      # packages
ROOT_DIR="$(dirname "$PACKAGES_DIR")"                      # scripts

# 加载配置文件
if [[ -f "$ROOT_DIR/config.sh" ]]; then
    source "$ROOT_DIR/config.sh"
else
    log_error "配置文件不存在: $ROOT_DIR/config.sh"
    exit 1
fi

# 检查Docker是否可用
check_docker_available() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        return 1
    fi

    if ! systemctl is-active docker &> /dev/null; then
        log_error "Docker服务未运行，请先启动Docker服务"
        return 1
    fi

    return 0
}

# 检查OnlyOffice是否已安装
check_onlyoffice_installed() {
    # 先检查Docker是否可用，如果不可用则返回未安装状态
    if ! command -v docker &> /dev/null; then
        return 1  # Docker未安装，OnlyOffice肯定未安装
    fi

    if ! systemctl is-active docker &> /dev/null; then
        return 1  # Docker服务未运行，无法检查OnlyOffice状态，返回未安装
    fi

    # 检查配置变量是否已加载
    if [[ -z "$ONLYOFFICE_CONTAINER_NAME" ]]; then
        return 1  # 配置未加载，返回未安装
    fi

    # 检查OnlyOffice容器状态
    if docker ps --format "table {{.Names}}" 2>/dev/null | grep -q "^$ONLYOFFICE_CONTAINER_NAME$"; then
        return 0  # 已安装并运行
    elif docker ps -a --format "table {{.Names}}" 2>/dev/null | grep -q "^$ONLYOFFICE_CONTAINER_NAME$"; then
        return 2  # 已安装但未运行
    else
        return 1  # 未安装
    fi
}

# 检查OnlyOffice安装状态（供主脚本调用）
check_onlyoffice_installed_status() {
    check_onlyoffice_installed
    exit $?
}

# 检查OnlyOffice安装包
check_onlyoffice_packages() {
    log_title "检查OnlyOffice安装包"

    local package_file="$BASE_DIR/onlyoffice-documentserver-${ONLYOFFICE_VERSION}.tar"

    if [[ -f "$package_file" ]]; then
        local file_size
        file_size=$(du -h "$package_file" | cut -f1)
        log_info "✅ OnlyOffice安装包存在: $package_file ($file_size)"
        return 0
    else
        log_error "❌ OnlyOffice安装包不存在: $package_file"
        log_info "请将OnlyOffice Docker镜像文件放置在: $package_file"
        return 1
    fi
}

# 检查OnlyOffice镜像
check_onlyoffice_image() {
    log_step "检查OnlyOffice Docker镜像"

    if docker images --format "table {{.Repository}}:{{.Tag}}" | grep -q "^$ONLYOFFICE_IMAGE$"; then
        log_info "✅ OnlyOffice镜像已存在: $ONLYOFFICE_IMAGE"
        return 0
    fi

    # 镜像不存在，尝试加载
    log_step "加载OnlyOffice Docker镜像"
    local package_file="$BASE_DIR/onlyoffice-documentserver-${ONLYOFFICE_VERSION}.tar"

    if [[ ! -f "$package_file" ]]; then
        log_error "OnlyOffice镜像文件不存在: $package_file"
        return 1
    fi

    if docker load -i "$package_file"; then
        log_info "✅ OnlyOffice镜像加载成功"
        return 0
    else
        log_error "OnlyOffice镜像加载失败"
        return 1
    fi
}

# 创建OnlyOffice目录结构
create_onlyoffice_directories() {
    log_title "创建OnlyOffice目录结构"

    # 创建主目录
    log_step "创建数据目录: $ONLYOFFICE_DATA_DIR"
    mkdir -p "$ONLYOFFICE_DATA_DIR"/{config,data,logs,lib,db}

    # 设置目录权限
    log_step "设置目录权限"
    chmod -R 755 "$ONLYOFFICE_DATA_DIR"

    # 显示配置信息
    if [[ -n "$DEBUG" ]]; then
        log_step "OnlyOffice配置信息："
        echo "   容器名: $ONLYOFFICE_CONTAINER_NAME"
        echo "   版本: $ONLYOFFICE_VERSION"
        echo "   端口: $ONLYOFFICE_PORT"
        echo "   数据目录: $ONLYOFFICE_DATA_DIR"
        echo "   JWT启用: $ONLYOFFICE_JWT_ENABLED"
    fi
}

# 创建OnlyOffice配置文件
create_onlyoffice_config() {
    log_title "创建OnlyOffice配置文件"

    local config_file="$ONLYOFFICE_DATA_DIR/config/default.json"

    log_step "生成OnlyOffice配置文件: $config_file"

    # 创建default.json配置文件（基于手书中的完整配置）
    cat > "$config_file" << 'EOF'
{
"statsd": {
"useMetrics": false,
"host": "localhost",
"port": "8125",
"prefix": "ds."
},
"aiSettings": {
"actions": {
},
"models": [
],
"providers": {
},
"version": 3,
"timeout": "30s",
"allowedCorsOrigins": [
"https://onlyoffice.github.io", "https://onlyoffice-plugins.github.io"
],
"pluginDir" : "../branding/info/ai"
},
"log": {
"filePath": "",
"options": {
"replaceConsole": true
}
},
"runtimeConfig": {
"filePath": "",
"cache": {
"stdTTL": 300,
"checkperiod": 60,
"useClones": false
}
},
"queue": {
"type": "rabbitmq",
"visibilityTimeout": 300,
"retentionPeriod": 900
},
"email": {
"smtpServerConfiguration": {
"host": "localhost",
"port": 587,
"auth": {
"user": "",
"pass": ""
}
},
"connectionConfiguration": {
"disableFileAccess": false,
"disableUrlAccess": false
},
"contactDefaults": {
"from": "<EMAIL>",
"to": "<EMAIL>"
}
},
"notification": {
"rules": {
"licenseExpirationWarning": {
"enable": false,
"transportType": [
"email"
],
"template": {
"title": "%s Docs license expiration warning",
"body": "Attention! Your license is about to expire on %s.\nUpon reaching this date, you will no longer be entitled to receive personal technical support and install new Docs versions released after this date."
},
"policies": {
"repeatInterval": "1d"
}
},
"licenseExpirationError": {
"enable": false,
"transportType": [
"email"
],
"template": {
"title": "%s Docs license expiration warning",
"body": "Attention! Your license expired on %s.\nYou are no longer entitled to receive personal technical support and install new Docs versions released after this date.\<NAME_EMAIL> to discuss license renewal."
},
"policies": {
"repeatInterval": "1d"
}
},
"licenseLimitEdit": {
"enable": false,
"transportType": [
"email"
],
"template": {
"title": "%s Docs license connection limit warning",
"body": "Attention! You have reached %s%% of the %s limit set by your license."
},
"policies": {
"repeatInterval": "1h"
}
},
"licenseLimitLiveViewer": {
"enable": false,
"transportType": [
"email"
],
"template": {
"title": "%s Docs license connection limit warning",
"body": "Attention! You have reached %s%% of the live viewer %s limit set by your license."
},
"policies": {
"repeatInterval": "1h"
}
}
}
},
"storage": {
"name": "storage-fs",
"fs": {
"folderPath": "",
"urlExpires": 900,
"secretString": "verysecretstring"
},
"region": "",
"endpoint": "http://localhost/s3",
"bucketName": "cache",
"storageFolderName": "files",
"cacheFolderName": "data",
"urlExpires": 604800,
"accessKeyId": "",
"secretAccessKey": "",
"sslEnabled": false,
"s3ForcePathStyle": true,
"externalHost": "",
"useDirectStorageUrls": true
},
"persistentStorage": {
},
"rabbitmq": {
"url": "amqp://localhost:5672",
"socketOptions": {},
"exchangepubsub": {
"name": "ds.pubsub",
"options": {
"durable": true
}
},
"queuepubsub": {
"name": "",
"options": {
"autoDelete": true,
"exclusive": true,
"arguments": {
"x-queue-type": "classic"
}
}
},
"queueconverttask": {
"name": "ds.converttask6",
"options": {
"durable": true,
"maxPriority": 6,
"arguments": {
"x-queue-type": "classic"
}
}
},
"queueconvertresponse": {
"name": "ds.convertresponse",
"options": {
"durable": true,
"arguments": {
"x-queue-type": "classic"
}
}
},
"exchangeconvertdead": {
"name": "ds.exchangeconvertdead",
"options": {
"durable": true
}
},
"queueconvertdead": {
"name": "ds.convertdead",
"options": {
"durable": true,
"arguments": {
"x-queue-type": "classic"
}
}
},
"queuedelayed": {
"name": "ds.delayed",
"options": {
"durable": true,
"arguments": {
"x-queue-type": "classic"
}
}
}
},
"activemq": {
"connectOptions": {
"port": 5672,
"host": "localhost",
"reconnect": false
},
"queueconverttask": "ds.converttask",
"queueconvertresponse": "ds.convertresponse",
"queueconvertdead": "ActiveMQ.DLQ",
"queuedelayed": "ds.delayed",
"topicpubsub": "ds.pubsub"
},
"dnscache": {
"enable" : true,
"ttl" : 300,
"cachesize" : 1000
},
"openpgpjs": {
"config": {
},
"encrypt": {
"passwords": ["verysecretstring"]
},
"decrypt": {
"passwords": ["verysecretstring"]
}
},
"aesEncrypt": {
"config": {
"keyByteLength": 32,
"saltByteLength": 64,
"initializationVectorByteLength": 16,
"iterationsByteLength": 5
},
"secret": "verysecretstring"
},
"bottleneck": {
"getChanges": {
}
},
"win-ca": {
"inject": "+"
},
"wopi": {
"enable": false,
"host" : "",
"htmlTemplate" : "../../web-apps/apps/api/wopi",
"wopiZone" : "external-http",
"favIconUrlWord" : "/web-apps/apps/documenteditor/main/resources/img/favicon.ico",
"favIconUrlCell" : "/web-apps/apps/spreadsheeteditor/main/resources/img/favicon.ico",
"favIconUrlSlide" : "/web-apps/apps/presentationeditor/main/resources/img/favicon.ico",
"favIconUrlPdf" : "/web-apps/apps/pdfeditor/main/resources/img/favicon.ico",
"favIconUrlDiagram" : "/web-apps/apps/visioeditor/main/resources/img/favicon.ico",
"fileInfoBlockList" : ["FileUrl"],
"pdfView": ["djvu", "xps", "oxps"],
"pdfEdit": ["pdf"],
"forms": ["pdf"],
"wordView": ["doc", "dotm", "dot", "fodt", "ott", "rtf", "mht", "mhtml", "html", "htm", "xml", "epub", "fb2", "sxw", "stw", "wps", "wpt", "pages", "docxf", "oform", "hwp", "hwpx", "md"],
"wordEdit": ["docx", "dotx", "docm", "odt", "txt"],
"cellView": ["xls", "xltm", "xlt", "fods", "ots", "sxc", "xml", "et", "ett", "numbers"],
"cellEdit": ["xlsx", "xlsb", "xltx", "xlsm", "ods", "csv"],
"slideView": ["ppt", "ppsx", "ppsm", "pps", "potm", "pot", "fodp", "otp", "sxi", "dps", "dpt", "key", "odg"],
"slideEdit": ["pptx", "potx", "pptm", "odp"],
"diagramView": ["vsdx", "vstx", "vssx", "vsdm", "vstm", "vssm"],
"diagramEdit": [],
"publicKey": "",
"modulus": "",
"exponent": 65537,
"privateKey": "",
"publicKeyOld": "",
"modulusOld": "",
"exponentOld": 65537,
"privateKeyOld": "",
"refreshLockInterval": "10m",
"dummy" : {
"enable": false,
"sampleFilePath": ""
}
},
"tenants": {
"baseDir": "",
"baseDomain": "",
"filenameConfig": "config.json",
"filenameSecret": "secret.key",
"filenameLicense": "license.lic",
"defaultTenant": "localhost",
"cache" : {
"stdTTL": 300,
"checkperiod": 60,
"useClones": false
}
},
"externalRequest": {
"directIfIn" : {
"allowList": [],
"jwtToken": true
},
"action": {
"allow": true,
"blockPrivateIP": true,
"proxyUrl": "",
"proxyUser": {
"username": "",
"password": ""
},
"proxyHeaders": {
}
}
},
"services": {
"CoAuthoring": {
"server": {
"port": 8000,
"workerpercpu": 1,
"mode": "development",
"limits_tempfile_upload": 104857600,
"limits_image_size": 26214400,
"limits_image_download_timeout": {
"connectionAndInactivity": "2m",
"wholeCycle": "2m"
},
"callbackRequestTimeout": {
"connectionAndInactivity": "10m",
"wholeCycle": "10m"
},
"healthcheckfilepath": "../public/healthcheck.docx",
"savetimeoutdelay": 5000,
"edit_singleton": false,
"forgottenfiles": "forgotten",
"forgottenfilesname": "output",
"maxRequestChanges": 20000,
"openProtectedFile": true,
"isAnonymousSupport": true,
"editorDataStorage": "editorDataMemory",
"editorStatStorage": "",
"assemblyFormatAsOrigin": true,
"newFileTemplate" : "../../document-templates/new",
"downloadFileAllowExt": ["pdf", "xlsx"],
"tokenRequiredParams": true,
"forceSaveUsingButtonWithoutChanges": false
},
"requestDefaults": {
"headers": {
"User-Agent": "Node.js/6.13",
"Connection": "Keep-Alive"
},
"rejectUnauthorized": true
},
"autoAssembly": {
"enable": false,
"interval": "5m",
"step": "1m"
},
"utils": {
"utils_common_fontdir": "null",
"utils_fonts_search_patterns": "*.ttf;*.ttc;*.otf",
"limits_image_types_upload": "jpg;jpeg;jpe;png;gif;bmp;svg;tiff;tif"
},
"sql": {
"type": "postgres",
"tableChanges": "doc_changes",
"tableResult": "task_result",
"dbHost": "localhost",
"dbPort": 5432,
"dbName": "onlyoffice",
"dbUser": "onlyoffice",
"dbPass": "onlyoffice",
"charset": "utf8",
"connectionlimit": 10,
"max_allowed_packet": 1048575,
"pgPoolExtraOptions": {
"idleTimeoutMillis": 30000,
"maxLifetimeSeconds ": 60000,
"statement_timeout ": 60000,
"query_timeout  ": 60000,
"connectionTimeoutMillis": 60000
},
"damengExtraOptions": {
"columnNameUpperCase": false,
"columnNameCase": "lower",
"connectTimeout": 60000,
"loginEncrypt": false,
"localTimezone": 0,
"poolTimeout": 60,
"socketTimeout": 60000,
"queueTimeout": 60000
},
"oracleExtraOptions": {
"connectTimeout": 60
},
"msSqlExtraOptions": {
"options": {
"encrypt": false,
"trustServerCertificate": true
},
"pool": {
"idleTimeoutMillis": 30000
}
},
"mysqlExtraOptions": {
"connectTimeout": 60000,
"queryTimeout": 60000
}
},
"redis": {
"name": "redis",
"prefix": "ds:",
"host": "127.0.0.1",
"port": 6379,
"options": {},
"optionsCluster": {},
"iooptions": {
"lazyConnect": true
},
"iooptionsClusterNodes": [
],
"iooptionsClusterOptions": {
"lazyConnect": true
}
},
"pubsub": {
"maxChanges": 1000
},
"expire": {
"saveLock": 60,
"presence": 300,
"locks": 604800,
"changeindex": 86400,
"lockDoc": 30,
"message": 86400,
"lastsave": 604800,
"forcesave": 604800,
"forcesaveLock": 5000,
"saved": 3600,
"documentsCron": "0 */2 * * * *",
"files": 86400,
"filesCron": "00 00 */1 * * *",
"filesremovedatonce": 100,
"sessionidle": "1h",
"sessionabsolute": "30d",
"sessionclosecommand": "2m",
"pemStdTTL": "1h",
"pemCheckPeriod": "10m",
"updateVersionStatus": "5m",
"monthUniqueUsers": "1y"
},
"ipfilter": {
"rules": [{"address": "*", "allowed": true}],
"useforrequest": false,
"errorcode": 403
},
"request-filtering-agent" : {
"allowPrivateIPAddress": true,
"allowMetaIPAddress": true
},
"secret": {
"browser": {"string": "secret", "file": ""},
"inbox": {"string": "secret", "file": ""},
"outbox": {"string": "secret", "file": ""},
"session": {"string": "secret", "file": ""}
},
"token": {
"enable": {
"browser": false,
"request": {
"inbox": false,
"outbox": false
}
},
"browser": {
"secretFromInbox": true
},
"inbox": {
"header": "Authorization",
"prefix": "Bearer ",
"inBody": false
},
"outbox": {
"header": "Authorization",
"prefix": "Bearer ",
"algorithm": "HS256",
"expires": "5m",
"inBody": false,
"urlExclusionRegex": ""
},
"session": {
"algorithm": "HS256",
"expires": "30d"
},
"verifyOptions": {
"clockTolerance": 60
}
},
"plugins": {
"uri": "/sdkjs-plugins",
"autostart": []
},
"themes": {
"uri": "/web-apps/apps/common/main/resources/themes"
},
"editor":{
"spellcheckerUrl": "",
"reconnection":{
"attempts": 50,
"delay": "2s"
},
"binaryChanges": false,
"websocketMaxPayloadSize": "1.5MB",
"maxChangesSize": "150MB"
},
"sockjs": {
"sockjs_url": "",
"disable_cors": true,
"websocket": true
},
"socketio": {
"connection": {
"path": "/doc/",
"serveClient": false,
"pingTimeout": 20000,
"pingInterval": 25000,
"maxHttpBufferSize": 1e8
}
},
"callbackBackoffOptions": {
"retries": 3,
"timeout":{
"factor": 2,
"minTimeout": 1000,
"maxTimeout": **********,
"randomize": false
},
"httpStatus": "429,500-599"
}
}
},
"license" : {
"license_file": "",
"warning_limit_percents": 70,
"packageType": 0,
"warning_license_expiration": "30d"
},
"FileConverter": {
"converter": {
"maxDownloadBytes": 104857600,
"downloadTimeout": {
"connectionAndInactivity": "2m",
"wholeCycle": "2m"
},
"downloadAttemptMaxCount": 3,
"downloadAttemptDelay": 1000,
"maxprocesscount": 1,
"fontDir": "null",
"presentationThemesDir": "null",
"x2tPath": "null",
"docbuilderPath": "null",
"args": "",
"spawnOptions": {},
"errorfiles": "",
"streamWriterBufferSize": 8388608,
"maxRedeliveredCount": 2,
"inputLimits": [
{
"type": "docx;dotx;docm;dotm",
"zip": {
"uncompressed": "50MB",
"template": "*.xml"
}
},
{
"type": "xlsx;xltx;xlsm;xltm",
"zip": {
"uncompressed": "300MB",
"template": "*.xml"
}
},
{
"type": "pptx;ppsx;potx;pptm;ppsm;potm",
"zip": {
"uncompressed": "50MB",
"template": "*.xml"
}
}
]
}
}
}
EOF

    log_info "OnlyOffice配置文件创建完成: $config_file"
}

# 启动OnlyOffice容器
start_onlyoffice_container() {
    log_title "启动OnlyOffice容器"

    # 检查网络是否存在
    if ! docker network ls | grep -q "$DOCKER_NETWORK"; then
        log_step "创建Docker网络: $DOCKER_NETWORK"
        docker network create "$DOCKER_NETWORK"
    fi

    # 启动OnlyOffice容器（根据手书和config.sh配置）
    log_step "启动OnlyOffice $ONLYOFFICE_VERSION 容器"

    # 构建docker run命令
    local docker_cmd="docker run -d"
    docker_cmd="$docker_cmd --name $ONLYOFFICE_CONTAINER_NAME"
    docker_cmd="$docker_cmd --network $DOCKER_NETWORK"
    docker_cmd="$docker_cmd -p $ONLYOFFICE_PORT:80"

    # 环境变量配置
    docker_cmd="$docker_cmd -e JWT_ENABLED=$ONLYOFFICE_JWT_ENABLED"
    docker_cmd="$docker_cmd -e JWT_SECRET=$ONLYOFFICE_JWT_SECRET"

    # 数据卷挂载
    docker_cmd="$docker_cmd -v $ONLYOFFICE_DATA_DIR/config/default.json:$ONLYOFFICE_CONTAINER_CONFIG_PATH"
    docker_cmd="$docker_cmd -v $ONLYOFFICE_DATA_DIR/logs:$ONLYOFFICE_CONTAINER_LOG_PATH"
    docker_cmd="$docker_cmd -v $ONLYOFFICE_DATA_DIR/data:$ONLYOFFICE_CONTAINER_DATA_PATH"
    docker_cmd="$docker_cmd -v $ONLYOFFICE_DATA_DIR/lib:$ONLYOFFICE_CONTAINER_LIB_PATH"
    docker_cmd="$docker_cmd -v $ONLYOFFICE_DATA_DIR/db:$ONLYOFFICE_CONTAINER_DB_PATH"

    # 资源限制（从config.sh读取）
    if [[ -n "$ONLYOFFICE_MEMORY_LIMIT" ]]; then
        docker_cmd="$docker_cmd --memory=$ONLYOFFICE_MEMORY_LIMIT"
    fi
    if [[ -n "$ONLYOFFICE_CPU_LIMIT" ]]; then
        docker_cmd="$docker_cmd --cpus=$ONLYOFFICE_CPU_LIMIT"
    fi

    # 重启策略和镜像
    docker_cmd="$docker_cmd --restart unless-stopped"
    docker_cmd="$docker_cmd $ONLYOFFICE_IMAGE"

    # 执行启动命令
    if [[ -n "$DEBUG" ]]; then
        log_step "执行命令: $docker_cmd"
    fi

    eval "$docker_cmd"

    if [[ $? -eq 0 ]]; then
        log_info "OnlyOffice容器启动成功"
    else
        log_error "OnlyOffice容器启动失败"
        return 1
    fi
}

# 等待OnlyOffice启动（参考MySQL和Redis的成功经验）
wait_for_onlyoffice() {
    log_title "等待OnlyOffice启动"

    log_step "等待OnlyOffice容器启动 (最多等待60秒)"
    local max_attempts=60
    local attempt=1

    # 第一阶段：等待容器启动
    while [[ $attempt -le 15 ]]; do
        if docker ps | grep -q "$ONLYOFFICE_CONTAINER_NAME"; then
            log_info "✅ OnlyOffice容器已启动"
            break
        fi
        echo -n "."
        sleep 1
        attempt=$((attempt + 1))
    done

    if [[ $attempt -gt 15 ]]; then
        log_error "OnlyOffice容器启动失败"
        return 1
    fi

    # 第二阶段：等待OnlyOffice服务就绪（OnlyOffice启动较慢）
    log_step "等待OnlyOffice服务初始化完成 (最多等待45秒)"
    attempt=1
    while [[ $attempt -le 45 ]]; do
        # 使用健康检查端点检测OnlyOffice是否真正就绪
        if curl -s "http://localhost:$ONLYOFFICE_PORT$ONLYOFFICE_HEALTHCHECK_URL" >/dev/null 2>&1; then
            log_info "✅ OnlyOffice服务就绪"
            # 额外等待2秒确保完全就绪
            sleep 2
            return 0
        fi

        # 检查容器是否还在运行
        if ! docker ps | grep -q "$ONLYOFFICE_CONTAINER_NAME"; then
            log_error "OnlyOffice容器意外停止"
            docker logs "$ONLYOFFICE_CONTAINER_NAME" | tail -10
            return 1
        fi

        echo -n "."
        sleep 1
        attempt=$((attempt + 1))
    done

    log_error "OnlyOffice服务初始化超时"
    log_info "查看OnlyOffice日志："
    docker logs "$ONLYOFFICE_CONTAINER_NAME" | tail -20
    return 1
}

# 验证OnlyOffice安装
verify_onlyoffice_installation() {
    log_title "验证OnlyOffice安装"

    # 检查容器状态
    log_step "检查容器状态"
    if ! docker ps | grep -q "$ONLYOFFICE_CONTAINER_NAME"; then
        log_error "OnlyOffice容器未运行"
        return 1
    fi
    log_info "✅ OnlyOffice容器运行正常"

    # 检查OnlyOffice服务连通性
    log_step "检查OnlyOffice服务连通性"
    if curl -s "http://localhost:$ONLYOFFICE_PORT$ONLYOFFICE_HEALTHCHECK_URL" >/dev/null 2>&1; then
        log_info "✅ OnlyOffice服务连通性正常"
    else
        log_error "❌ OnlyOffice服务连通性失败"
        return 1
    fi

    # 检查OnlyOffice版本信息（使用正确的Command Service API）
    log_step "检查OnlyOffice版本信息"

    # 给Command Service额外的初始化时间（避免不必要的警告）
    log_step "等待Command Service完全就绪..."
    sleep 3

    local version_info
    # 使用OnlyOffice 8.2+的新API端点和POST方法
    version_info=$(curl -s -X POST "http://localhost:$ONLYOFFICE_PORT/command" \
        -H "Content-Type: application/json" \
        -d '{"c":"version"}' 2>/dev/null | grep -o '"version":"[^"]*"' | cut -d'"' -f4)

    if [[ -n "$version_info" ]]; then
        log_info "✅ OnlyOffice版本: $version_info"
    else
        log_warn "⚠️  OnlyOffice版本检查失败，Command Service可能仍在初始化中"
        # 给OnlyOffice更多时间初始化
        log_step "等待Command Service完全初始化..."
        sleep 5

        # 重试版本检查
        version_info=$(curl -s -X POST "http://localhost:$ONLYOFFICE_PORT/command" \
            -H "Content-Type: application/json" \
            -d '{"c":"version"}' 2>/dev/null | grep -o '"version":"[^"]*"' | cut -d'"' -f4)

        if [[ -n "$version_info" ]]; then
            log_info "✅ OnlyOffice版本: $version_info"
        else
            # 尝试旧版本的API端点（兼容性）
            log_step "尝试旧版本API端点..."
            version_info=$(curl -s -X POST "http://localhost:$ONLYOFFICE_PORT/coauthoring/CommandService.ashx" \
                -H "Content-Type: application/json" \
                -d '{"c":"version"}' 2>/dev/null | grep -o '"version":"[^"]*"' | cut -d'"' -f4)

            if [[ -n "$version_info" ]]; then
                log_info "✅ OnlyOffice版本: $version_info (通过旧版API获取)"
            else
                log_warn "⚠️  OnlyOffice版本信息获取失败，但容器运行正常"
                log_info "💡 这可能是因为OnlyOffice仍在初始化中，属于正常现象"
            fi
        fi
    fi

    # 检查配置文件
    log_step "检查配置文件"
    if [[ -f "$ONLYOFFICE_DATA_DIR/config/default.json" ]]; then
        log_info "✅ OnlyOffice配置文件存在"
    else
        log_warn "⚠️  OnlyOffice配置文件不存在"
    fi

    # 检查数据目录
    log_step "检查数据目录"
    for dir in data logs lib db; do
        if [[ -d "$ONLYOFFICE_DATA_DIR/$dir" ]]; then
            log_info "✅ 数据目录存在: $dir"
        else
            log_warn "⚠️  数据目录不存在: $dir"
        fi
    done

    # 检查JWT配置
    log_step "检查JWT配置"
    log_info "✅ JWT启用状态: $ONLYOFFICE_JWT_ENABLED"
    if [[ "$ONLYOFFICE_JWT_ENABLED" == "true" && -n "$ONLYOFFICE_JWT_SECRET" ]]; then
        log_info "✅ JWT密钥已配置"
    elif [[ "$ONLYOFFICE_JWT_ENABLED" == "false" ]]; then
        log_info "✅ JWT已禁用"
    else
        log_warn "⚠️  JWT配置可能有问题"
    fi

    log_info "✅ OnlyOffice安装验证完成"
    return 0
}

# 显示安装后信息
show_post_install_info() {
    log_title "OnlyOffice安装完成"

    echo ""
    echo "🎉 OnlyOffice DocumentServer $ONLYOFFICE_VERSION 安装成功！"
    echo ""
    echo "📋 服务信息："
    echo "   容器名称: $ONLYOFFICE_CONTAINER_NAME"
    echo "   服务版本: $ONLYOFFICE_VERSION"
    echo "   访问地址: http://$SERVER_IP:$ONLYOFFICE_PORT/"
    echo "   健康检查: http://$SERVER_IP:$ONLYOFFICE_PORT$ONLYOFFICE_HEALTHCHECK_URL"
    echo ""
    echo "📁 数据目录："
    echo "   主目录: $ONLYOFFICE_DATA_DIR"
    echo "   配置文件: $ONLYOFFICE_DATA_DIR/config/default.json"
    echo "   数据目录: $ONLYOFFICE_DATA_DIR/data"
    echo "   日志目录: $ONLYOFFICE_DATA_DIR/logs"
    echo ""
    echo "🔐 安全配置："
    echo "   JWT启用: $ONLYOFFICE_JWT_ENABLED"
    if [[ "$ONLYOFFICE_JWT_ENABLED" == "true" ]]; then
        echo "   JWT密钥: $ONLYOFFICE_JWT_SECRET"
    fi
    echo ""
    echo "🔧 管理命令："
    echo "   启动服务: docker start $ONLYOFFICE_CONTAINER_NAME"
    echo "   停止服务: docker stop $ONLYOFFICE_CONTAINER_NAME"
    echo "   查看日志: docker logs $ONLYOFFICE_CONTAINER_NAME"
    echo "   查看状态: docker ps | grep $ONLYOFFICE_CONTAINER_NAME"
    echo ""
    echo "💡 提示："
    echo "   - OnlyOffice DocumentServer已准备就绪，可以集成到您的应用中"
    echo "   - 如需修改配置，请编辑 $ONLYOFFICE_DATA_DIR/config/default.json 后重启容器"
    echo "   - 更多信息请访问: https://api.onlyoffice.com/"
    echo ""
}

# 启动OnlyOffice服务
start_onlyoffice_service() {
    log_title "启动OnlyOffice服务"

    # 检查容器是否存在
    if ! docker ps -a | grep -q "$ONLYOFFICE_CONTAINER_NAME"; then
        log_error "OnlyOffice容器不存在，请先安装OnlyOffice"
        return 1
    fi

    # 检查容器是否已经运行
    if docker ps | grep -q "$ONLYOFFICE_CONTAINER_NAME"; then
        log_info "OnlyOffice容器已经在运行"
        return 0
    fi

    # 启动容器并等待就绪
    if docker start "$ONLYOFFICE_CONTAINER_NAME"; then
        if wait_for_onlyoffice; then
            log_info "✅ OnlyOffice服务启动完成"
            return 0
        fi
    fi

    log_error "OnlyOffice服务启动失败"
    return 1
}

# 停止OnlyOffice服务
stop_onlyoffice_service() {
    log_title "停止OnlyOffice服务"

    if ! docker ps | grep -q "$ONLYOFFICE_CONTAINER_NAME"; then
        log_info "OnlyOffice容器未运行"
        return 0
    fi

    if docker stop "$ONLYOFFICE_CONTAINER_NAME"; then
        log_info "✅ OnlyOffice服务停止成功"
        return 0
    else
        log_error "OnlyOffice服务停止失败"
        return 1
    fi
}

# 卸载OnlyOffice
uninstall_onlyoffice() {
    log_title "卸载OnlyOffice DocumentServer"

    # 确认卸载
    echo -n "确定要卸载OnlyOffice DocumentServer吗？这将删除容器和镜像 (y/N): "
    read -r confirm
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        log_info "取消卸载"
        return 0
    fi

    # 停止并删除容器
    if docker ps -a | grep -q "$ONLYOFFICE_CONTAINER_NAME"; then
        log_step "停止并删除OnlyOffice容器"
        docker stop "$ONLYOFFICE_CONTAINER_NAME" 2>/dev/null || true
        docker rm "$ONLYOFFICE_CONTAINER_NAME" 2>/dev/null || true
        log_info "✅ OnlyOffice容器已删除"
    fi

    # 删除镜像
    if docker images --format "table {{.Repository}}:{{.Tag}}" | grep -q "^$ONLYOFFICE_IMAGE$"; then
        echo -n "是否删除OnlyOffice镜像？(y/N): "
        read -r confirm_image
        if [[ "$confirm_image" =~ ^[Yy]$ ]]; then
            log_step "删除OnlyOffice镜像: $ONLYOFFICE_IMAGE"

            # 强制删除镜像，包括所有标签
            if docker rmi -f "$ONLYOFFICE_IMAGE" 2>/dev/null; then
                log_info "✅ OnlyOffice镜像已删除"
            else
                log_warn "⚠️  镜像删除失败，可能被其他容器使用"
                # 尝试删除所有相关的OnlyOffice镜像
                log_step "尝试删除所有OnlyOffice相关镜像..."
                docker images | grep "onlyoffice/documentserver" | awk '{print $1":"$2}' | xargs -r docker rmi -f 2>/dev/null || true
                log_info "✅ 已尝试清理所有OnlyOffice镜像"
            fi
        else
            log_info "保留OnlyOffice镜像: $ONLYOFFICE_IMAGE"
        fi
    else
        log_info "OnlyOffice镜像不存在，无需删除"
    fi

    # 询问是否删除数据目录
    if [[ -d "$ONLYOFFICE_DATA_DIR" ]]; then
        echo -n "是否删除OnlyOffice数据目录？(包含配置和数据) (y/N): "
        read -r confirm_data
        if [[ "$confirm_data" =~ ^[Yy]$ ]]; then
            log_step "删除OnlyOffice数据目录"
            rm -rf "$ONLYOFFICE_DATA_DIR"
            log_info "✅ OnlyOffice数据目录已删除"
        else
            log_info "保留OnlyOffice数据目录: $ONLYOFFICE_DATA_DIR"
        fi
    fi

    log_info "✅ OnlyOffice卸载完成"
}

# 诊断OnlyOffice问题
diagnose_onlyoffice_issues() {
    log_title "OnlyOffice状态诊断"

    # 1. 容器状态检查
    log_step "1. 容器状态检查"
    if docker ps | grep -q "$ONLYOFFICE_CONTAINER_NAME"; then
        log_info "✅ OnlyOffice容器正在运行"
        docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep "$ONLYOFFICE_CONTAINER_NAME"
    elif docker ps -a | grep -q "$ONLYOFFICE_CONTAINER_NAME"; then
        log_warn "⚠️  OnlyOffice容器已停止"
        docker ps -a --format "table {{.Names}}\t{{.Status}}" | grep "$ONLYOFFICE_CONTAINER_NAME"
    else
        log_error "❌ OnlyOffice容器不存在"
    fi

    # 2. 端口监听检查
    log_step "2. 端口监听检查"
    if docker port "$ONLYOFFICE_CONTAINER_NAME" 2>/dev/null | grep -q "80"; then
        log_info "✅ OnlyOffice端口映射正常"
        docker port "$ONLYOFFICE_CONTAINER_NAME"
    else
        log_warn "⚠️  OnlyOffice端口映射异常"
    fi

    # 3. 服务连通性检查
    log_step "3. 服务连通性检查"
    if curl -s --connect-timeout 5 "http://localhost:$ONLYOFFICE_PORT$ONLYOFFICE_HEALTHCHECK_URL" >/dev/null 2>&1; then
        log_info "✅ OnlyOffice服务连通性正常"
    else
        log_warn "⚠️  OnlyOffice服务连通性异常"
    fi

    # 4. 容器日志检查
    log_step "4. 容器日志检查（最近20行）"
    if docker ps -a | grep -q "$ONLYOFFICE_CONTAINER_NAME"; then
        docker logs "$ONLYOFFICE_CONTAINER_NAME" --tail 20
    else
        log_warn "容器不存在，无法查看日志"
    fi

    # 5. 数据目录检查
    log_step "5. 数据目录检查"
    if [[ -d "$ONLYOFFICE_DATA_DIR" ]]; then
        local dir_size
        dir_size=$(du -sh "$ONLYOFFICE_DATA_DIR" | cut -f1)
        log_info "✅ 数据目录存在，大小: $dir_size"

        # 检查各子目录
        for subdir in config data logs lib db; do
            if [[ -d "$ONLYOFFICE_DATA_DIR/$subdir" ]]; then
                local subdir_size
                subdir_size=$(du -sh "$ONLYOFFICE_DATA_DIR/$subdir" | cut -f1)
                log_info "  - $subdir: $subdir_size"
            else
                log_warn "  - $subdir: 不存在"
            fi
        done
    else
        log_warn "⚠️  数据目录不存在: $ONLYOFFICE_DATA_DIR"
    fi

    # 6. 配置文件检查
    log_step "6. 配置文件检查"
    local config_file="$ONLYOFFICE_DATA_DIR/config/default.json"
    if [[ -f "$config_file" ]]; then
        local config_size
        config_size=$(du -h "$config_file" | cut -f1)
        log_info "✅ 配置文件存在: $config_file ($config_size)"
    else
        log_warn "⚠️  配置文件不存在: $config_file"
    fi

    # 7. 镜像检查
    log_step "7. 镜像检查"
    if docker images | grep -q "$ONLYOFFICE_IMAGE"; then
        log_info "✅ OnlyOffice镜像存在"
        docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}" | grep "onlyoffice/documentserver"
    else
        log_warn "⚠️  OnlyOffice镜像不存在"
    fi

    # 8. 版本信息检查
    log_step "8. 版本信息检查"
    if docker ps | grep -q "$ONLYOFFICE_CONTAINER_NAME"; then
        local version_info
        # 使用正确的Command Service API
        version_info=$(curl -s -X POST "http://localhost:$ONLYOFFICE_PORT/command" \
            -H "Content-Type: application/json" \
            -d '{"c":"version"}' 2>/dev/null | grep -o '"version":"[^"]*"' | cut -d'"' -f4)

        if [[ -n "$version_info" ]]; then
            log_info "✅ OnlyOffice版本: $version_info"
        else
            # 尝试旧版本API端点
            version_info=$(curl -s -X POST "http://localhost:$ONLYOFFICE_PORT/coauthoring/CommandService.ashx" \
                -H "Content-Type: application/json" \
                -d '{"c":"version"}' 2>/dev/null | grep -o '"version":"[^"]*"' | cut -d'"' -f4)

            if [[ -n "$version_info" ]]; then
                log_info "✅ OnlyOffice版本: $version_info (通过旧版API)"
            else
                log_warn "⚠️  OnlyOffice版本信息获取失败"
                log_info "💡 可能原因：服务仍在初始化或API端点不可用"
            fi
        fi
    else
        log_warn "⚠️  OnlyOffice容器未运行，无法检查版本"
    fi

    # 9. 网络检查
    log_step "9. 网络检查"
    if docker network ls | grep -q "$DOCKER_NETWORK"; then
        log_info "✅ Docker网络存在: $DOCKER_NETWORK"
    else
        log_warn "⚠️  Docker网络不存在: $DOCKER_NETWORK"
    fi

    log_info "诊断完成"
}

# 主安装函数
install_onlyoffice() {
    log_title "OnlyOffice DocumentServer $ONLYOFFICE_VERSION 安装开始"

    # 检查是否已安装
    check_onlyoffice_installed
    local install_status=$?
    case $install_status in
        0)
            log_info "OnlyOffice已安装且正在运行"
            log_info "如需重新安装，请先卸载现有版本"
            return 0
            ;;
        2)
            log_info "OnlyOffice已安装但未运行，正在启动..."
            start_onlyoffice_service
            return $?
            ;;
    esac

    # 检查Docker环境
    if ! check_docker_available; then
        return 1
    fi

    # 检查安装包
    if ! check_onlyoffice_packages; then
        return 1
    fi

    # 检查/加载Docker镜像
    if ! check_onlyoffice_image; then
        return 1
    fi

    # 创建目录结构
    if ! create_onlyoffice_directories; then
        return 1
    fi

    # 创建配置文件
    if ! create_onlyoffice_config; then
        return 1
    fi

    # 启动容器
    if ! start_onlyoffice_container; then
        return 1
    fi

    # 等待服务就绪
    if ! wait_for_onlyoffice; then
        return 1
    fi

    # 验证安装
    if ! verify_onlyoffice_installation; then
        return 1
    fi

    # 显示安装后信息
    show_post_install_info

    log_info "✅ OnlyOffice DocumentServer $ONLYOFFICE_VERSION 安装完成"
    return 0
}

# 主函数
main() {
    # 检查root权限
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        log_info "请使用: sudo $0"
        exit 1
    fi

    install_onlyoffice
}

# 更新OnlyOffice配置
update_onlyoffice_config() {
    log_title "更新OnlyOffice配置"
    
    # 检查Docker是否可用
    if ! check_docker_available; then
        return 1
    fi
    
    # 检查OnlyOffice容器是否存在
    if ! docker ps -a --format "table {{.Names}}" | grep -q "^$ONLYOFFICE_CONTAINER_NAME$"; then
        log_error "OnlyOffice容器不存在，请先安装OnlyOffice"
        return 1
    fi
    
    # 检查配置文件目录是否存在
    if [[ ! -d "$ONLYOFFICE_DATA_DIR/config" ]]; then
        log_error "OnlyOffice配置目录不存在: $ONLYOFFICE_DATA_DIR/config"
        return 1
    fi
    
    # 备份现有配置
    local config_file="$ONLYOFFICE_DATA_DIR/config/default.json"
    local backup_config="$ONLYOFFICE_DATA_DIR/config/default.json.backup.$(date +%Y%m%d_%H%M%S)"
    
    if [[ -f "$config_file" ]]; then
        log_step "备份现有配置文件"
        cp "$config_file" "$backup_config"
        log_info "配置已备份到: $backup_config"
    fi
    
    # 记录容器运行状态
    local was_running=false
    if docker ps --format "table {{.Names}}" | grep -q "^$ONLYOFFICE_CONTAINER_NAME$"; then
        was_running=true
        log_info "OnlyOffice容器当前正在运行"
    fi
    
    # 重新生成配置文件
    log_step "生成新的OnlyOffice配置文件"
    if ! create_onlyoffice_config; then
        log_error "生成OnlyOffice配置文件失败"
        # 恢复备份配置
        if [[ -f "$backup_config" ]]; then
            cp "$backup_config" "$config_file"
            log_info "已恢复备份配置"
        fi
        return 1
    fi
    
    # 如果容器正在运行，需要重新创建容器以应用新的环境变量
    if [[ "$was_running" == true ]]; then
        log_step "重新创建OnlyOffice容器以应用新配置（包括环境变量）"
        log_warn "注意：Docker容器的环境变量只能在创建时设置，需要重新创建容器"

        # 停止并删除旧容器
        if docker stop "$ONLYOFFICE_CONTAINER_NAME"; then
            log_info "OnlyOffice容器已停止"
            sleep 5

            if docker rm "$ONLYOFFICE_CONTAINER_NAME"; then
                log_info "旧OnlyOffice容器已删除"
            else
                log_error "删除旧容器失败"
                return 1
            fi
        else
            log_error "停止OnlyOffice容器失败"
            # 恢复备份配置
            if [[ -f "$backup_config" ]]; then
                cp "$backup_config" "$config_file"
                log_info "已恢复备份配置"
            fi
            return 1
        fi
        
        # 重新创建并启动容器（使用新的环境变量）
        if start_onlyoffice_container; then
            log_info "新OnlyOffice容器已创建并启动"

            # 等待服务启动
            sleep 20

            # 健康检查
            if verify_onlyoffice_installation; then
                log_success "✅ OnlyOffice配置更新成功，新配置和环境变量已生效"
                
                # 显示新配置内容摘要
                log_step "当前OnlyOffice配置摘要："
                if [[ -f "$config_file" ]]; then
                    echo "配置文件路径: $config_file"
                    echo "配置文件大小: $(du -h "$config_file" | cut -f1)"
                    echo "队列类型: $(grep -o '"type":[[:space:]]*"[^"]*"' "$config_file" | head -1 | cut -d'"' -f4)"
                    echo "服务端口: $ONLYOFFICE_HTTP_PORT"
                fi
                
                return 0
            else
                log_error "OnlyOffice服务启动后健康检查失败，恢复备份配置"
                # 停止容器
                docker stop "$ONLYOFFICE_CONTAINER_NAME" >/dev/null 2>&1
                
                # 恢复备份配置
                if [[ -f "$backup_config" ]]; then
                    cp "$backup_config" "$config_file"
                    log_info "已恢复备份配置"
                    
                    # 重新启动容器
                    docker start "$ONLYOFFICE_CONTAINER_NAME" >/dev/null 2>&1
                    log_info "已使用备份配置重启容器"
                fi
                return 1
            fi
        else
            log_error "启动OnlyOffice容器失败，恢复备份配置"
            # 恢复备份配置
            if [[ -f "$backup_config" ]]; then
                cp "$backup_config" "$config_file"
                docker start "$ONLYOFFICE_CONTAINER_NAME" >/dev/null 2>&1
                log_info "已恢复备份配置并重启容器"
            fi
            return 1
        fi
    else
        log_info "✅ OnlyOffice配置更新完成"
        log_warn "OnlyOffice容器未运行，请手动重启容器以应用新配置"
        log_info "重启命令: docker restart $ONLYOFFICE_CONTAINER_NAME"
        
        # 显示新配置内容摘要
        log_step "新配置内容摘要："
        if [[ -f "$config_file" ]]; then
            echo "配置文件路径: $config_file"
            echo "配置文件大小: $(du -h "$config_file" | cut -f1)"
            echo "队列类型: $(grep -o '"type":[[:space:]]*"[^"]*"' "$config_file" | head -1 | cut -d'"' -f4)"
            echo "服务端口: $ONLYOFFICE_HTTP_PORT"
        fi
        
        return 0
    fi
}

# 显示帮助信息
show_help() {
    echo "OnlyOffice DocumentServer $ONLYOFFICE_VERSION 安装脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  无参数              执行OnlyOffice安装"
    echo "  --check-installed   检查OnlyOffice是否已安装"
    echo "  --check-packages    检查OnlyOffice安装包是否完整"
    echo "  --start             启动OnlyOffice服务"
    echo "  --stop              停止OnlyOffice服务"
    echo "  --update-config     更新OnlyOffice配置文件"
    echo "  --uninstall         卸载OnlyOffice"
    echo "  --diagnose          诊断OnlyOffice问题"
    echo "  --help              显示此帮助信息"
    echo ""
    echo "说明:"
    echo "  • --update-config会重新生成default.json并重启容器"
    echo "  • 配置更新前会自动备份现有配置文件"
    echo ""
    echo "示例:"
    echo "  $0                  # 安装OnlyOffice"
    echo "  $0 --start          # 启动OnlyOffice服务"
    echo "  $0 --diagnose       # 诊断OnlyOffice问题"
    echo ""
}

# 处理命令行参数
handle_arguments() {
    case "$1" in
        --check-installed)
            check_onlyoffice_installed_status
            ;;
        --check-packages)
            check_onlyoffice_packages
            ;;
        --start)
            start_onlyoffice_service
            ;;
        --stop)
            stop_onlyoffice_service
            ;;
        --update-config)
            update_onlyoffice_config
            ;;
        --uninstall)
            uninstall_onlyoffice
            ;;
        --diagnose)
            diagnose_onlyoffice_issues
            ;;
        --help)
            show_help
            ;;
        *)
            main "$@"
            ;;
    esac
}

# 错误处理
trap 'echo -e "\n${RED}OnlyOffice安装过程中出现错误${NC}"; exit 1' ERR

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    handle_arguments "$@"
fi