#!/bin/bash

# CentOS 7兼容性设置
if [[ ${BASH_VERSION%%.*} -lt 4 ]]; then
    echo "错误: 需要bash 4.0或更高版本，当前版本: $BASH_VERSION"
    exit 1
fi

#=============================================================================
# RocketMQ 5.3.3安装模块
# 版本: 1.0
# 用途: 在Docker环境中安装RocketMQ 5.3.3
# 依赖: packages/rocketmq/rocketmq-5.3.3.tar
# 
# 重要提醒: install.sh只需要确保整体流程OK，主要负责用户操作，
#          具体逻辑由各自组件的Install来完成！！！
#=============================================================================

# 移除 set -e 以避免状态检查时脚本中断
# set -e

# 调试模式支持
if [[ -n "$DEBUG" ]]; then
    set -x
fi

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BASE_DIR="$(dirname "$SCRIPT_DIR")"                        # packages/rocketmq
PACKAGES_DIR="$(dirname "$BASE_DIR")"                      # packages
ROOT_DIR="$(dirname "$PACKAGES_DIR")"                      # scripts

# 加载配置文件
if [[ -f "$ROOT_DIR/config.sh" ]]; then
    source "$ROOT_DIR/config.sh"
else
    echo "配置文件不存在: $ROOT_DIR/config.sh"
    exit 1
fi

# 加载通用函数库
if [[ -f "$ROOT_DIR/utils/common.sh" ]]; then
    source "$ROOT_DIR/utils/common.sh"
else
    # 基本的日志函数
    log_error() { echo -e "\033[0;31m[ERROR]\033[0m $1"; }
    log_info() { echo -e "\033[0;32m[INFO]\033[0m $1"; }
    log_warn() { echo -e "\033[1;33m[WARN]\033[0m $1"; }
    log_step() { echo -e "\033[0;35m[STEP]\033[0m $1"; }
    log_success() { echo -e "\033[0;32m[SUCCESS]\033[0m $1"; }
    log_title() { echo -e "\033[0;34m[TITLE]\033[0m $1"; }
fi

# ============================================================================
# 依赖检查函数
# ============================================================================

# 检查Docker依赖
check_docker_dependency() {
    log_step "检查Docker依赖"

    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        return 1
    fi

    if ! systemctl is-active docker &> /dev/null; then
        log_error "Docker服务未启动，请先启动Docker服务"
        log_info "启动命令: systemctl start docker"
        return 1
    fi

    if ! docker info &> /dev/null; then
        log_error "Docker守护进程无响应"
        return 1
    fi

    log_info "✅ Docker环境检查通过"
    return 0
}

# 检查网络依赖
check_network_dependency() {
    log_step "检查Docker网络"

    if ! docker network ls | grep -q "$DOCKER_NETWORK"; then
        log_warn "Docker网络不存在，将自动创建: $DOCKER_NETWORK"
        if ! docker network create "$DOCKER_NETWORK"; then
            log_error "Docker网络创建失败"
            return 1
        fi
        log_info "✅ Docker网络创建成功: $DOCKER_NETWORK"
    else
        log_info "✅ Docker网络已存在: $DOCKER_NETWORK"
    fi

    return 0
}

# 检查RocketMQ是否已安装
check_rocketmq_installed() {
    # 先检查Docker是否可用
    if ! command -v docker &> /dev/null; then
        return 1  # Docker未安装，RocketMQ肯定未安装
    fi
    
    if ! systemctl is-active docker &> /dev/null; then
        return 1  # Docker服务未运行，无法检查RocketMQ状态
    fi
    
    # 检查配置变量是否已加载
    if [[ -z "$ROCKETMQ_NAMESERVER_CONTAINER_NAME" ]] || [[ -z "$ROCKETMQ_BROKER_CONTAINER_NAME" ]]; then
        return 1  # 配置未加载，返回未安装
    fi
    
    # RocketMQ需要检查两个容器：NameServer和Broker
    local nameserver_running=false
    local broker_running=false
    local nameserver_exists=false
    local broker_exists=false
    
    # 检查NameServer容器
    if docker ps --format "{{.Names}}" 2>/dev/null | grep -q "^$ROCKETMQ_NAMESERVER_CONTAINER_NAME$"; then
        nameserver_running=true
    elif docker ps -a --format "{{.Names}}" 2>/dev/null | grep -q "^$ROCKETMQ_NAMESERVER_CONTAINER_NAME$"; then
        nameserver_exists=true
    fi
    
    # 检查Broker容器
    if docker ps --format "{{.Names}}" 2>/dev/null | grep -q "^$ROCKETMQ_BROKER_CONTAINER_NAME$"; then
        broker_running=true
    elif docker ps -a --format "{{.Names}}" 2>/dev/null | grep -q "^$ROCKETMQ_BROKER_CONTAINER_NAME$"; then
        broker_exists=true
    fi
    
    # 如果容器都在运行，进一步检查服务健康状态
    if [[ "$nameserver_running" == true ]] && [[ "$broker_running" == true ]]; then
        # 检查NameServer健康状态
        local nameserver_healthy=false
        if docker logs "$ROCKETMQ_NAMESERVER_CONTAINER_NAME" 2>&1 | grep -qE "(Name Server boot success|NameServer started)"; then
            nameserver_healthy=true
        fi

        # 检查Broker健康状态（优先检查启动成功日志）
        local broker_healthy=false
        local broker_logs=$(docker logs "$ROCKETMQ_BROKER_CONTAINER_NAME" 2>&1)

        # 先检查是否有启动成功的日志
        if echo "$broker_logs" | grep -qE "(broker.*boot success|Broker started|register to name server success)"; then
            broker_healthy=true   # 有启动成功日志，认为健康
        else
            # 如果没有启动成功日志，再检查最近的日志是否有Killed
            local recent_logs=$(echo "$broker_logs" | tail -5)
            if echo "$recent_logs" | grep -q "Killed"; then
                broker_healthy=false  # 最近有Killed日志，服务不健康
            else
                # 容器运行但没有明确的启动日志，通过端口检查判断
                if netstat -tlnp 2>/dev/null | grep -q ":${ROCKETMQ_BROKER_FAST_PORT}"; then
                    broker_healthy=true   # 端口监听正常，认为健康
                fi
            fi
        fi

        # 根据健康状态返回结果
        if [[ "$nameserver_healthy" == true ]] && [[ "$broker_healthy" == true ]]; then
            return 0  # 都在运行且健康
        else
            return 2  # 容器运行但服务不健康
        fi
    elif [[ "$nameserver_exists" == true ]] || [[ "$broker_exists" == true ]] || [[ "$nameserver_running" == true ]] || [[ "$broker_running" == true ]]; then
        return 2  # 部分安装或未完全启动
    else
        return 1  # 完全未安装
    fi
}

# 检查RocketMQ安装状态（供主脚本调用）
check_rocketmq_installed_status() {
    check_rocketmq_installed
    exit $?
}

# 检查RocketMQ相关包（供主脚本调用）
check_rocketmq_packages() {
    local errors=0
    
    # 检查RocketMQ Docker镜像文件
    local rocketmq_tar="$BASE_DIR/rocketmq-${ROCKETMQ_VERSION}.tar"
    if [[ -f "$rocketmq_tar" ]]; then
        local file_size=$(du -h "$rocketmq_tar" | cut -f1)
        echo "✓ RocketMQ ${ROCKETMQ_VERSION} Docker镜像 ($file_size)"
        
        # 检查文件大小
        local size_bytes=$(stat -c%s "$rocketmq_tar")
        local size_mb=$((size_bytes / 1024 / 1024))
        if [[ $size_mb -lt 300 ]]; then
            echo "⚠ RocketMQ镜像文件似乎过小 (${size_mb}MB)，请检查完整性"
            errors=$((errors + 1))
        fi
    else
        echo "✗ 缺失: RocketMQ ${ROCKETMQ_VERSION} Docker镜像"
        errors=$((errors + 1))
    fi
    
    # 检查脚本本身
    if [[ -x "${BASH_SOURCE[0]}" ]]; then
        echo "✓ RocketMQ安装脚本具有执行权限"
    else
        echo "⚠ RocketMQ安装脚本缺少执行权限"
        errors=$((errors + 1))
    fi
    
    exit $errors
}

# ============================================================================
# 目录和文件准备函数
# ============================================================================

# 创建RocketMQ目录结构
create_rocketmq_directories() {
    log_step "创建RocketMQ目录结构"

    local directories=(
        "$ROCKETMQ_DATA_DIR"
        "$ROCKETMQ_DATA_DIR/nameserver/logs"
        "$ROCKETMQ_DATA_DIR/nameserver/store"
        "$ROCKETMQ_DATA_DIR/broker/logs"
        "$ROCKETMQ_DATA_DIR/broker/store"
        "$ROCKETMQ_DATA_DIR/broker/conf"
    )

    for dir in "${directories[@]}"; do
        if [[ ! -d "$dir" ]]; then
            mkdir -p "$dir"
            log_info "创建目录: $dir"
        fi
    done

    # 设置目录权限（确保RocketMQ容器可以写入）
    chmod -R 777 "$ROCKETMQ_DATA_DIR"
    # 创建rocketmqlogs子目录
    mkdir -p "$ROCKETMQ_DATA_DIR/nameserver/logs/rocketmqlogs"
    mkdir -p "$ROCKETMQ_DATA_DIR/broker/logs/rocketmqlogs"
    chmod -R 777 "$ROCKETMQ_DATA_DIR"
    log_info "✅ RocketMQ目录结构创建完成"
    return 0
}

# 生成Broker配置文件
generate_broker_config() {
    log_step "生成RocketMQ Broker配置文件"

    local config_file="$ROCKETMQ_DATA_DIR/broker/conf/broker.conf"

    cat > "$config_file" << EOF
# RocketMQ Broker配置文件
# 生成时间: $(date)

# 集群名称
brokerClusterName=${ROCKETMQ_CLUSTER_NAME}

# Broker名称
brokerName=${ROCKETMQ_BROKER_NAME}

# Broker ID (0表示Master，>0表示Slave)
brokerId=0

# NameServer地址
namesrvAddr=namesrv:${ROCKETMQ_NAMESERVER_PORT}

# Broker对外服务的监听端口
listenPort=${ROCKETMQ_BROKER_FAST_PORT}

# 删除文件时间点，默认凌晨4点
deleteWhen=04

# 文件保留时间，默认72小时
fileReservedTime=72

# Broker角色
# - ASYNC_MASTER 异步复制Master
# - SYNC_MASTER 同步双写Master
# - SLAVE
brokerRole=ASYNC_MASTER

# 刷盘方式
# - ASYNC_FLUSH 异步刷盘
# - SYNC_FLUSH 同步刷盘
flushDiskType=ASYNC_FLUSH

# 自动创建Topic
autoCreateTopicEnable=true

# 自动创建订阅组
autoCreateSubscriptionGroup=true

# 发送消息线程池数量
sendMessageThreadPoolNums=128

# 拉取消息线程池数量
pullMessageThreadPoolNums=128

# Broker对外IP（Docker环境中重要）
brokerIP1=${ROCKETMQ_BROKER_IP}

# 存储路径
storePathRootDir=${ROCKETMQ_CONTAINER_STORE_PATH}
storePathCommitLog=${ROCKETMQ_CONTAINER_STORE_PATH}/commitlog

# 调度消息存储路径（修复NullPointerException）
storePathSchedule=${ROCKETMQ_CONTAINER_STORE_PATH}/schedule

# 内存映射文件大小
mapedFileSizeCommitLog=1073741824
mapedFileSizeConsumeQueue=300000

# 启用属性过滤器
enablePropertyFilter=false

# 启用消费队列扩展
enableConsumeQueueExt=false

# 调度消息服务配置（修复启动问题）
scheduleMessageEnable=true
EOF

    log_info "✅ Broker配置文件生成完成: $config_file"
    return 0
}

# ============================================================================
# Docker镜像和容器管理函数
# ============================================================================

# 加载RocketMQ Docker镜像
load_rocketmq_image() {
    log_step "加载RocketMQ Docker镜像"

    # 检查镜像是否已存在
    if docker images --format "{{.Repository}}:{{.Tag}}" | grep -q "^$ROCKETMQ_IMAGE$"; then
        log_info "✅ RocketMQ镜像已存在: $ROCKETMQ_IMAGE"
        return 0
    fi

    # 加载镜像文件
    local rocketmq_tar="$BASE_DIR/rocketmq-${ROCKETMQ_VERSION}.tar"
    if [[ -f "$rocketmq_tar" ]]; then
        log_info "加载镜像文件: $rocketmq_tar"
        if docker load -i "$rocketmq_tar"; then
            log_info "✅ RocketMQ镜像加载成功"
            return 0
        else
            log_error "镜像加载失败"
            return 1
        fi
    else
        log_warn "镜像文件不存在，尝试从Docker Hub拉取"
        if docker pull "$ROCKETMQ_IMAGE"; then
            log_info "✅ RocketMQ镜像拉取成功"
            return 0
        else
            log_error "镜像拉取失败"
            return 1
        fi
    fi
}

# 启动RocketMQ NameServer容器
start_nameserver_container() {
    log_step "启动RocketMQ NameServer容器"

    # 检查容器是否已存在
    if docker ps -a --format "{{.Names}}" | grep -q "^$ROCKETMQ_NAMESERVER_CONTAINER_NAME$"; then
        log_info "NameServer容器已存在，先删除旧容器"
        docker stop "$ROCKETMQ_NAMESERVER_CONTAINER_NAME" &> /dev/null
        docker rm "$ROCKETMQ_NAMESERVER_CONTAINER_NAME" &> /dev/null
    fi

    # 启动NameServer容器
    log_info "启动RocketMQ NameServer容器: $ROCKETMQ_NAMESERVER_CONTAINER_NAME"

    local docker_run_cmd=(
        docker run -d
        --name "$ROCKETMQ_NAMESERVER_CONTAINER_NAME"
        --network "$DOCKER_NETWORK"
        -p "${ROCKETMQ_NAMESERVER_PORT}:${ROCKETMQ_NAMESERVER_PORT}"
        -v "$ROCKETMQ_DATA_DIR/nameserver/logs:${ROCKETMQ_CONTAINER_LOG_PATH}"
        -v "$ROCKETMQ_DATA_DIR/nameserver/logs:${ROCKETMQ_CONTAINER_ROCKETMQLOGS_PATH}"
        -v "$ROCKETMQ_DATA_DIR/nameserver/store:${ROCKETMQ_CONTAINER_STORE_PATH}"
        -e "MAX_HEAP_SIZE=${ROCKETMQ_NAMESERVER_MAX_HEAP}"
        -e "HEAP_NEWSIZE=${ROCKETMQ_NAMESERVER_HEAP_NEWSIZE}"
        --memory="$ROCKETMQ_NAMESERVER_MEMORY_LIMIT"
        --cpus="$ROCKETMQ_CPU_LIMIT"
        --restart=unless-stopped
        "$ROCKETMQ_IMAGE"
        sh mqnamesrv
    )

    if "${docker_run_cmd[@]}"; then
        log_info "✅ RocketMQ NameServer容器启动成功"
        return 0
    else
        log_error "RocketMQ NameServer容器启动失败"
        return 1
    fi
}

# 启动RocketMQ Broker容器
start_broker_container() {
    log_step "启动RocketMQ Broker容器"

    # 检查容器是否已存在
    if docker ps -a --format "{{.Names}}" | grep -q "^$ROCKETMQ_BROKER_CONTAINER_NAME$"; then
        log_info "Broker容器已存在，先删除旧容器"
        docker stop "$ROCKETMQ_BROKER_CONTAINER_NAME" &> /dev/null
        docker rm "$ROCKETMQ_BROKER_CONTAINER_NAME" &> /dev/null
    fi

    # 启动Broker容器
    log_info "启动RocketMQ Broker容器: $ROCKETMQ_BROKER_CONTAINER_NAME"

    local docker_run_cmd=(
        docker run -d
        --name "$ROCKETMQ_BROKER_CONTAINER_NAME"
        --network "$DOCKER_NETWORK"
        -p "${ROCKETMQ_BROKER_PORT}:${ROCKETMQ_BROKER_PORT}"
        -p "${ROCKETMQ_BROKER_FAST_PORT}:${ROCKETMQ_BROKER_FAST_PORT}"
        -p "${ROCKETMQ_BROKER_HA_PORT}:${ROCKETMQ_BROKER_HA_PORT}"
        -v "$ROCKETMQ_DATA_DIR/broker/logs:${ROCKETMQ_CONTAINER_LOG_PATH}"
        -v "$ROCKETMQ_DATA_DIR/broker/logs:${ROCKETMQ_CONTAINER_ROCKETMQLOGS_PATH}"
        -v "$ROCKETMQ_DATA_DIR/broker/store:${ROCKETMQ_CONTAINER_STORE_PATH}"
        -v "$ROCKETMQ_DATA_DIR/broker/conf/broker.conf:${ROCKETMQ_CONTAINER_CONF_PATH}"
        --link "${ROCKETMQ_NAMESERVER_CONTAINER_NAME}:namesrv"
        -e "NAMESRV_ADDR=namesrv:${ROCKETMQ_NAMESERVER_PORT}"
        -e "MAX_HEAP_SIZE=${ROCKETMQ_BROKER_MAX_HEAP}"
        -e "HEAP_NEWSIZE=${ROCKETMQ_BROKER_HEAP_NEWSIZE}"
        --memory="$ROCKETMQ_BROKER_MEMORY_LIMIT"
        --cpus="$ROCKETMQ_CPU_LIMIT"
        --restart=unless-stopped
        "$ROCKETMQ_IMAGE"
        sh mqbroker -c ${ROCKETMQ_CONTAINER_CONF_PATH}
    )

    if "${docker_run_cmd[@]}"; then
        log_info "✅ RocketMQ Broker容器启动成功"
        return 0
    else
        log_error "RocketMQ Broker容器启动失败"
        return 1
    fi
}

# ============================================================================
# 健康检查和服务管理函数
# ============================================================================

# 执行RocketMQ健康检查
perform_rocketmq_health_check() {
    log_step "执行RocketMQ健康检查"

    local max_attempts=30
    local attempt=1

    while [[ $attempt -le $max_attempts ]]; do
        log_info "健康检查尝试 $attempt/$max_attempts"

        # 检查NameServer容器是否运行
        if ! docker ps --format "{{.Names}}" | grep -q "^$ROCKETMQ_NAMESERVER_CONTAINER_NAME$"; then
            log_error "RocketMQ NameServer容器未运行"
            return 1
        fi

        # 检查Broker容器是否运行
        if ! docker ps --format "{{.Names}}" | grep -q "^$ROCKETMQ_BROKER_CONTAINER_NAME$"; then
            log_error "RocketMQ Broker容器未运行"
            return 1
        fi

        # 检查NameServer端口
        if netstat -tlnp 2>/dev/null | grep -q ":${ROCKETMQ_NAMESERVER_PORT}"; then
            log_info "✅ RocketMQ NameServer端口监听正常"

            # 检查Broker端口
            if netstat -tlnp 2>/dev/null | grep -q ":${ROCKETMQ_BROKER_FAST_PORT}"; then
                log_info "✅ RocketMQ Broker端口监听正常"

                # 检查容器日志中是否有启动成功的标志（多种可能的日志格式）
                local nameserver_ready=false
                local broker_ready=false

                # NameServer启动检查（多种日志格式）
                if docker logs "$ROCKETMQ_NAMESERVER_CONTAINER_NAME" 2>&1 | grep -qE "(Name Server boot success|NameServer started|server started|startup completed)"; then
                    nameserver_ready=true
                    log_info "✅ RocketMQ NameServer启动完成"
                fi

                # Broker启动检查（多种日志格式）
                if docker logs "$ROCKETMQ_BROKER_CONTAINER_NAME" 2>&1 | grep -qE "(broker.*boot success|Broker started|broker started|startup completed|register to name server success)"; then
                    broker_ready=true
                    log_info "✅ RocketMQ Broker启动完成"
                fi

                # 如果两个服务都启动成功
                if [[ "$nameserver_ready" == true && "$broker_ready" == true ]]; then
                    log_info "✅ RocketMQ服务启动完成"
                    return 0
                fi

                # 如果端口监听正常但日志检查失败，进行更严格的检查
                if [[ $attempt -gt 15 ]]; then
                    log_info "进行严格的服务可用性检查..."

                    # 检查容器是否在重启状态
                    local broker_status=$(docker inspect rmqbroker --format '{{.State.Status}}' 2>/dev/null)
                    if [[ "$broker_status" == "restarting" ]]; then
                        log_warn "Broker容器处于重启状态，服务不稳定"
                        continue
                    fi

                    # 检查最近是否有错误日志
                    local recent_errors=$(docker logs rmqbroker 2>&1 | tail -10 | grep -i "exception\|error\|fail" | wc -l)
                    if [[ $recent_errors -gt 0 ]]; then
                        log_warn "Broker最近有错误日志，服务可能不稳定"
                        continue
                    fi

                    # 连续检查端口稳定性
                    local stable_count=0
                    for i in {1..5}; do
                        if timeout 2 bash -c "</dev/tcp/localhost/${ROCKETMQ_NAMESERVER_PORT}" 2>/dev/null && \
                           timeout 2 bash -c "</dev/tcp/localhost/${ROCKETMQ_BROKER_FAST_PORT}" 2>/dev/null; then
                            stable_count=$((stable_count + 1))
                        fi
                        sleep 1
                    done

                    if [[ $stable_count -eq 5 ]]; then
                        log_info "✅ RocketMQ端口连续5次连接测试通过，认为服务已启动"
                        return 0
                    else
                        log_warn "端口连接不稳定 ($stable_count/5)，继续等待"
                    fi
                fi
            fi
        fi

        log_info "等待RocketMQ服务启动... (${attempt}/${max_attempts})"
        sleep 5
        attempt=$((attempt + 1))
    done

    log_error "RocketMQ健康检查超时"
    log_info "请检查容器日志:"
    log_info "  NameServer: docker logs $ROCKETMQ_NAMESERVER_CONTAINER_NAME"
    log_info "  Broker: docker logs $ROCKETMQ_BROKER_CONTAINER_NAME"
    return 1
}

# 简化的健康检查（仅基于端口和容器状态）
simple_rocketmq_health_check() {
    log_step "执行简化RocketMQ健康检查"

    # 检查容器状态
    if ! docker ps --format "{{.Names}}" | grep -q "^$ROCKETMQ_NAMESERVER_CONTAINER_NAME$"; then
        log_error "RocketMQ NameServer容器未运行"
        return 1
    fi

    if ! docker ps --format "{{.Names}}" | grep -q "^$ROCKETMQ_BROKER_CONTAINER_NAME$"; then
        log_error "RocketMQ Broker容器未运行"
        return 1
    fi

    # 检查端口监听
    if netstat -tlnp 2>/dev/null | grep -q ":${ROCKETMQ_NAMESERVER_PORT}"; then
        log_info "✅ RocketMQ NameServer端口监听正常"

        if netstat -tlnp 2>/dev/null | grep -q ":${ROCKETMQ_BROKER_FAST_PORT}"; then
            log_info "✅ RocketMQ Broker端口监听正常"
            log_info "✅ RocketMQ服务基于端口检查认为已启动"
            return 0
        fi
    fi

    return 1
}

# 主安装函数
main() {
    log_title "开始安装RocketMQ ${ROCKETMQ_VERSION}"

    # 1. 检查依赖
    if ! check_docker_dependency; then
        return 1
    fi

    if ! check_network_dependency; then
        return 1
    fi

    # 2. 检查是否已安装
    check_rocketmq_installed
    local install_status=$?
    case $install_status in
        0)
            log_info "✅ RocketMQ已安装并运行中"
            log_info "NameServer地址: ${SERVER_IP}:${ROCKETMQ_NAMESERVER_PORT}"
            return 0
            ;;
        2)
            log_info "RocketMQ已安装但未完全运行，尝试启动"
            if start_rocketmq_services; then
                return 0
            else
                log_warn "启动失败，将重新安装"
            fi
            ;;
    esac

    # 3. 创建目录结构
    if ! create_rocketmq_directories; then
        return 1
    fi

    # 4. 生成配置文件
    if ! generate_broker_config; then
        return 1
    fi

    # 5. 加载Docker镜像
    if ! load_rocketmq_image; then
        return 1
    fi

    # 6. 启动NameServer容器
    if ! start_nameserver_container; then
        return 1
    fi

    # 7. 等待NameServer启动
    log_step "等待NameServer服务启动"
    sleep 10

    # 8. 启动Broker容器
    if ! start_broker_container; then
        return 1
    fi

    # 9. 等待服务启动
    log_step "等待RocketMQ服务启动"
    sleep 15

    # 10. 健康检查
    if perform_rocketmq_health_check; then
        log_success "🎉 RocketMQ ${ROCKETMQ_VERSION} 安装成功！"
    elif simple_rocketmq_health_check; then
        log_success "🎉 RocketMQ ${ROCKETMQ_VERSION} 安装成功！（基于简化检查）"
    else
        log_error "RocketMQ健康检查失败，请检查日志"
        return 1
    fi

    echo ""
    echo "📋 服务信息："
    echo "   • NameServer地址: ${SERVER_IP}:${ROCKETMQ_NAMESERVER_PORT}"
    echo "   • Broker端口: ${ROCKETMQ_BROKER_FAST_PORT}"
    echo "   • 数据目录: ${ROCKETMQ_DATA_DIR}"
    echo "   • 配置文件: ${ROCKETMQ_DATA_DIR}/broker/conf/broker.conf"
    echo ""
    echo "💡 提示："
    echo "   • 可以使用RocketMQ Console或客户端连接"
    echo "   • NameServer地址配置: ${SERVER_IP}:${ROCKETMQ_NAMESERVER_PORT}"
    echo ""
    return 0
}

# 卸载RocketMQ
uninstall_rocketmq() {
    log_title "卸载RocketMQ ${ROCKETMQ_VERSION}"

    echo ""
    echo "⚠️  警告: 此操作将："
    echo "   • 停止并删除RocketMQ容器（NameServer和Broker）"
    echo "   • 删除RocketMQ Docker镜像"
    echo "   • 保留配置文件和日志（可选择删除）"
    echo ""

    echo -n "确认卸载RocketMQ? (y/N): "
    read -r confirm_uninstall
    if [[ ! "$confirm_uninstall" =~ ^[Yy]$ ]]; then
        log_info "卸载取消"
        return 0
    fi

    # 停止并删除Broker容器
    log_step "停止并删除RocketMQ Broker容器"
    if docker ps --format "{{.Names}}" | grep -q "^$ROCKETMQ_BROKER_CONTAINER_NAME$"; then
        docker stop "$ROCKETMQ_BROKER_CONTAINER_NAME"
        log_info "RocketMQ Broker容器已停止"
    fi

    if docker ps -a --format "{{.Names}}" | grep -q "^$ROCKETMQ_BROKER_CONTAINER_NAME$"; then
        docker rm "$ROCKETMQ_BROKER_CONTAINER_NAME"
        log_info "RocketMQ Broker容器已删除"
    fi

    # 停止并删除NameServer容器
    log_step "停止并删除RocketMQ NameServer容器"
    if docker ps --format "{{.Names}}" | grep -q "^$ROCKETMQ_NAMESERVER_CONTAINER_NAME$"; then
        docker stop "$ROCKETMQ_NAMESERVER_CONTAINER_NAME"
        log_info "RocketMQ NameServer容器已停止"
    fi

    if docker ps -a --format "{{.Names}}" | grep -q "^$ROCKETMQ_NAMESERVER_CONTAINER_NAME$"; then
        docker rm "$ROCKETMQ_NAMESERVER_CONTAINER_NAME"
        log_info "RocketMQ NameServer容器已删除"
    fi

    # 删除Docker镜像
    echo -n "是否删除RocketMQ Docker镜像? (y/N): "
    read -r remove_image
    if [[ "$remove_image" =~ ^[Yy]$ ]]; then
        log_step "删除RocketMQ Docker镜像"
        if docker images --format "{{.Repository}}:{{.Tag}}" | grep -q "^$ROCKETMQ_IMAGE$"; then
            docker rmi "$ROCKETMQ_IMAGE"
            log_info "RocketMQ镜像已删除"
        fi
    fi

    # 删除配置文件和日志
    echo -n "是否删除配置文件和日志? (y/N): "
    read -r remove_data
    if [[ "$remove_data" =~ ^[Yy]$ ]]; then
        log_step "删除配置文件和日志"
        if [[ -d "$ROCKETMQ_DATA_DIR" ]]; then
            rm -rf "$ROCKETMQ_DATA_DIR"
            log_info "RocketMQ数据目录已删除"
        fi
    fi

    log_success "✅ RocketMQ卸载完成"
    return 0
}

# 显示帮助信息
# 更新RocketMQ配置
update_rocketmq_config() {
    log_title "更新RocketMQ配置"
    
    # 检查Docker依赖
    if ! check_docker_dependency; then
        return 1
    fi
    
    # 检查网络依赖
    if ! check_network_dependency; then
        return 1
    fi
    
    # 检查RocketMQ容器是否存在
    local nameserver_exists=false
    local broker_exists=false
    
    if docker ps -a --format "{{.Names}}" | grep -q "^$ROCKETMQ_NAMESERVER_CONTAINER_NAME$"; then
        nameserver_exists=true
    fi
    
    if docker ps -a --format "{{.Names}}" | grep -q "^$ROCKETMQ_BROKER_CONTAINER_NAME$"; then
        broker_exists=true
    fi
    
    if [[ "$nameserver_exists" == false && "$broker_exists" == false ]]; then
        log_error "RocketMQ容器不存在，请先安装RocketMQ"
        return 1
    fi
    
    # 检查配置文件目录是否存在
    if [[ ! -d "$ROCKETMQ_DATA_DIR/broker/conf" ]]; then
        log_error "RocketMQ配置目录不存在: $ROCKETMQ_DATA_DIR/broker/conf"
        return 1
    fi
    
    # 备份现有配置
    local config_file="$ROCKETMQ_DATA_DIR/broker/conf/broker.conf"
    local backup_config="$ROCKETMQ_DATA_DIR/broker/conf/broker.conf.backup.$(date +%Y%m%d_%H%M%S)"
    
    if [[ -f "$config_file" ]]; then
        log_step "备份现有配置文件"
        cp "$config_file" "$backup_config"
        log_info "配置已备份到: $backup_config"
    fi
    
    # 记录容器运行状态
    local nameserver_was_running=false
    local broker_was_running=false
    
    if docker ps --format "{{.Names}}" | grep -q "^$ROCKETMQ_NAMESERVER_CONTAINER_NAME$"; then
        nameserver_was_running=true
        log_info "RocketMQ NameServer容器当前正在运行"
    fi
    
    if docker ps --format "{{.Names}}" | grep -q "^$ROCKETMQ_BROKER_CONTAINER_NAME$"; then
        broker_was_running=true
        log_info "RocketMQ Broker容器当前正在运行"
    fi
    
    # 重新生成配置文件
    log_step "生成新的RocketMQ Broker配置文件"
    if ! generate_broker_config; then
        log_error "生成RocketMQ Broker配置文件失败"
        # 恢复备份配置
        if [[ -f "$backup_config" ]]; then
            cp "$backup_config" "$config_file"
            log_info "已恢复备份配置"
        fi
        return 1
    fi
    
    # 处理NameServer容器的重新创建（如果需要环境变量更新）
    if [[ "$nameserver_was_running" == true ]]; then
        log_step "重新创建RocketMQ NameServer容器以应用新的环境变量"
        log_warn "注意：Docker容器的环境变量只能在创建时设置，需要重新创建容器"

        # 停止并删除NameServer容器
        if docker stop "$ROCKETMQ_NAMESERVER_CONTAINER_NAME"; then
            log_info "RocketMQ NameServer容器已停止"
            sleep 3

            if docker rm "$ROCKETMQ_NAMESERVER_CONTAINER_NAME"; then
                log_info "旧RocketMQ NameServer容器已删除"
            else
                log_error "删除旧NameServer容器失败"
                return 1
            fi
        else
            log_error "停止RocketMQ NameServer容器失败"
            return 1
        fi

        # 重新创建NameServer容器
        if ! start_nameserver_container; then
            log_error "重新创建RocketMQ NameServer容器失败"
            return 1
        fi
        log_info "新RocketMQ NameServer容器已创建并启动"
    fi

    # 如果Broker容器正在运行，需要重新创建以应用新配置
    if [[ "$broker_was_running" == true ]]; then
        log_step "重新创建RocketMQ Broker容器以应用新配置（包括环境变量）"
        log_warn "注意：Docker容器的环境变量只能在创建时设置，需要重新创建容器"

        # 停止并删除Broker容器
        if docker stop "$ROCKETMQ_BROKER_CONTAINER_NAME"; then
            log_info "RocketMQ Broker容器已停止"
            sleep 3

            if docker rm "$ROCKETMQ_BROKER_CONTAINER_NAME"; then
                log_info "旧RocketMQ Broker容器已删除"
            else
                log_error "删除旧Broker容器失败"
                return 1
            fi
        else
            log_error "停止RocketMQ Broker容器失败"
            # 恢复备份配置
            if [[ -f "$backup_config" ]]; then
                cp "$backup_config" "$config_file"
                log_info "已恢复备份配置"
            fi
            return 1
        fi
        
        # 重新创建并启动Broker容器（使用新的环境变量）
        if start_broker_container; then
            log_info "新RocketMQ Broker容器已创建并启动"
            
            # 等待服务启动
            sleep 10
            
            # 健康检查
            if perform_rocketmq_health_check; then
                log_info "✅ RocketMQ配置更新成功，新配置已生效"
                
                # 显示新配置内容摘要
                log_step "当前RocketMQ配置摘要："
                if [[ -f "$config_file" ]]; then
                    echo "集群名称: $(grep "^brokerClusterName" "$config_file" | cut -d'=' -f2)"
                    echo "Broker名称: $(grep "^brokerName" "$config_file" | cut -d'=' -f2)"
                    echo "监听端口: $(grep "^listenPort" "$config_file" | cut -d'=' -f2)"
                    echo "配置文件路径: $config_file"
                fi
                
                return 0
            else
                log_error "RocketMQ服务启动后健康检查失败，恢复备份配置"
                # 停止容器
                docker stop "$ROCKETMQ_BROKER_CONTAINER_NAME" >/dev/null 2>&1
                
                # 恢复备份配置
                if [[ -f "$backup_config" ]]; then
                    cp "$backup_config" "$config_file"
                    log_info "已恢复备份配置"
                    
                    # 重新启动容器
                    docker start "$ROCKETMQ_BROKER_CONTAINER_NAME" >/dev/null 2>&1
                    log_info "已使用备份配置重启容器"
                fi
                return 1
            fi
        else
            log_error "启动RocketMQ Broker容器失败，恢复备份配置"
            # 恢复备份配置
            if [[ -f "$backup_config" ]]; then
                cp "$backup_config" "$config_file"
                docker start "$ROCKETMQ_BROKER_CONTAINER_NAME" >/dev/null 2>&1
                log_info "已恢复备份配置并重启容器"
            fi
            return 1
        fi
    else
        log_info "✅ RocketMQ配置更新完成"
        log_warn "RocketMQ Broker容器未运行，请手动重启容器以应用新配置"
        log_info "重启命令: docker restart $ROCKETMQ_BROKER_CONTAINER_NAME"
        
        # 显示新配置内容摘要
        log_step "新配置内容摘要："
        if [[ -f "$config_file" ]]; then
            echo "集群名称: $(grep "^brokerClusterName" "$config_file" | cut -d'=' -f2)"
            echo "Broker名称: $(grep "^brokerName" "$config_file" | cut -d'=' -f2)"
            echo "监听端口: $(grep "^listenPort" "$config_file" | cut -d'=' -f2)"
            echo "配置文件路径: $config_file"
        fi
        
        return 0
    fi
}

show_help() {
    echo "RocketMQ ${ROCKETMQ_VERSION} 安装脚本使用说明:"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  (无参数)          执行RocketMQ安装"
    echo "  --check-installed 检查RocketMQ是否已安装"
    echo "  --check-packages  检查RocketMQ安装包完整性"
    echo "  --install         执行RocketMQ安装"
    echo "  --uninstall       卸载RocketMQ"
    echo "  --start           启动RocketMQ服务"
    echo "  --stop            停止RocketMQ服务"
    echo "  --restart         重启RocketMQ服务"
    echo "  --update-config   更新RocketMQ配置文件"
    echo "  --status          显示RocketMQ状态"
    echo "  --health-check    执行健康检查"
    echo "  --help            显示此帮助信息"
    echo ""
    echo "说明:"
    echo "  • --update-config会重新生成broker.conf并重启Broker容器"
    echo "  • 配置更新前会自动备份现有配置文件"
    echo ""
    echo "示例:"
    echo "  $0                # 安装RocketMQ"
    echo "  $0 --start        # 启动RocketMQ服务"
    echo "  $0 --status       # 查看服务状态"
    echo ""
}

# 启动RocketMQ服务
start_rocketmq_services() {
    log_title "启动RocketMQ服务"

    # 检查容器是否存在
    if ! docker ps -a --format "{{.Names}}" | grep -q "^$ROCKETMQ_NAMESERVER_CONTAINER_NAME$"; then
        log_error "RocketMQ NameServer容器不存在，请先安装RocketMQ"
        return 1
    fi

    if ! docker ps -a --format "{{.Names}}" | grep -q "^$ROCKETMQ_BROKER_CONTAINER_NAME$"; then
        log_error "RocketMQ Broker容器不存在，请先安装RocketMQ"
        return 1
    fi

    # 启动NameServer
    log_step "启动RocketMQ NameServer容器"
    if ! docker ps --format "{{.Names}}" | grep -q "^$ROCKETMQ_NAMESERVER_CONTAINER_NAME$"; then
        if docker start "$ROCKETMQ_NAMESERVER_CONTAINER_NAME"; then
            log_info "RocketMQ NameServer容器启动成功"
            sleep 5
        else
            log_error "RocketMQ NameServer容器启动失败"
            return 1
        fi
    else
        log_info "RocketMQ NameServer已在运行"
    fi

    # 启动Broker
    log_step "启动RocketMQ Broker容器"
    if ! docker ps --format "{{.Names}}" | grep -q "^$ROCKETMQ_BROKER_CONTAINER_NAME$"; then
        if docker start "$ROCKETMQ_BROKER_CONTAINER_NAME"; then
            log_info "RocketMQ Broker容器启动成功"
            sleep 5
        else
            log_error "RocketMQ Broker容器启动失败"
            return 1
        fi
    else
        log_info "RocketMQ Broker已在运行"
    fi

    # 健康检查
    if perform_rocketmq_health_check; then
        log_success "✅ RocketMQ服务启动成功"
        echo "NameServer地址: ${SERVER_IP}:${ROCKETMQ_NAMESERVER_PORT}"
        return 0
    else
        log_error "RocketMQ服务启动后健康检查失败"
        return 1
    fi
}

# 停止RocketMQ服务
stop_rocketmq_services() {
    log_title "停止RocketMQ服务"

    local stopped_count=0

    # 停止Broker
    if docker ps --format "{{.Names}}" | grep -q "^$ROCKETMQ_BROKER_CONTAINER_NAME$"; then
        log_step "停止RocketMQ Broker容器"
        if docker stop "$ROCKETMQ_BROKER_CONTAINER_NAME"; then
            log_info "RocketMQ Broker容器已停止"
            stopped_count=$((stopped_count + 1))
        else
            log_error "RocketMQ Broker容器停止失败"
        fi
    else
        log_info "RocketMQ Broker容器未运行"
    fi

    # 停止NameServer
    if docker ps --format "{{.Names}}" | grep -q "^$ROCKETMQ_NAMESERVER_CONTAINER_NAME$"; then
        log_step "停止RocketMQ NameServer容器"
        if docker stop "$ROCKETMQ_NAMESERVER_CONTAINER_NAME"; then
            log_info "RocketMQ NameServer容器已停止"
            stopped_count=$((stopped_count + 1))
        else
            log_error "RocketMQ NameServer容器停止失败"
        fi
    else
        log_info "RocketMQ NameServer容器未运行"
    fi

    if [[ $stopped_count -gt 0 ]]; then
        log_success "✅ RocketMQ服务停止成功"
    else
        log_info "✅ RocketMQ服务已停止"
    fi

    return 0
}

# 重启RocketMQ服务
restart_rocketmq_services() {
    log_title "重启RocketMQ服务"

    if stop_rocketmq_services && start_rocketmq_services; then
        log_success "✅ RocketMQ服务重启成功"
        return 0
    else
        log_error "RocketMQ服务重启失败"
        return 1
    fi
}

# 显示RocketMQ状态
show_rocketmq_status() {
    log_title "RocketMQ服务状态"

    echo ""
    echo "📋 容器状态："

    # NameServer状态
    if docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep "$ROCKETMQ_NAMESERVER_CONTAINER_NAME"; then
        echo ""
    else
        echo "   NameServer容器未运行"
    fi

    # Broker状态
    if docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep "$ROCKETMQ_BROKER_CONTAINER_NAME"; then
        echo ""
    else
        echo "   Broker容器未运行"
    fi

    echo "🔗 连接信息："
    echo "   • NameServer地址: ${SERVER_IP}:${ROCKETMQ_NAMESERVER_PORT}"
    echo "   • Broker端口: ${ROCKETMQ_BROKER_FAST_PORT}"
    echo ""

    echo "📁 目录信息："
    echo "   • 数据目录: ${ROCKETMQ_DATA_DIR}"
    echo "   • NameServer日志: ${ROCKETMQ_DATA_DIR}/nameserver/logs"
    echo "   • Broker日志: ${ROCKETMQ_DATA_DIR}/broker/logs"
    echo "   • Broker配置: ${ROCKETMQ_DATA_DIR}/broker/conf/broker.conf"
    echo ""
}

# 处理命令行参数
handle_arguments() {
    case "$1" in
        --check-installed)
            check_rocketmq_installed_status
            ;;
        --check-packages)
            check_rocketmq_packages
            ;;
        --install)
            main
            ;;
        --uninstall)
            uninstall_rocketmq
            ;;
        --start)
            start_rocketmq_services
            ;;
        --stop)
            stop_rocketmq_services
            ;;
        --restart)
            restart_rocketmq_services
            ;;
        --update-config)
            update_rocketmq_config
            ;;
        --status)
            show_rocketmq_status
            ;;
        --health-check)
            perform_rocketmq_health_check
            ;;
        --help)
            show_help
            ;;
        "")
            main
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 错误处理
trap 'echo -e "\n❌ RocketMQ脚本执行过程中出现错误"; exit 1' ERR

# 执行参数处理
handle_arguments "$@"