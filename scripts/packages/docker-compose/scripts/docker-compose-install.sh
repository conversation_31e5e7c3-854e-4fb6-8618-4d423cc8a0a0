#!/bin/bash

# CentOS 7兼容性设置
if [[ ${BASH_VERSION%%.*} -lt 4 ]]; then
    echo "错误: 需要bash 4.0或更高版本，当前版本: $BASH_VERSION"
    exit 1
fi

#=============================================================================
# Docker Compose离线安装模块
# 版本: 1.0
# 用途: 离线安装Docker Compose (使用二进制文件)
# 依赖: packages/docker-compose/docker-compose-Linux-x86_64
# 支持: CentOS 7 完全离线环境
#=============================================================================

# 移除 set -e 以避免状态检查时脚本中断
# set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_title() {
    echo -e "${BLUE}[TITLE]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BASE_DIR="$(dirname "$SCRIPT_DIR")"                        # packages/docker-compose
PACKAGES_DIR="$(dirname "$BASE_DIR")"                      # packages
ROOT_DIR="$(dirname "$PACKAGES_DIR")"                      # scripts

# 加载配置文件
if [[ -f "$ROOT_DIR/config.sh" ]]; then
    source "$ROOT_DIR/config.sh"
else
    echo "配置文件不存在: $ROOT_DIR/config.sh"
    echo "SCRIPT_DIR: $SCRIPT_DIR"
    echo "BASE_DIR: $BASE_DIR"
    echo "PACKAGES_DIR: $PACKAGES_DIR"
    echo "ROOT_DIR: $ROOT_DIR"
    exit 1
fi

# Docker Compose版本信息
DOCKER_COMPOSE_VERSION=${DOCKER_COMPOSE_VERSION:-"v2.21.0"}

# 检查Docker Compose是否已安装
check_docker_compose_installed() {
    log_title "检查Docker Compose安装状态"
    
    if command -v docker-compose &> /dev/null; then
        local compose_version
        compose_version=$(docker-compose --version 2>/dev/null || echo "未知版本")
        log_info "Docker Compose已安装: $compose_version"
        
        # 检查版本是否可以正常工作
        if docker-compose version &> /dev/null; then
            log_info "Docker Compose运行正常"
            echo -n "Docker Compose已安装且运行正常，是否重新安装? (y/N): "
            read -r reinstall
            if [[ ! "$reinstall" =~ ^[Yy]$ ]]; then
                log_info "跳过Docker Compose安装"
                return 0
            fi
        else
            log_warn "Docker Compose已安装但无法正常运行"
        fi
    else
        log_info "Docker Compose未安装，开始安装"
    fi
    
    return 1
}

# 检查系统要求
check_system_requirements() {
    log_title "检查系统要求"
    
    # 检查操作系统
    if [[ -f /etc/centos-release ]]; then
        local centos_version
        centos_version=$(cat /etc/centos-release)
        log_info "系统版本: $centos_version"
    else
        log_warn "非CentOS系统，可能存在兼容性问题"
    fi
    
    # 检查内核版本
    local kernel_version
    kernel_version=$(uname -r)
    log_info "内核版本: $kernel_version"
    
    # 检查架构
    local arch
    arch=$(uname -m)
    if [[ "$arch" != "x86_64" ]]; then
        log_error "不支持的架构: $arch，需要x86_64"
        return 1
    fi
    log_info "系统架构: $arch"
    
    # 检查Docker依赖
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，Docker Compose需要先安装Docker"
        log_info "请先安装Docker后再安装Docker Compose"
        return 1
    fi
    
    if ! systemctl is-active docker &> /dev/null; then
        log_warn "Docker服务未运行，Docker Compose需要Docker服务运行"
        log_info "Docker Compose仍可安装，但建议先启动Docker服务"
    fi
    
    log_info "系统要求检查通过"
    return 0
}

# 检查Docker Compose安装包
check_docker_compose_package() {
    log_title "检查Docker Compose安装包"
    
    local compose_binary="$BASE_DIR/docker-compose-Linux-x86_64"
    
    if [[ ! -f "$compose_binary" ]]; then
        log_error "Docker Compose二进制文件不存在: $compose_binary"
        log_info "请先下载Docker Compose二进制文件"
        return 1
    fi
    
    local file_size
    file_size=$(du -h "$compose_binary" | cut -f1)
    log_info "找到Docker Compose二进制文件: docker-compose-Linux-x86_64 ($file_size)"
    
    # 检查文件完整性（简单检查）
    if [[ $(stat -c%s "$compose_binary") -lt 10000000 ]]; then  # 小于10MB认为不完整
        log_warn "Docker Compose二进制文件过小，可能不完整"
        echo -n "是否继续安装? (y/N): "
        read -r continue_install
        if [[ ! "$continue_install" =~ ^[Yy]$ ]]; then
            return 1
        fi
    fi
    
    # 检查文件是否有执行权限
    if [[ ! -x "$compose_binary" ]]; then
        log_step "添加执行权限"
        chmod +x "$compose_binary"
    fi
    
    log_info "Docker Compose安装包检查通过"
    return 0
}

# 检查Docker Compose相关包（供主脚本调用）
check_docker_compose_packages() {
    local errors=0
    
    # 检查Docker Compose二进制文件
    local compose_binary="$BASE_DIR/docker-compose-Linux-x86_64"
    if [[ -f "$compose_binary" ]]; then
        local file_size
        file_size=$(du -h "$compose_binary" | cut -f1)
        echo "✓ Docker Compose Linux x86_64 二进制文件 ($file_size)"
        
        # 检查文件大小
        if [[ $(stat -c%s "$compose_binary") -lt 10000000 ]]; then
            echo "⚠ Docker Compose二进制文件过小，可能不完整"
            errors=$((errors + 1))
        fi
        
        # 检查执行权限
        if [[ -x "$compose_binary" ]]; then
            echo "✓ Docker Compose二进制文件具有执行权限"
        else
            echo "⚠ Docker Compose二进制文件缺少执行权限，自动修复中..."
            chmod +x "$compose_binary" 2>/dev/null && echo "✓ 已自动修复执行权限" || {
                echo "✗ 权限修复失败，建议手动执行: chmod +x $compose_binary"
                errors=$((errors + 1))
            }
        fi
    else
        echo "✗ 缺失: Docker Compose Linux x86_64 二进制文件"
        errors=$((errors + 1))
    fi
    
    # 检查脚本本身（动态修复权限）
    local script_self="$SCRIPT_DIR/docker-compose-install.sh"
    if [[ -x "$script_self" ]]; then
        echo "✓ Docker Compose安装脚本具有执行权限"
    else
        echo "⚠ Docker Compose安装脚本缺少执行权限，自动修复中..."
        chmod +x "$script_self" 2>/dev/null && echo "✓ 已自动修复执行权限" || {
            echo "✗ 权限修复失败，建议手动执行: chmod +x $script_self"
            errors=$((errors + 1))
        }
    fi
    
    exit $errors
}

# 安装Docker Compose二进制文件
install_docker_compose_binary() {
    log_title "安装Docker Compose二进制文件（离线安装）"
    
    local compose_binary="$BASE_DIR/docker-compose-Linux-x86_64"
    local target_path="/usr/local/bin/docker-compose"
    
    # 停止现有的docker-compose进程（如果有）
    if pgrep docker-compose &> /dev/null; then
        log_step "停止现有docker-compose进程"
        pkill docker-compose &> /dev/null || true
        sleep 2
    fi
    
    # 备份现有的docker-compose（如果存在）
    if [[ -f "$target_path" ]]; then
        local backup_path="/usr/local/bin/docker-compose.backup.$(date +%Y%m%d_%H%M%S)"
        log_step "备份现有docker-compose到: $backup_path"
        cp "$target_path" "$backup_path"
    fi
    
    # 复制二进制文件
    log_step "安装Docker Compose二进制文件到 /usr/local/bin/"
    if cp "$compose_binary" "$target_path"; then
        log_info "Docker Compose二进制文件复制成功"
    else
        log_error "Docker Compose二进制文件复制失败"
        return 1
    fi
    
    # 设置执行权限
    log_step "设置执行权限"
    chmod +x "$target_path"
    
    # 验证安装
    log_step "验证Docker Compose安装"
    if command -v docker-compose &> /dev/null; then
        local compose_version
        compose_version=$(docker-compose --version 2>/dev/null || echo "版本检测失败")
        log_info "Docker Compose安装成功: $compose_version"
    else
        log_error "Docker Compose安装失败，命令不可用"
        return 1
    fi
    
    # 测试Docker Compose功能
    log_step "测试Docker Compose功能"
    if docker-compose version &> /dev/null; then
        log_info "Docker Compose功能测试通过"
    else
        log_warn "Docker Compose功能测试失败，但安装已完成"
        log_info "可能需要Docker服务运行才能完全测试功能"
    fi
    
    log_info "Docker Compose二进制文件安装完成"
}

# 验证Docker Compose安装
verify_docker_compose_installation() {
    log_title "验证Docker Compose安装"
    
    # 检查Docker Compose版本
    log_step "检查Docker Compose版本"
    if command -v docker-compose &> /dev/null; then
        local compose_version
        compose_version=$(docker-compose --version 2>/dev/null || echo "版本检测失败")
        log_info "Docker Compose版本: $compose_version"
    else
        log_error "Docker Compose命令不可用"
        return 1
    fi
    
    # 检查Docker Compose服务信息
    log_step "检查Docker Compose服务信息"
    if docker-compose version &> /dev/null; then
        log_info "Docker Compose服务连接正常"
        
        # 显示详细版本信息
        echo ""
        log_step "Docker Compose详细信息"
        docker-compose version
    else
        log_warn "Docker Compose无法连接到Docker服务"
        log_info "这可能是因为Docker服务未运行，但Docker Compose已正确安装"
    fi
    
    # 检查安装路径
    local compose_path
    compose_path=$(which docker-compose 2>/dev/null || echo "未找到")
    log_info "Docker Compose安装路径: $compose_path"
    
    log_info "Docker Compose安装验证完成"
}

# 显示安装后信息
show_post_install_info() {
    log_title "安装后信息"
    
    echo ""
    echo "✅ Docker Compose离线安装完成！"
    echo ""
    echo "📋 安装信息:"
    echo "   • Docker Compose版本: $(docker-compose --version 2>/dev/null || echo '检查中...')"
    echo "   • 安装路径: $(which docker-compose 2>/dev/null || echo '检查中...')"
    echo "   • 安装方式: 离线二进制安装"
    echo "   • 运行模式: 完全离线模式"
    
    # 检查Docker依赖状态
    if command -v docker &> /dev/null; then
        if systemctl is-active docker &> /dev/null; then
            echo "   • Docker依赖: 已安装且运行中"
        else
            echo "   • Docker依赖: 已安装但未启动"
            echo "   • 建议启动Docker服务: systemctl start docker"
        fi
    else
        echo "   • Docker依赖: 未安装（需要先安装Docker）"
    fi
    
    echo ""
    echo "🔧 常用命令:"
    echo "   • 查看版本: docker-compose --version"
    echo "   • 查看详细信息: docker-compose version"
    echo "   • 启动服务: docker-compose up -d"
    echo "   • 停止服务: docker-compose down"
    echo "   • 查看运行状态: docker-compose ps"
    echo ""
    echo "⚠️  注意事项:"
    echo "   • Docker Compose需要Docker服务运行才能正常工作"
    echo "   • 使用前请确保Docker服务已启动: systemctl start docker"
    echo "   • 离线安装模式，无需互联网连接"
    echo ""
}

# 主函数
main() {
    log_title "Docker Compose环境离线安装开始"
    echo "目标版本: Docker Compose $DOCKER_COMPOSE_VERSION"
    echo "安装方式: 完全离线二进制安装"
    echo "适用系统: CentOS 7 及兼容系统"
    echo ""

    # 检查是否已安装
    if check_docker_compose_installed; then
        return 0
    fi

    # 检查系统要求
    check_system_requirements || exit 1

    # 检查安装包
    check_docker_compose_package || exit 1

    # 安装Docker Compose二进制文件
    install_docker_compose_binary || exit 1

    # 验证安装
    verify_docker_compose_installation || exit 1

    # 显示安装信息
    show_post_install_info

    log_title "Docker Compose环境离线安装完成"
    return 0
}

# 启动Docker Compose（实际上是检查Docker服务状态）
start_docker_compose() {
    log_title "启动Docker Compose服务检查"

    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        return 1
    fi

    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，Docker Compose需要Docker支持"
        return 1
    fi

    if ! systemctl is-active docker &> /dev/null; then
        log_warn "Docker服务未运行，正在启动Docker服务..."
        if systemctl start docker; then
            log_info "✅ Docker服务启动成功"
            sleep 3
        else
            log_error "❌ Docker服务启动失败"
            return 1
        fi
    fi

    log_step "检查Docker Compose功能"
    if docker-compose version &> /dev/null; then
        log_info "✅ Docker Compose功能正常"
        return 0
    else
        log_error "❌ Docker Compose功能异常"
        return 1
    fi
}

# 停止Docker Compose相关服务
stop_docker_compose() {
    log_title "停止Docker Compose相关服务"

    if ! command -v docker-compose &> /dev/null; then
        log_info "Docker Compose未安装，无需停止"
        return 0
    fi

    # 查找当前目录下的docker-compose.yml文件并停止相关服务
    local compose_files_found=0
    
    # 在常见位置查找docker-compose文件
    local search_paths=("$PWD" "$ROOT_DIR" "$INSTALL_DIR")
    
    for search_path in "${search_paths[@]}"; do
        if [[ -n "$search_path" && -d "$search_path" ]]; then
            find "$search_path" -name "docker-compose.yml" -o -name "docker-compose.yaml" 2>/dev/null | while read -r compose_file; do
                local compose_dir
                compose_dir=$(dirname "$compose_file")
                log_step "在 $compose_dir 中发现docker-compose文件，停止相关服务"
                
                cd "$compose_dir" || continue
                if docker-compose ps -q 2>/dev/null | grep -q .; then
                    log_info "停止docker-compose服务..."
                    docker-compose down 2>/dev/null || log_warn "停止服务时出现警告"
                    compose_files_found=1
                else
                    log_info "该位置没有运行中的docker-compose服务"
                fi
            done
        fi
    done

    if [[ $compose_files_found -eq 0 ]]; then
        log_info "未找到活跃的docker-compose服务"
    fi

    log_info "✅ Docker Compose相关服务停止操作完成"
    return 0
}

# 更新Docker Compose配置
update_docker_compose_config() {
    log_title "更新Docker Compose配置"

    # 检查Docker Compose是否已安装
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，无法更新配置"
        return 1
    fi

    # Docker Compose主要是通过docker-compose.yml文件进行配置
    # 这里我们提供一些配置检查和建议
    
    log_step "检查Docker Compose配置环境"
    
    # 检查Docker服务状态
    if ! systemctl is-active docker &> /dev/null; then
        log_warn "Docker服务未运行，建议先启动Docker服务"
        log_info "启动命令: systemctl start docker"
    fi
    
    # 检查当前目录是否有docker-compose文件
    local compose_file=""
    if [[ -f "docker-compose.yml" ]]; then
        compose_file="docker-compose.yml"
    elif [[ -f "docker-compose.yaml" ]]; then
        compose_file="docker-compose.yaml"
    fi
    
    if [[ -n "$compose_file" ]]; then
        log_info "发现配置文件: $compose_file"
        
        # 验证docker-compose文件语法
        log_step "验证docker-compose文件语法"
        if docker-compose -f "$compose_file" config &> /dev/null; then
            log_info "✅ docker-compose文件语法正确"
            
            # 显示配置摘要
            log_step "显示配置文件摘要"
            echo "服务配置概览:"
            docker-compose -f "$compose_file" config --services 2>/dev/null | while read -r service; do
                echo "  - $service"
            done
        else
            log_error "❌ docker-compose文件语法错误"
            log_info "请检查 $compose_file 文件的语法"
            return 1
        fi
        
        # 检查是否有环境变量文件
        if [[ -f ".env" ]]; then
            log_info "发现环境变量文件: .env"
            log_step ".env 文件变量数量: $(grep -c "^[^#]" .env 2>/dev/null || echo 0)"
        else
            log_info "未找到 .env 文件（这是可选的）"
        fi
        
    else
        log_warn "当前目录未找到docker-compose.yml或docker-compose.yaml文件"
        log_info "Docker Compose的配置主要通过这些文件进行"
        log_info "如果您需要创建配置文件，请参考Docker Compose官方文档"
    fi
    
    # 检查Docker网络配置
    log_step "检查Docker网络配置"
    if docker network ls &> /dev/null; then
        local networks_count
        networks_count=$(docker network ls --format "{{.Name}}" | wc -l)
        log_info "当前Docker网络数量: $networks_count"
        
        # 检查是否有自定义网络
        if docker network ls --format "{{.Name}}" | grep -v -E "^(bridge|host|none)$" &> /dev/null; then
            log_info "✅ 检测到自定义Docker网络"
        else
            log_info "仅使用默认Docker网络"
        fi
    else
        log_warn "无法检查Docker网络配置"
    fi
    
    log_info "✅ Docker Compose配置检查完成"
    log_info "注意: Docker Compose的配置更新主要通过修改docker-compose.yml文件实现"
    return 0
}

# 检查Docker Compose安装状态（供主脚本调用）
check_docker_compose_installed_status() {
    # 更严格的Docker Compose检查：直接检查二进制文件是否存在且可执行
    local compose_binary=""
    if [[ -f /usr/local/bin/docker-compose ]] && [[ -x /usr/local/bin/docker-compose ]]; then
        compose_binary="/usr/local/bin/docker-compose"
    elif [[ -f /usr/bin/docker-compose ]] && [[ -x /usr/bin/docker-compose ]]; then
        compose_binary="/usr/bin/docker-compose"
    else
        # Docker Compose二进制文件不存在或不可执行
        return 1  # 未安装
    fi

    # 尝试执行Docker Compose版本检查确认其正常工作
    if ! "$compose_binary" --version &> /dev/null; then
        return 1  # 未安装（二进制文件损坏）
    fi

    # 检查Docker依赖
    if ! command -v docker &> /dev/null; then
        return 2  # 已安装但Docker未安装(待启动Docker)
    fi

    if ! systemctl is-active docker &> /dev/null; then
        return 2  # 已安装但Docker服务未启动(待启动)
    fi

    # Docker Compose已安装且Docker服务正常
    return 0  # 已启动
}

# 卸载Docker Compose
uninstall_docker_compose() {
    log_title "Docker Compose离线环境卸载"

    echo ""
    echo "⚠️  警告: 此操作将卸载Docker Compose环境，包括："
    echo "   • 删除Docker Compose二进制文件"
    echo "   • 清理相关配置"
    echo ""

    echo -n "确认卸载Docker Compose? (y/N): "
    read -r confirm_uninstall
    if [[ ! "$confirm_uninstall" =~ ^[Yy]$ ]]; then
        log_info "卸载取消"
        return 0
    fi

    echo ""
    log_info "开始卸载Docker Compose..."

    # 停止可能的docker-compose服务
    log_step "停止Docker Compose相关服务"
    stop_docker_compose

    # 删除Docker Compose二进制文件
    log_step "删除Docker Compose二进制文件"
    local compose_paths=("/usr/local/bin/docker-compose" "/usr/bin/docker-compose")
    local removed_count=0
    
    for compose_path in "${compose_paths[@]}"; do
        if [[ -f "$compose_path" ]]; then
            log_info "删除: $compose_path"
            rm -f "$compose_path"
            removed_count=$((removed_count + 1))
        fi
    done
    
    if [[ $removed_count -eq 0 ]]; then
        log_info "未找到需要删除的Docker Compose二进制文件"
    else
        log_info "已删除 $removed_count 个Docker Compose二进制文件"
    fi

    # 清理shell命令缓存，防止command -v docker-compose返回缓存的路径
    log_step "清理系统缓存"
    hash -r 2>/dev/null || true

    log_info "✅ Docker Compose离线环境卸载完成"
    return 0
}

# 显示帮助信息
show_help() {
    echo "Docker Compose离线安装脚本使用说明:"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  (无参数)          执行Docker Compose离线安装"
    echo "  --check-installed 检查Docker Compose是否已安装"
    echo "  --check-packages  检查Docker Compose安装包是否完整"
    echo "  --start           启动Docker Compose服务检查"
    echo "  --stop            停止Docker Compose相关服务"
    echo "  --update-config   更新Docker Compose配置检查"
    echo "  --uninstall       卸载Docker Compose"
    echo "  --help           显示此帮助信息"
    echo ""
    echo "说明:"
    echo "  • 完全离线安装，无需互联网连接"
    echo "  • 需要预先下载docker-compose-Linux-x86_64二进制文件"
    echo "  • 适用于CentOS 7及兼容系统"
    echo "  • 需要先安装Docker才能正常使用Docker Compose"
    echo "  • --update-config主要用于检查和验证现有配置文件"
    echo ""
}

# 处理命令行参数
handle_arguments() {
    case "$1" in
        --check-installed)
            check_docker_compose_installed_status
            exit_code=$?
            exit $exit_code
            ;;
        --check-packages)
            check_docker_compose_packages
            ;;
        --uninstall)
            uninstall_docker_compose
            ;;
        --start)
            start_docker_compose
            ;;
        --stop)
            stop_docker_compose
            ;;
        --update-config)
            update_docker_compose_config
            ;;
        --help)
            show_help
            ;;
        *)
            main "$@"
            ;;
    esac
}

# 错误处理（仅在主安装过程中启用）
# trap 'echo -e "\n${RED}Docker Compose离线安装过程中出现错误${NC}"; exit 1' ERR

# 执行参数处理
handle_arguments "$@"