#!/bin/bash

# CentOS 7兼容性设置
if [[ ${BASH_VERSION%%.*} -lt 4 ]]; then
    echo "错误: 需要bash 4.0或更高版本，当前版本: $BASH_VERSION"
    exit 1
fi

#=============================================================================
# Docker环境离线安装模块
# 版本: 2.0
# 用途: 离线安装Docker CE 28.3.2 (使用tar.gz二进制包)
# 依赖: packages/docker/docker-28.3.2.tgz
# 支持: CentOS 7 完全离线环境
#=============================================================================

# 移除 set -e 以避免状态检查时脚本中断
# set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_title() {
    echo -e "${BLUE}[TITLE]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BASE_DIR="$(dirname "$SCRIPT_DIR")"                        # packages/docker
PACKAGES_DIR="$(dirname "$BASE_DIR")"                      # packages
ROOT_DIR="$(dirname "$PACKAGES_DIR")"                      # scripts

# 加载配置文件
if [[ -f "$ROOT_DIR/config.sh" ]]; then
    source "$ROOT_DIR/config.sh"
else
    echo "配置文件不存在: $ROOT_DIR/config.sh"
    echo "SCRIPT_DIR: $SCRIPT_DIR"
    echo "BASE_DIR: $BASE_DIR"
    echo "PACKAGES_DIR: $PACKAGES_DIR"
    echo "ROOT_DIR: $ROOT_DIR"
    exit 1
fi

# 检查Docker是否已安装
check_docker_installed() {
    log_title "检查Docker安装状态"
    
    if command -v docker &> /dev/null; then
        local docker_version
        docker_version=$(docker --version 2>/dev/null || echo "未知版本")
        log_info "Docker已安装: $docker_version"
        
        if docker info &> /dev/null; then
            log_info "Docker服务正在运行"
            echo -n "Docker已安装且运行正常，是否重新安装? (y/N): "
            read -r reinstall
            if [[ ! "$reinstall" =~ ^[Yy]$ ]]; then
                log_info "跳过Docker安装"
                return 0
            fi
        else
            log_warn "Docker已安装但服务未运行"
        fi
    else
        log_info "Docker未安装，开始安装"
    fi
    
    return 1
}

# 检查系统要求
check_system_requirements() {
    log_title "检查系统要求"
    
    # 检查操作系统
    if [[ -f /etc/centos-release ]]; then
        local centos_version
        centos_version=$(cat /etc/centos-release)
        log_info "系统版本: $centos_version"
    else
        log_warn "非CentOS系统，可能存在兼容性问题"
    fi
    
    # 检查内核版本
    local kernel_version
    kernel_version=$(uname -r)
    log_info "内核版本: $kernel_version"
    
    # 检查架构
    local arch
    arch=$(uname -m)
    if [[ "$arch" != "x86_64" ]]; then
        log_error "不支持的架构: $arch，需要x86_64"
        return 1
    fi
    log_info "系统架构: $arch"
    
    # 检查磁盘空间
    local available_space
    available_space=$(df -BG /usr | awk 'NR==2 {print $4}' | sed 's/G//')
    if [[ "$available_space" -lt 2 ]]; then
        log_error "磁盘空间不足，需要至少2GB可用空间"
        return 1
    fi
    log_info "可用磁盘空间: ${available_space}GB"
    
    log_info "系统要求检查通过"
    return 0
}

# 检查Docker安装包
check_docker_package() {
    log_title "检查Docker安装包"
    
    local docker_tgz="$PACKAGES_DIR/docker/docker-${DOCKER_VERSION}.tgz"
    
    if [[ ! -f "$docker_tgz" ]]; then
        log_error "Docker安装包不存在: $docker_tgz"
        log_info "请先下载Docker安装包"
        return 1
    fi
    
    local file_size
    file_size=$(du -h "$docker_tgz" | cut -f1)
    log_info "找到Docker安装包: docker-${DOCKER_VERSION}.tgz ($file_size)"
    
    # 检查文件完整性（简单检查）
    if [[ $(stat -c%s "$docker_tgz") -lt 50000000 ]]; then  # 小于50MB认为不完整
        log_warn "Docker安装包文件过小，可能不完整"
        echo -n "是否继续安装? (y/N): "
        read -r continue_install
        if [[ ! "$continue_install" =~ ^[Yy]$ ]]; then
            return 1
        fi
    fi
    
    log_info "Docker安装包检查通过"
    return 0
}

# 检查Docker相关包（供主脚本调用）
check_docker_packages() {
    local errors=0
    
    # 检查Docker tar.gz包
    local docker_tgz="$PACKAGES_DIR/docker/docker-${DOCKER_VERSION}.tgz"
    if [[ -f "$docker_tgz" ]]; then
        local file_size
        file_size=$(du -h "$docker_tgz" | cut -f1)
        echo "✓ Docker ${DOCKER_VERSION} tar.gz包 ($file_size)"
        
        # 检查文件大小
        if [[ $(stat -c%s "$docker_tgz") -lt 50000000 ]]; then
            echo "⚠ Docker安装包文件过小，可能不完整"
            errors=$((errors + 1))
        fi
    else
        echo "✗ 缺失: Docker ${DOCKER_VERSION} tar.gz包"
        errors=$((errors + 1))
    fi
    
    # 检查Docker配置文件（可选）
    local docker_config="$PACKAGES_DIR/docker/docker-daemon.json"
    if [[ -f "$docker_config" ]]; then
        echo "✓ Docker配置文件"
    else
        echo "- Docker配置文件 (可选，将使用默认配置)"
    fi
    
    # 检查脚本本身（动态修复权限）
    local script_self="$SCRIPT_DIR/docker-install.sh"
    if [[ -x "$script_self" ]]; then
        echo "✓ Docker安装脚本具有执行权限"
    else
        echo "⚠ Docker安装脚本缺少执行权限，自动修复中..."
        chmod +x "$script_self" 2>/dev/null && echo "✓ 已自动修复执行权限" || {
            echo "✗ 权限修复失败，建议手动执行: chmod +x $script_self"
            errors=$((errors + 1))
        }
    fi
    
    exit $errors
}

# 安装系统依赖
install_dependencies() {
    log_title "安装系统依赖"
    
    # 检查基本工具是否存在（离线安装模式，不使用包管理器）
    local missing_tools=()
    local required_tools=("tar" "gzip")
    
    for tool in "${required_tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            missing_tools+=("$tool")
        fi
    done
    
    if [[ ${#missing_tools[@]} -gt 0 ]]; then
        log_error "缺少必要工具: ${missing_tools[*]}"
        log_error "请确保系统已安装基本工具：tar, gzip"
        return 1
    fi
    
    log_info "基本工具检查通过（离线安装模式）"
    
    # 检查iptables（Docker网络需要）
    if ! command -v iptables &> /dev/null; then
        log_warn "未找到iptables，Docker网络功能可能受限"
        log_info "建议安装iptables：yum install -y iptables-services"
    else
        log_info "iptables已安装"
    fi
}

# 卸载旧版本Docker（如果存在）
remove_old_docker() {
    log_title "卸载旧版本Docker"
    
    # 离线安装模式：仅检查Docker进程和服务，不使用包管理器
    log_info "离线安装模式：检查现有Docker进程"
    
    # 停止Docker服务（如果存在）
    if systemctl is-active docker &> /dev/null; then
        log_step "停止现有Docker服务"
        systemctl stop docker &> /dev/null || true
        systemctl stop docker.socket &> /dev/null || true
    fi
    
    # 检查是否存在Docker命令
    if command -v docker &> /dev/null; then
        log_warn "检测到已安装的Docker，将覆盖现有安装"
        local docker_version
        docker_version=$(docker --version 2>/dev/null || echo "未知版本")
        log_info "当前版本: $docker_version"
    else
        log_info "未检测到已安装的Docker"
    fi
    
    # 清理可能存在的Docker进程
    if pgrep dockerd &> /dev/null; then
        log_step "停止Docker守护进程"
        pkill dockerd &> /dev/null || true
        sleep 2
    fi
}

# 安装Docker二进制文件（离线安装）
install_docker_binary() {
    log_title "安装Docker二进制文件（离线安装）"
    
    local docker_tgz="$BASE_DIR/docker-${DOCKER_VERSION}.tgz"
    local temp_dir="/tmp/docker-install"
    
    # 创建临时目录
    log_step "创建临时安装目录"
    rm -rf "$temp_dir"
    mkdir -p "$temp_dir"
    
    # 解压Docker包
    log_step "解压Docker离线安装包"
    cd "$temp_dir"
    if ! tar -xzf "$docker_tgz"; then
        log_error "解压Docker安装包失败"
        return 1
    fi
    
    # 检查解压结果
    if [[ ! -d "$temp_dir/docker" ]]; then
        log_error "Docker包结构异常，未找到docker目录"
        return 1
    fi
    
    # 显示包含的二进制文件
    log_step "检查Docker二进制文件"
    ls -la "$temp_dir/docker/"
    
    # 复制二进制文件到系统路径
    log_step "安装Docker二进制文件到 /usr/bin/"
    cp "$temp_dir/docker/"* /usr/bin/
    chmod +x /usr/bin/docker*
    
    # 创建Docker数据目录
    log_step "创建Docker数据目录"
    mkdir -p /var/lib/docker
    
    # 验证安装
    log_step "验证Docker二进制文件安装"
    if command -v docker &> /dev/null; then
        local docker_version
        docker_version=$(docker --version)
        log_info "Docker二进制文件安装成功: $docker_version"
    else
        log_error "Docker二进制文件安装失败"
        return 1
    fi
    
    # 验证dockerd
    if command -v dockerd &> /dev/null; then
        log_info "Docker守护进程二进制文件安装成功"
    else
        log_error "Docker守护进程二进制文件安装失败"
        return 1
    fi
    
    # 清理临时文件
    log_step "清理临时文件"
    rm -rf "$temp_dir"
    
    log_info "Docker二进制文件安装完成"
}

# 创建docker用户组
create_docker_group() {
    log_title "创建docker用户组"
    
    if getent group docker > /dev/null 2>&1; then
        log_info "docker用户组已存在"
    else
        log_step "创建docker用户组"
        groupadd docker
        log_info "docker用户组创建成功"
    fi
    
    # 添加当前用户到docker组（如果不是root）
    if [[ $EUID -ne 0 ]]; then
        local current_user
        current_user=$(whoami)
        log_step "将用户 $current_user 添加到docker组"
        usermod -aG docker "$current_user"
        log_info "用户已添加到docker组，注销后重新登录生效"
    fi
}

# 创建systemd服务文件（基于离线二进制安装）
create_systemd_services() {
    log_title "创建systemd服务文件"
    
    # 创建docker服务文件（简化版，适用于二进制安装）
    log_step "创建docker.service"
    cat > /etc/systemd/system/docker.service << 'EOF'
[Unit]
Description=Docker Application Container Engine
Documentation=https://docs.docker.com
After=network-online.target firewalld.service
Wants=network-online.target

[Service]
Type=notify
# 启动Docker守护进程
ExecStart=/usr/bin/dockerd
# 重载配置
ExecReload=/bin/kill -s HUP $MAINPID
# 等待时间
TimeoutStartSec=0
# 重启设置
RestartSec=2
Restart=always
# 启动限制
StartLimitBurst=3
StartLimitInterval=60s
# 资源限制
LimitNOFILE=infinity
LimitNPROC=infinity
LimitCORE=infinity
# 任务限制
TasksMax=infinity
# 启用委托
Delegate=yes
# 进程管理
KillMode=process
# OOM调整
OOMScoreAdjust=-500

[Install]
WantedBy=multi-user.target
EOF

    # 创建docker.socket（可选，用于socket激活）
    log_step "创建docker.socket"
    cat > /etc/systemd/system/docker.socket << 'EOF'
[Unit]
Description=Docker Socket for the API
PartOf=docker.service

[Socket]
ListenStream=/var/run/docker.sock
SocketMode=0660
SocketUser=root
SocketGroup=docker

[Install]
WantedBy=sockets.target
EOF
    
    log_info "systemd服务文件创建完成"
}

# 配置Docker daemon
configure_docker_daemon() {
    log_title "配置Docker daemon"
    
    local daemon_config="/etc/docker/daemon.json"
    
    # 创建配置目录
    mkdir -p /etc/docker
    
    # 检查是否存在配置文件
    if [[ -f "$PACKAGES_DIR/docker/docker-daemon.json" ]]; then
        log_step "使用预置的Docker配置"
        cp "$PACKAGES_DIR/docker/docker-daemon.json" "$daemon_config"
    else
        log_step "创建Docker配置（离线安装优化）"
        
        # 检查是否有iptables，据此调整配置
        if command -v iptables &> /dev/null; then
            # 标准配置（有iptables） - 离线优化版本 + 中国镜像源
            cat > "$daemon_config" << 'EOF'
{
    "registry-mirrors": [
        "https://mirror.ccs.tencentyun.com",
        "https://docker.m.daocloud.io",
        "https://registry.cn-hangzhou.aliyuncs.com",
        "https://docker.mirrors.ustc.edu.cn",
        "https://docker.1panel.live",
        "https://hub-mirror.c.163.com"
    ],
    "log-driver": "json-file",
    "log-opts": {
        "max-size": "100m",
        "max-file": "3"
    },
    "storage-driver": "overlay2",
    "log-level": "warn"
}
EOF
            log_info "使用标准Docker配置（含中国镜像源，iptables可用）"
        else
            # 无iptables的配置（使用host网络模式） - 离线优化版本 + 中国镜像源
            cat > "$daemon_config" << 'EOF'
{
    "registry-mirrors": [
        "https://mirror.ccs.tencentyun.com",
        "https://docker.m.daocloud.io",
        "https://registry.cn-hangzhou.aliyuncs.com",
        "https://docker.mirrors.ustc.edu.cn",
        "https://docker.1panel.live",
        "https://hub-mirror.c.163.com"
    ],
    "log-driver": "json-file",
    "log-opts": {
        "max-size": "100m",
        "max-file": "3"
    },
    "storage-driver": "overlay2",
    "log-level": "warn",
    "iptables": false,
    "ip-forward": false,
    "ip-masq": false
}
EOF
            log_warn "使用无iptables配置（含中国镜像源，网络功能受限）"
            log_info "建议安装iptables获得完整Docker网络功能"
        fi
    fi
    
    log_info "Docker daemon配置完成: $daemon_config"
}

# 启动Docker服务（离线二进制安装版本）
start_docker_services() {
    log_title "启动Docker服务"
    
    # 重新加载systemd配置
    log_step "重新加载systemd配置"
    systemctl daemon-reload
    
    # 启用服务
    log_step "启用Docker服务"
    systemctl enable docker.service
    systemctl enable docker.socket
    
    # 启动Docker服务
    log_step "启动Docker服务"
    systemctl start docker.service
    
    # 等待服务启动
    log_step "等待Docker服务启动"
    sleep 5
    
    # 如果启动失败，尝试重启
    if ! systemctl is-active docker &> /dev/null; then
        log_step "首次启动失败，尝试重启"
        systemctl restart docker.service
        sleep 10
    fi
    
    # 检查服务状态
    log_step "检查Docker服务状态"
    if systemctl is-active docker &> /dev/null; then
        log_info "✅ Docker服务启动成功"
        systemctl status docker --no-pager -l | head -10
    else
        log_error "❌ Docker服务启动失败"
        log_info "服务状态详情:"
        systemctl status docker --no-pager -l
        log_info "Docker守护进程日志:"
        journalctl -u docker --no-pager -l -n 20
        return 1
    fi
    
    # 检查Docker socket
    if [[ -S /var/run/docker.sock ]]; then
        log_info "✅ Docker socket创建成功"
    else
        log_warn "⚠️ Docker socket未找到"
    fi
}

# 验证Docker安装
verify_docker_installation() {
    log_title "验证Docker安装"
    
    # 检查Docker版本
    log_step "检查Docker版本"
    if command -v docker &> /dev/null; then
        local docker_version
        docker_version=$(docker --version)
        log_info "Docker版本: $docker_version"
    else
        log_error "Docker命令不可用"
        return 1
    fi
    
    # 检查Docker信息
    log_step "检查Docker信息"
    if docker info &> /dev/null; then
        log_info "Docker守护进程运行正常"
    else
        log_error "无法连接到Docker守护进程"
        return 1
    fi
    
    # 运行测试容器（如果有网络支持）
    log_step "运行Hello World测试"
    if command -v iptables &> /dev/null; then
        if docker run --rm hello-world &> /dev/null; then
            log_info "Docker Hello World测试通过"
        else
            log_warn "Docker Hello World测试失败，但基本功能可能正常"
        fi
    else
        log_warn "跳过Hello World测试（缺少iptables网络支持）"
        log_info "Docker基本功能已安装，需要网络支持的功能可能受限"
    fi
    
    # 显示Docker信息摘要
    log_step "Docker信息摘要"
    docker version --format "Client: {{.Client.Version}}, Server: {{.Server.Version}}"
    docker info --format "Storage Driver: {{.Driver}}, Cgroup Driver: {{.CgroupDriver}}"
    
    log_info "Docker安装验证完成"
}

# 创建Docker网络
create_docker_network() {
    log_title "创建Docker网络"
    
    local network_name="$DOCKER_NETWORK"
    
    # 检查是否有iptables支持
    if ! command -v iptables &> /dev/null; then
        log_warn "缺少iptables，跳过自定义网络创建"
        log_info "Docker将使用默认网络配置"
        return 0
    fi
    
    if docker network ls | grep -q "$network_name" 2>/dev/null; then
        log_info "Docker网络已存在: $network_name"
    else
        log_step "创建Docker网络: $network_name"
        if docker network create "$network_name" 2>/dev/null; then
            log_info "Docker网络创建成功: $network_name"
        else
            log_warn "Docker网络创建失败，将使用默认网络"
            log_info "这可能是由于网络配置限制导致的"
        fi
    fi
}

# 同步系统时间（离线模式 - 仅显示当前时间）
sync_system_time() {
    log_title "系统时间检查"
    
    # 离线安装模式：不进行网络时间同步，仅显示当前时间
    log_info "当前系统时间: $(date)"
    log_info "离线安装模式：跳过网络时间同步"
    log_warn "建议手动确保系统时间正确，避免容器时间问题"
}

# 显示安装后信息
show_post_install_info() {
    log_title "安装后信息"
    
    echo ""
    echo "✅ Docker离线安装完成！"
    echo ""
    echo "📋 安装信息:"
    echo "   • Docker版本: $(docker --version)"
    echo "   • 存储驱动: $(docker info --format '{{.Driver}}' 2>/dev/null || echo '检查中...')"
    echo "   • 安装方式: 离线二进制安装"
    echo "   • 运行模式: 完全离线模式"
    
    # 检查网络配置
    if command -v iptables &> /dev/null; then
        echo "   • 网络支持: 完整（iptables可用）"
        if docker network ls | grep -q "$DOCKER_NETWORK" 2>/dev/null; then
            echo "   • 自定义网络: $DOCKER_NETWORK (已创建)"
        fi
    else
        echo "   • 网络支持: 受限（缺少iptables）"
        echo "   • 建议手动安装iptables包以获得完整网络功能"
    fi
    
    echo ""
    echo "🔧 常用命令:"
    echo "   • 查看版本: docker --version"
    echo "   • 查看信息: docker info"
    echo "   • 查看镜像: docker images"
    echo "   • 查看容器: docker ps -a"
    echo ""
    echo "⚠️  注意事项:"
    echo "   • Docker服务已设置为开机自启"
    echo "   • 非root用户需要重新登录后才能使用docker命令"
    if ! command -v iptables &> /dev/null; then
        echo "   • 网络功能受限，建议安装iptables包"
    fi
    echo "   • 离线安装模式，无需互联网连接"
    echo ""
}

# 主函数
main() {
    log_title "Docker环境离线安装开始"
    echo "目标版本: Docker CE $DOCKER_VERSION"
    echo "安装方式: 完全离线二进制安装"
    echo "适用系统: CentOS 7 及兼容系统"
    echo ""

    # 检查是否已安装
    if check_docker_installed; then
        return 0
    fi

    # 检查系统要求
    check_system_requirements || exit 1

    # 检查安装包
    check_docker_package || exit 1

    # 安装系统依赖
    install_dependencies

    # 卸载旧版本
    remove_old_docker

    # 安装Docker二进制文件
    install_docker_binary || exit 1

    # 创建用户组
    create_docker_group

    # 创建systemd服务
    create_systemd_services

    # 配置Docker daemon
    configure_docker_daemon

    # 启动服务
    start_docker_services || exit 1

    # 验证安装
    verify_docker_installation || exit 1

    # 创建网络（如果支持）
    if command -v iptables &> /dev/null; then
        create_docker_network
    else
        log_info "跳过网络创建（无iptables支持）"
    fi

    # 同步系统时间（离线模式）
    sync_system_time

    # 显示安装信息
    show_post_install_info

    log_title "Docker环境离线安装完成"
    return 0
}

# 错误处理
trap 'echo -e "\n${RED}Docker离线安装过程中出现错误${NC}"; exit 1' ERR

# 启动Docker服务
start_docker() {
    log_title "启动Docker服务"

    if systemctl is-active docker &> /dev/null; then
        log_info "Docker服务已在运行"
        return 0
    fi

    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        return 1
    fi

    log_step "启动Docker服务"
    if systemctl start docker; then
        log_info "✅ Docker服务启动成功"
        return 0
    else
        log_error "❌ Docker服务启动失败"
        return 1
    fi
}

# 停止Docker服务
stop_docker() {
    log_title "停止Docker服务"

    if ! systemctl is-active docker &> /dev/null; then
        log_info "Docker服务已停止"
        return 0
    fi

    log_step "停止Docker服务"
    if systemctl stop docker; then
        log_info "✅ Docker服务停止成功"
        return 0
    else
        log_error "❌ Docker服务停止失败"
        return 1
    fi
}

# 更新Docker配置
update_docker_config() {
    log_title "更新Docker配置"

    # 检查Docker是否已安装
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，无法更新配置"
        return 1
    fi

    # 备份现有配置
    local daemon_config="/etc/docker/daemon.json"
    local backup_config="/etc/docker/daemon.json.backup.$(date +%Y%m%d_%H%M%S)"

    if [[ -f "$daemon_config" ]]; then
        log_step "备份现有配置文件"
        cp "$daemon_config" "$backup_config"
        log_info "配置已备份到: $backup_config"
    fi

    # 重新生成配置文件
    log_step "生成新的Docker配置文件"
    configure_docker_daemon

    # 验证配置文件格式
    if command -v python3 &> /dev/null; then
        if ! python3 -m json.tool "$daemon_config" > /dev/null 2>&1; then
            log_error "配置文件格式错误，恢复备份"
            if [[ -f "$backup_config" ]]; then
                cp "$backup_config" "$daemon_config"
                log_info "已恢复备份配置"
            fi
            return 1
        fi
        log_info "✅ 配置文件格式验证通过"
    else
        log_warn "无法验证JSON格式（缺少python3），跳过验证"
    fi

    # 重启Docker服务以应用新配置
    log_step "重启Docker服务以应用新配置"
    if systemctl is-active docker &> /dev/null; then
        log_info "重新加载systemd配置"
        systemctl daemon-reload

        log_info "重启Docker服务"
        if systemctl restart docker; then
            # 等待Docker服务完全启动
            sleep 5

            # 验证服务状态
            if systemctl is-active docker &> /dev/null && docker info &> /dev/null; then
                log_info "✅ Docker服务重启成功，新配置已生效"

                # 显示新配置内容
                log_step "当前Docker配置内容："
                if [[ -f "$daemon_config" ]]; then
                    cat "$daemon_config"
                fi

                return 0
            else
                log_error "Docker服务重启后状态异常，恢复备份配置"
                if [[ -f "$backup_config" ]]; then
                    cp "$backup_config" "$daemon_config"
                    systemctl restart docker
                    log_info "已恢复备份配置并重启服务"
                fi
                return 1
            fi
        else
            log_error "Docker服务重启失败，恢复备份配置"
            if [[ -f "$backup_config" ]]; then
                cp "$backup_config" "$daemon_config"
                systemctl restart docker
                log_info "已恢复备份配置并重启服务"
            fi
            return 1
        fi
    else
        log_info "✅ Docker配置更新完成"
        log_warn "Docker服务未运行，请手动启动服务以应用新配置"
        log_info "启动命令: systemctl start docker"
        return 0
    fi
}

# 处理命令行参数
handle_arguments() {
    case "$1" in
        --check-installed)
            check_docker_installed_status
            exit_code=$?
            exit $exit_code
            ;;
        --check-packages)
            check_docker_packages
            ;;
        --uninstall)
            uninstall_docker
            ;;
        --start)
            start_docker
            ;;
        --stop)
            stop_docker
            ;;
        --update-config)
            update_docker_config
            ;;
        --help)
            show_help
            ;;
        *)
            main "$@"
            ;;
    esac
}

# 检查Docker安装状态（供主脚本调用）
check_docker_installed_status() {
    # 在DEBUG模式下输出详细检查信息
    if [[ -n "$DEBUG" ]]; then
        echo "DEBUG: 开始检查Docker状态" >&2
        echo "DEBUG: /usr/bin/docker存在: $([[ -f /usr/bin/docker ]] && echo 'yes' || echo 'no')" >&2
        echo "DEBUG: /usr/local/bin/docker存在: $([[ -f /usr/local/bin/docker ]] && echo 'yes' || echo 'no')" >&2
        echo "DEBUG: command -v docker: $(command -v docker 2>/dev/null || echo 'not found')" >&2
        echo "DEBUG: systemctl is-active docker: $(systemctl is-active docker 2>/dev/null || echo 'inactive')" >&2
        echo "DEBUG: systemctl is-enabled docker: $(systemctl is-enabled docker 2>/dev/null || echo 'disabled')" >&2
        echo "DEBUG: service file exists: $([[ -f /etc/systemd/system/docker.service ]] && echo 'yes' || echo 'no')" >&2
    fi

    # 更严格的Docker检查：直接检查二进制文件是否存在且可执行
    local docker_binary=""
    if [[ -f /usr/bin/docker ]] && [[ -x /usr/bin/docker ]]; then
        docker_binary="/usr/bin/docker"
    elif [[ -f /usr/local/bin/docker ]] && [[ -x /usr/local/bin/docker ]]; then
        docker_binary="/usr/local/bin/docker"
    else
        # Docker二进制文件不存在或不可执行
        if [[ -n "$DEBUG" ]]; then
            echo "DEBUG: Docker二进制文件不存在或不可执行，返回未安装状态" >&2
        fi
        return 1  # 未安装
    fi

    # 尝试执行Docker版本检查确认其正常工作
    if ! "$docker_binary" --version &> /dev/null; then
        if [[ -n "$DEBUG" ]]; then
            echo "DEBUG: Docker版本检查失败，返回未安装状态" >&2
        fi
        return 1  # 未安装（二进制文件损坏）
    fi

    # 检查Docker服务状态
    if systemctl is-active docker &> /dev/null; then
        if [[ -n "$DEBUG" ]]; then
            echo "DEBUG: Docker服务正在运行，返回已启动状态" >&2
        fi
        return 0  # 已启动
    elif systemctl is-enabled docker &> /dev/null 2>&1 || [[ -f /etc/systemd/system/docker.service ]]; then
        if [[ -n "$DEBUG" ]]; then
            echo "DEBUG: Docker已安装但未启动，返回待启动状态" >&2
        fi
        return 2  # 已安装但未启动(待启动)
    else
        if [[ -n "$DEBUG" ]]; then
            echo "DEBUG: Docker服务未配置，返回未安装状态" >&2
        fi
        return 1  # 未安装（服务未配置）
    fi
}

# 卸载Docker
uninstall_docker() {
    log_title "Docker离线环境卸载"

    echo ""
    echo "⚠️  警告: 此操作将卸载Docker环境，包括："
    echo "   • 停止Docker服务"
    echo "   • 删除Docker二进制文件"
    echo "   • 删除systemd服务文件"
    echo "   • 删除Docker配置文件"
    echo ""

    echo -n "确认卸载Docker环境? (y/N): "
    read -r confirm_uninstall
    if [[ ! "$confirm_uninstall" =~ ^[Yy]$ ]]; then
        log_info "卸载取消"
        return 0
    fi

    echo ""
    log_info "开始卸载Docker环境..."

    # 停止Docker服务
    log_step "停止Docker服务"
    systemctl stop docker 2>/dev/null || true
    systemctl stop docker.socket 2>/dev/null || true
    systemctl stop containerd 2>/dev/null || true

    # 禁用服务
    log_step "禁用Docker服务"
    systemctl disable docker 2>/dev/null || true
    systemctl disable docker.socket 2>/dev/null || true
    systemctl disable containerd 2>/dev/null || true

    # 删除Docker二进制文件
    log_step "删除Docker二进制文件"
    rm -f /usr/bin/docker*

    # 删除systemd服务文件
    log_step "删除systemd服务文件"
    rm -f /etc/systemd/system/docker.service
    rm -f /etc/systemd/system/docker.socket
    rm -f /etc/systemd/system/containerd.service

    # 删除配置文件
    log_step "删除配置文件"
    rm -rf /etc/docker

    # 重新加载systemd
    systemctl daemon-reload

    # 清理shell命令缓存，防止command -v docker返回缓存的路径
    log_step "清理系统缓存"
    hash -r 2>/dev/null || true

    log_info "✅ Docker离线环境卸载完成"
    return 0
}

# 显示帮助信息
show_help() {
    echo "Docker离线安装脚本使用说明:"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  (无参数)          执行Docker离线安装"
    echo "  --check-installed 检查Docker是否已安装"
    echo "  --check-packages  检查Docker安装包是否完整"
    echo "  --start           启动Docker服务"
    echo "  --stop            停止Docker服务"
    echo "  --update-config   更新Docker配置文件"
    echo "  --uninstall       卸载Docker"
    echo "  --help           显示此帮助信息"
    echo ""
    echo "说明:"
    echo "  • 完全离线安装，无需互联网连接"
    echo "  • 需要预先下载docker-${DOCKER_VERSION}.tgz包"
    echo "  • 适用于CentOS 7及兼容系统"
    echo "  • --update-config会重新生成daemon.json并重启服务"
    echo ""
}

# 执行参数处理
handle_arguments "$@"