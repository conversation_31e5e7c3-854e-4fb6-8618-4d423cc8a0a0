#!/bin/bash

# CentOS 7兼容性设置
if [[ ${BASH_VERSION%%.*} -lt 4 ]]; then
    echo "错误: 需要bash 4.0或更高版本，当前版本: $BASH_VERSION"
    exit 1
fi

#=============================================================================
# Skyeye ERP Docker环境离线安装脚本
# 版本: 1.0
# 用途: 菜单化安装Skyeye ERP所需的Docker基础环境
# 目标系统: CentOS 7
# MySQL版本: 8.0 (用户要求)
# 
# 作者: 根据skyeye-基础组件安装手书.md制作
# 支持完全离线安装模式
#=============================================================================

# 移除 set -e 以避免状态检查时脚本中断
# set -e

# 调试模式支持
# 使用方法: DEBUG=1 bash install.sh 或 export DEBUG=1
if [[ -n "$DEBUG" ]]; then
    echo "调试模式已启用"
    set -x  # 显示执行的命令
fi

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 全局变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PACKAGES_DIR="$SCRIPT_DIR/packages"

# 加载配置文件
if [[ -f "$SCRIPT_DIR/config.sh" ]]; then
    source "$SCRIPT_DIR/config.sh"
else
    echo "配置文件不存在: $SCRIPT_DIR/config.sh"
    exit 1
fi

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_title() {
    echo -e "${BLUE}[TITLE]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# 显示Banner
show_banner() {
    clear
    echo -e "${CYAN}"
    cat << 'EOF'
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║    ███████╗██╗  ██╗██╗   ██╗███████╗██╗   ██╗███████╗       ║
║    ██╔════╝██║ ██╔╝╚██╗ ██╔╝██╔════╝╚██╗ ██╔╝██╔════╝       ║
║    ███████╗█████╔╝  ╚████╔╝ █████╗   ╚████╔╝ █████╗         ║
║    ╚════██║██╔═██╗   ╚██╔╝  ██╔══╝    ╚██╔╝  ██╔══╝         ║
║    ███████║██║  ██╗   ██║   ███████╗   ██║   ███████╗       ║
║    ╚══════╝╚═╝  ╚═╝   ╚═╝   ╚══════╝   ╚═╝   ╚══════╝       ║
║                                                              ║
║             ERP Docker环境离线安装工具 v1.0                   ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
EOF
    echo -e "${NC}"
    echo -e "🎯 ${BLUE}目标系统${NC}: CentOS 7"
    echo -e "🐳 ${BLUE}Docker版本${NC}: $DOCKER_VERSION"
    echo -e "🗄️ ${BLUE}MySQL版本${NC}: $MYSQL_VERSION"
    echo -e "📦 ${BLUE}部署模式${NC}: 完全离线安装"
    echo -e "🔧 ${BLUE}安装目录${NC}: $INSTALL_DIR"
    echo ""
}

# 检查系统环境
check_system() {
    log_title "系统环境检查"
    
    # 检查操作系统
    if [[ ! -f /etc/centos-release ]]; then
        log_warn "警告: 未检测到CentOS系统"
        if [[ -f /etc/os-release ]]; then
            . /etc/os-release
            log_info "检测到系统: $PRETTY_NAME"
        fi
        echo -n "是否继续安装? (y/N): "
        read -r continue_install
        if [[ ! "$continue_install" =~ ^[Yy]$ ]]; then
            log_info "安装取消"
            exit 0
        fi
    else
        local centos_version
        centos_version=$(cat /etc/centos-release)
        log_info "检测到系统: $centos_version"
    fi
    
    # 检查root权限
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        log_info "请使用: sudo $0"
        exit 1
    fi
    
    # 检查磁盘空间
    local available_space
    available_space=$(df -BG / | awk 'NR==2 {print $4}' | sed 's/G//')
    if [[ "$available_space" -lt 10 ]]; then
        log_error "磁盘空间不足，需要至少10GB可用空间"
        log_info "当前可用空间: ${available_space}GB"
        exit 1
    fi
    
    log_success "系统环境检查通过"
}

# 检查安装包
check_packages() {
    log_title "检查离线安装包"
    
    if [[ ! -d "$PACKAGES_DIR" ]]; then
        log_error "安装包目录不存在: $PACKAGES_DIR"
        log_info "请确保packages目录与install.sh在同一目录下"
        log_warn "将以有限功能模式运行"
        return 1
    fi
    
    log_info "检查各组件离线安装包..."
    
    local components=("docker" "docker-compose" "mysql" "redis" "nacos" "rocketmq" "onlyoffice" "tomcat")
    local total_errors=0
    local available_components=()
    
    for component in "${components[@]}"; do
        local script_path="$PACKAGES_DIR/$component/scripts/${component}-install.sh"
        if [[ -f "$script_path" ]]; then
            echo ""
            log_step "检查 $component 安装包..."
            
            # 确保脚本有执行权限
            if [[ ! -x "$script_path" ]]; then
                log_step "修复 $component 脚本执行权限..."
                chmod +x "$script_path" 2>/dev/null || log_warn "权限修复失败"
            fi
            
            if bash "$script_path" --check-packages 2>/dev/null; then
                available_components+=("$component")
                log_info "✅ $component 安装包检查通过"
            else
                log_warn "⚠️  $component 安装包不完整"
                total_errors=$((total_errors + 1))
            fi
        else
            log_warn "⚠️  $component 安装脚本不存在，跳过检查"
        fi
    done
    
    echo ""
    log_title "安装包检查结果"
    echo "可用组件: ${#available_components[@]}/${#components[@]}"
    
    if [[ ${#available_components[@]} -gt 0 ]]; then
        echo "✅ 可安装的组件: ${available_components[*]}"
    fi
    
    if [[ $total_errors -gt 0 ]]; then
        log_warn "部分组件安装包不完整，相关功能可能不可用"
        log_info "你可以在菜单中单独安装可用的组件"
        return 0  # 改为返回0，继续显示菜单
    else
        log_success "所有安装包检查通过"
        return 0
    fi
}

# 加载通用函数库
source "$SCRIPT_DIR/utils/common.sh" 2>/dev/null || {
    # 如果common.sh不存在，创建基本的Docker检查函数
    check_docker_available() {
        if ! command -v docker &> /dev/null; then
            return 1  # Docker未安装
        fi
        if ! systemctl is-active docker &> /dev/null; then
            return 2  # Docker已安装但未启动
        fi
        return 0  # Docker正常运行
    }
}

# 检查组件状态（调用各组件脚本或使用内置逻辑）
get_component_status() {
    local component="$1"
    local script_path="$PACKAGES_DIR/$component/scripts/${component}-install.sh"
    
    # 对于容器化组件，先检查Docker依赖
    local component_type
    case "$component" in
        "mysql"|"redis"|"nacos"|"rocketmq"|"onlyoffice"|"tomcat")
            component_type="container"
            ;;
        "docker")
            component_type="infrastructure"
            ;;
        *)
            component_type="unknown"
            ;;
    esac
    
    # 容器化组件必须先检查Docker状态
    if [[ "$component_type" == "container" ]]; then
        if [[ -n "$DEBUG" ]]; then
            echo "DEBUG: 检查容器组件 $component 的Docker依赖" >&2
        fi
        
        check_docker_available
        local docker_status=$?
        
        case $docker_status in
            1)
                if [[ -n "$DEBUG" ]]; then
                    echo "DEBUG: Docker未安装，容器组件 $component 返回未安装状态" >&2
                fi
                echo "未安装"
                return 0
                ;;
            2)
                if [[ -n "$DEBUG" ]]; then
                    echo "DEBUG: Docker未启动，容器组件 $component 返回未安装状态" >&2
                fi
                echo "未安装"
                return 0
                ;;
            0)
                # Docker正常，继续检查组件状态
                if [[ -n "$DEBUG" ]]; then
                    echo "DEBUG: Docker正常运行，继续检查容器组件 $component" >&2
                fi
                ;;
        esac
    fi
    
    # 检查脚本是否存在
    if [[ ! -f "$script_path" ]]; then
        if [[ -n "$DEBUG" ]]; then
            echo "DEBUG: 组件 $component 的安装脚本不存在: $script_path" >&2
        fi
        echo "待实现"
        return 0
    fi
    
    # 确保脚本有执行权限
    if [[ ! -x "$script_path" ]]; then
        if ! chmod +x "$script_path" 2>/dev/null; then
            if [[ -n "$DEBUG" ]]; then
                echo "DEBUG: 无法给组件 $component 的脚本添加执行权限" >&2
            fi
            echo "待实现"
            return 0
        fi
    fi
    
    # 调用组件脚本的状态检查功能
    local status_result
    local error_output
    
    # ShellCheck SC2155解决方案：移除set -e后，直接执行并捕获退出码
    if [[ -n "$DEBUG" ]]; then
        echo "DEBUG: 调用组件 $component 的状态检查脚本" >&2
    fi

    # 执行状态检查脚本并捕获退出码
    local exit_code
    if [[ -n "$DEBUG" ]]; then
        # 调试模式：显示输出并捕获退出码
        bash "$script_path" --check-installed >&2
        exit_code=$?
        echo "DEBUG: $component script exit code: $exit_code" >&2
    else
        # 正常模式：静默执行
        bash "$script_path" --check-installed >/dev/null 2>&1
        exit_code=$?
    fi

    # 根据退出码判断状态
    case $exit_code in
        0) echo "已启动" ;;
        1) echo "未安装" ;;
        2) echo "待启动" ;;
        *) echo "异常" ;;
    esac
}

# 检查系统管理功能状态
get_system_function_status() {
    local function_name="$1"
    
    case "$function_name" in
        "service_status")
            # 检查是否实现了完整的服务状态检查功能
            if declare -f show_service_status &>/dev/null && \
               declare -f check_all_services_status &>/dev/null; then
                echo "可用"
            else
                echo "待实现"
            fi
            ;;
        "health_check")
            # 检查是否实现了完整的健康检查功能
            if declare -f perform_health_check &>/dev/null && \
               declare -f check_docker_health &>/dev/null; then
                echo "可用"
            else
                echo "待实现"
            fi
            ;;
        "service_info")
            # 检查是否实现了完整的服务信息显示功能
            if declare -f show_service_info &>/dev/null && \
               declare -f collect_service_info &>/dev/null; then
                echo "可用"
            else
                echo "待实现"
            fi
            ;;
        "docker_compose")
            # 检查docker-compose脚本是否存在且可执行
            local compose_script="$PACKAGES_DIR/docker-compose/scripts/docker-compose-install.sh"
            if [[ -f "$compose_script" ]] && [[ -x "$compose_script" ]]; then
                echo "可用"
            else
                echo "待实现"
            fi
            ;;
        *)
            echo "待实现"
            ;;
    esac
}

# 检查批量操作功能状态
get_batch_operation_status() {
    local operation_type="$1"
    local components=("docker" "mysql" "redis" "nacos" "rocketmq" "onlyoffice" "tomcat")
    
    case "$operation_type" in
        "batch_install")
            # 检查是否有完整的批量安装实现
            local implemented_count=0
            for comp in "${components[@]}"; do
                local script_path="$PACKAGES_DIR/$comp/scripts/${comp}-install.sh"
                if [[ -f "$script_path" ]] && [[ -x "$script_path" ]]; then
                    implemented_count=$((implemented_count + 1))
                fi
            done
            
            # 还需要检查批量安装函数是否实现
            if [[ $implemented_count -eq ${#components[@]} ]] && \
               declare -f batch_install_all_components &>/dev/null; then
                echo "可用"
            else
                echo "待实现"
            fi
            ;;
        "batch_uninstall")
            # 检查是否有完整的批量卸载实现
            local implemented_count=0
            for comp in "${components[@]}"; do
                local script_path="$PACKAGES_DIR/$comp/scripts/${comp}-install.sh"
                if [[ -f "$script_path" ]] && [[ -x "$script_path" ]]; then
                    implemented_count=$((implemented_count + 1))
                fi
            done
            
            # 还需要检查批量卸载函数是否实现
            if [[ $implemented_count -eq ${#components[@]} ]] && \
               declare -f batch_uninstall_all_components &>/dev/null; then
                echo "可用"
            else
                echo "待实现"
            fi
            ;;
        *)
            echo "待实现"
            ;;
    esac
}

# 检查高级选项功能状态
get_advanced_options_status() {
    # 检查高级选项菜单和相关功能是否完整实现
    if declare -f show_advanced_menu &>/dev/null && \
       declare -f handle_advanced_choice &>/dev/null; then
        echo "可用"
    else
        echo "待实现"
    fi
}

# 显示主菜单（重构为状态+组件的两级菜单）
show_main_menu() {
    echo "获取组件状态中..."

    # 动态检查各组件状态（调用各组件脚本）
    # 注意：必须分别声明和赋值，避免local命令掩盖函数的返回值
    local docker_status
    local docker_compose_status
    local mysql_status
    local redis_status
    local nacos_status
    local rocketmq_status
    local onlyoffice_status
    local tomcat_status

    docker_status=$(get_component_status "docker")
    docker_compose_status=$(get_component_status "docker-compose")
    mysql_status=$(get_component_status "mysql")
    redis_status=$(get_component_status "redis")
    nacos_status=$(get_component_status "nacos")
    rocketmq_status=$(get_component_status "rocketmq")
    onlyoffice_status=$(get_component_status "onlyoffice")
    tomcat_status=$(get_component_status "tomcat")

    echo -e "${CYAN}╔═══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${CYAN}║                        主菜单                                  ║${NC}"
    echo -e "${CYAN}╠═══════════════════════════════════════════════════════════════╣${NC}"
    echo -e "${CYAN}║                      📦 组件管理                               ║${NC}"
    echo -e "${CYAN}║${NC}                                                         ${CYAN}║${NC}"
    printf "${CYAN}║${NC}  ${GREEN}1${NC}) 🐳 [${docker_status}] Docker环境%-28s${CYAN}║${NC}\n" ""
    printf "${CYAN}║${NC}  ${GREEN}2${NC}) 🔗 [${docker_compose_status}] Docker Compose%-23s${CYAN}║${NC}\n" ""
    printf "${CYAN}║${NC}  ${GREEN}3${NC}) 🗄️  [${mysql_status}] MySQL 8.0%-30s${CYAN}║${NC}\n" ""
    printf "${CYAN}║${NC}  ${GREEN}4${NC}) 🔧 [${redis_status}] Redis 7.4%-30s${CYAN}║${NC}\n" ""
    printf "${CYAN}║${NC}  ${GREEN}5${NC}) 📋 [${nacos_status}] Nacos 2.5.0%-29s${CYAN}║${NC}\n" ""
    printf "${CYAN}║${NC}  ${GREEN}6${NC}) 🚀 [${rocketmq_status}] RocketMQ 5.3.3%-26s${CYAN}║${NC}\n" ""
    printf "${CYAN}║${NC}  ${GREEN}7${NC}) 📄 [${onlyoffice_status}] OnlyOffice DocumentServer%-13s${CYAN}║${NC}\n" ""
    printf "${CYAN}║${NC}  ${GREEN}8${NC}) 🌐 [${tomcat_status}] Tomcat 11.0.9%-27s${CYAN}║${NC}\n" ""
    echo -e "${CYAN}║${NC}                                                         ${CYAN}║${NC}"
    echo -e "${CYAN}║${NC}                      📊 系统管理                               ${CYAN}║${NC}"
    
    # 动态检查系统管理功能状态
    local service_status_status
    local health_check_status
    local service_info_status
    local docker_compose_status
    service_status_status=$(get_system_function_status "service_status")
    health_check_status=$(get_system_function_status "health_check")
    service_info_status=$(get_system_function_status "service_info")
    docker_compose_status=$(get_system_function_status "docker_compose")
    
    printf "${CYAN}║${NC}  ${YELLOW}21${NC}) 🔍 [${service_status_status}]服务状态检查%-24s${CYAN}║${NC}\n" ""
    printf "${CYAN}║${NC}  ${YELLOW}22${NC}) 🏥 [${health_check_status}]健康检查%-27s${CYAN}║${NC}\n" ""
    printf "${CYAN}║${NC}  ${YELLOW}23${NC}) 📊 [${service_info_status}]显示服务信息%-25s${CYAN}║${NC}\n" ""
    printf "${CYAN}║${NC}  ${YELLOW}24${NC}) 📦 [${docker_compose_status}]统一部署所有服务 (docker-compose)%-7s${CYAN}║${NC}\n" ""
    echo -e "${CYAN}║${NC}                                                         ${CYAN}║${NC}"
    echo -e "${CYAN}║${NC}                      🚀 批量操作                               ${CYAN}║${NC}"
    
    # 动态检查批量操作功能状态
    local batch_install_status
    local batch_uninstall_status
    batch_install_status=$(get_batch_operation_status "batch_install")
    batch_uninstall_status=$(get_batch_operation_status "batch_uninstall")
    
    printf "${CYAN}║${NC}  ${GREEN}98${NC}) 🚀 [${batch_install_status}]一键安装所有组件 (含Docker + 进度展示)%-7s${CYAN}║${NC}\n" ""
    printf "${CYAN}║${NC}  ${RED}99${NC}) 🗑️  [${batch_uninstall_status}]一键卸载所有组件 (含Docker + 状态检查)%-6s${CYAN}║${NC}\n" ""
    echo -e "${CYAN}║${NC}                                                         ${CYAN}║${NC}"
    echo -e "${CYAN}║${NC}                      ⚙️  其他选项                               ${CYAN}║${NC}"
    
    # 动态检查其他选项功能状态
    local advanced_options_status
    advanced_options_status=$(get_advanced_options_status)
    
    printf "${CYAN}║${NC}  ${PURPLE}30${NC}) ⚙️  [${advanced_options_status}]高级选项%-30s${CYAN}║${NC}\n" ""
    printf "${CYAN}║${NC}  ${YELLOW}0${NC})  🚪 退出%-45s${CYAN}║${NC}\n" ""
    echo -e "${CYAN}╚═══════════════════════════════════════════════════════════════╝${NC}"
    echo ""
}

# 显示组件操作子菜单
show_component_menu() {
    local component="$1"
    local component_desc="$2"
    local component_icon="$3"
    
    # 获取组件当前状态
    local status
    status=$(get_component_status "$component")
    
    echo ""
    echo -e "${PURPLE}╔═══════════════════════════════════════════════════════════════╗${NC}"
    printf "${PURPLE}║${NC}                    ${component_icon} ${component_desc} 管理                      ${PURPLE}║${NC}\n"
    echo -e "${PURPLE}╠═══════════════════════════════════════════════════════════════╣${NC}"
    printf "${PURPLE}║${NC}  当前状态: [${status}]%-40s${PURPLE}║${NC}\n" ""
    echo -e "${PURPLE}║${NC}                                                         ${PURPLE}║${NC}"
    echo -e "${PURPLE}║${NC}                      🔧 基本操作                               ${PURPLE}║${NC}"
    echo -e "${PURPLE}║${NC}  ${GREEN}1${NC}) 🚀 部署/安装                                       ${PURPLE}║${NC}"
    echo -e "${PURPLE}║${NC}  ${BLUE}2${NC}) ▶️  启动服务                                       ${PURPLE}║${NC}"
    echo -e "${PURPLE}║${NC}  ${YELLOW}3${NC}) ⏸️  停止服务                                       ${PURPLE}║${NC}"
    echo -e "${PURPLE}║${NC}  ${RED}4${NC}) 🗑️  卸载组件                                       ${PURPLE}║${NC}"
    echo -e "${PURPLE}║${NC}                                                         ${PURPLE}║${NC}"
    echo -e "${PURPLE}║${NC}                      ⚙️  高级操作                               ${PURPLE}║${NC}"
    echo -e "${PURPLE}║${NC}  ${CYAN}5${NC}) 🔄 更新配置                                       ${PURPLE}║${NC}"
    echo -e "${PURPLE}║${NC}  ${CYAN}6${NC}) 🏥 健康检查                                       ${PURPLE}║${NC}"
    echo -e "${PURPLE}║${NC}  ${CYAN}7${NC}) 📋 查看日志                                       ${PURPLE}║${NC}"
    echo -e "${PURPLE}║${NC}  ${CYAN}8${NC}) 🔧 诊断问题                                       ${PURPLE}║${NC}"
    echo -e "${PURPLE}║${NC}  ${CYAN}9${NC}) 📊 服务信息                                       ${PURPLE}║${NC}"
    echo -e "${PURPLE}║${NC}                                                         ${PURPLE}║${NC}"
    echo -e "${PURPLE}║${NC}  ${YELLOW}0${NC}) 🔙 返回主菜单                                   ${PURPLE}║${NC}"
    echo -e "${PURPLE}╚═══════════════════════════════════════════════════════════════╝${NC}"
    echo ""
}

# 显示高级选项菜单
show_advanced_menu() {
    echo ""
    echo -e "${PURPLE}╔═══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${PURPLE}║                       高级选项                                ║${NC}"
    echo -e "${PURPLE}╠═══════════════════════════════════════════════════════════════╣${NC}"
    echo -e "${PURPLE}║${NC}  ${GREEN}1${NC}) 🏗️  创建基础目录和网络                             ${PURPLE}║${NC}"
    echo -e "${PURPLE}║${NC}  ${GREEN}2${NC}) 📂 加载所有Docker镜像                             ${PURPLE}║${NC}"
    echo -e "${PURPLE}║${NC}  ${GREEN}3${NC}) 🔧 配置Docker镜像加速器                           ${PURPLE}║${NC}"
    echo -e "${PURPLE}║${NC}  ${GREEN}4${NC}) 🛡️  配置防火墙端口                                ${PURPLE}║${NC}"
    echo -e "${PURPLE}║${NC}  ${GREEN}5${NC}) 💾 数据备份                                       ${PURPLE}║${NC}"
    echo -e "${PURPLE}║${NC}  ${GREEN}6${NC}) 🗑️  环境清理                                      ${PURPLE}║${NC}"
    echo -e "${PURPLE}║${NC}  ${GREEN}7${NC}) 📋 查看安装日志                                   ${PURPLE}║${NC}"
    echo -e "${PURPLE}║${NC}  ${RED}0${NC}) 🔙 返回主菜单                                     ${PURPLE}║${NC}"
    echo -e "${PURPLE}╚═══════════════════════════════════════════════════════════════╝${NC}"
    echo ""
}

# 执行组件安装脚本
execute_component_install() {
    local component="$1"
    local description="$2"
    
    log_title "执行: $description"
    
    local script_path="$PACKAGES_DIR/$component/scripts/${component}-install.sh"
    if [[ -f "$script_path" ]]; then
        log_info "执行安装脚本: $component/scripts/${component}-install.sh"
        if bash "$script_path"; then
            log_success "$description 完成"
            return 0
        else
            log_error "$description 失败"
            return 1
        fi
    else
        log_error "安装脚本不存在: $script_path"
        log_info "该功能正在开发中..."
        return 1
    fi
}

# 执行组件卸载脚本
execute_component_uninstall() {
    local component="$1"
    local description="$2"
    
    log_title "执行: $description"
    
    local script_path="$PACKAGES_DIR/$component/scripts/${component}-install.sh"
    if [[ -f "$script_path" ]]; then
        log_info "执行卸载脚本: $component/scripts/${component}-install.sh --uninstall"
        if bash "$script_path" --uninstall; then
            log_success "$description 完成"
            return 0
        else
            log_error "$description 失败"
            return 1
        fi
    else
        log_error "卸载脚本不存在: $script_path"
        log_info "该功能正在开发中..."
        return 1
    fi
}

# 执行组件启动操作
execute_component_start() {
    local component="$1"
    local description="$2"
    
    local script_path="$PACKAGES_DIR/$component/scripts/${component}-install.sh"
    
    if [[ ! -f "$script_path" ]] || [[ ! -x "$script_path" ]]; then
        log_error "组件脚本不存在或无执行权限: $script_path"
        return 1
    fi
    
    log_title "启动: $description"
    if bash "$script_path" --start; then
        log_success "$description 启动成功"
        return 0
    else
        log_error "$description 启动失败"
        return 1
    fi
}

# 执行组件停止操作  
execute_component_stop() {
    local component="$1"
    local description="$2"
    
    local script_path="$PACKAGES_DIR/$component/scripts/${component}-install.sh"
    
    if [[ ! -f "$script_path" ]] || [[ ! -x "$script_path" ]]; then
        log_error "组件脚本不存在或无执行权限: $script_path"
        return 1
    fi
    
    log_title "停止: $description" 
    if bash "$script_path" --stop; then
        log_success "$description 停止成功"
        return 0
    else
        log_error "$description 停止失败"
        return 1
    fi
}

# 执行组件更新配置操作
execute_component_update_config() {
    local component="$1"
    local description="$2"
    
    local script_path="$PACKAGES_DIR/$component/scripts/${component}-install.sh"
    
    if [[ ! -f "$script_path" ]] || [[ ! -x "$script_path" ]]; then
        log_error "组件脚本不存在或无执行权限: $script_path"
        log_info "该功能正在开发中..."
        return 1
    fi
    
    log_title "更新配置: $description"
    # 检查脚本是否支持 --update-config 参数
    if bash "$script_path" --help 2>/dev/null | grep -q "update-config\|config"; then
        if bash "$script_path" --update-config; then
            log_success "$description 配置更新成功"
            return 0
        else
            log_error "$description 配置更新失败"
            return 1
        fi
    else
        log_warn "该组件暂不支持配置更新功能"
        log_info "您可以通过重新安装组件来更新配置"
        return 1
    fi
}

# 执行组件健康检查操作
execute_component_health_check() {
    local component="$1" 
    local description="$2"
    
    local script_path="$PACKAGES_DIR/$component/scripts/${component}-install.sh"
    
    if [[ ! -f "$script_path" ]] || [[ ! -x "$script_path" ]]; then
        log_error "组件脚本不存在或无执行权限: $script_path"
        return 1
    fi
    
    log_title "健康检查: $description"
    
    # 首先检查安装状态
    local status_result
    status_result=$(bash "$script_path" --check-installed 2>/dev/null)
    local exit_code=$?
    
    case $exit_code in
        0)
            log_info "✅ $description 运行状态: 正常"
            
            # 如果脚本支持诊断功能，执行诊断
            if bash "$script_path" --help 2>/dev/null | grep -q "diagnose"; then
                echo ""
                log_step "执行详细诊断..."
                if bash "$script_path" --diagnose; then
                    log_success "✅ $description 健康检查完成"
                else
                    log_warn "⚠️  $description 诊断发现一些问题"
                fi
            else
                log_success "✅ $description 基础健康检查通过"
            fi
            ;;
        2)
            log_warn "⚠️  $description 已安装但未启动"
            log_info "建议先启动服务再进行健康检查"
            ;;
        1)
            log_error "❌ $description 未安装"
            log_info "请先安装该组件"
            ;;
        *)
            log_error "❌ $description 状态异常"
            ;;
    esac
}

# 执行组件查看日志操作
execute_component_view_logs() {
    local component="$1"
    local description="$2"
    
    log_title "查看日志: $description"
    
    # 根据组件类型查看不同的日志
    case $component in
        "mysql")
            if command -v docker &> /dev/null && docker ps --format "table {{.Names}}" | grep -q "^mysql$"; then
                log_info "显示MySQL容器日志 (最近50行):"
                docker logs --tail 50 mysql
            else
                log_warn "MySQL容器未运行"
            fi
            ;;
        "redis")
            if command -v docker &> /dev/null && docker ps --format "table {{.Names}}" | grep -q "^redis$"; then
                log_info "显示Redis容器日志 (最近50行):"
                docker logs --tail 50 redis
            else
                log_warn "Redis容器未运行"
            fi
            ;;
        "nacos")
            if command -v docker &> /dev/null && docker ps --format "table {{.Names}}" | grep -q "^nacos$"; then
                log_info "显示Nacos容器日志 (最近50行):"
                docker logs --tail 50 nacos
            else
                log_warn "Nacos容器未运行"
            fi
            ;;
        "docker")
            log_info "显示Docker系统日志 (最近20行):"
            if command -v journalctl &> /dev/null; then
                journalctl -u docker --no-pager -n 20
            else
                log_warn "无法获取Docker系统日志"
            fi
            ;;
        *)
            # 尝试查看对应容器的日志
            if command -v docker &> /dev/null && docker ps --format "table {{.Names}}" | grep -q "^$component$"; then
                log_info "显示${description}容器日志 (最近50行):"
                docker logs --tail 50 "$component"
            else
                log_warn "未找到${description}的日志信息"
                log_info "该组件可能未运行或日志查看功能尚未实现"
            fi
            ;;
    esac
}

# 执行组件诊断操作
execute_component_diagnose() {
    local component="$1"
    local description="$2"
    
    local script_path="$PACKAGES_DIR/$component/scripts/${component}-install.sh"
    
    if [[ ! -f "$script_path" ]] || [[ ! -x "$script_path" ]]; then
        log_error "组件脚本不存在或无执行权限: $script_path"
        return 1
    fi
    
    log_title "诊断问题: $description"
    
    if bash "$script_path" --diagnose; then
        log_success "$description 诊断完成"
        return 0
    else
        log_error "$description 诊断失败"
        return 1
    fi
}

# 执行组件显示信息操作
execute_component_show_info() {
    local component="$1"
    local description="$2"
    
    log_title "服务信息: $description"
    
    # 获取组件状态
    local status
    status=$(get_component_status "$component")
    
    echo ""
    echo "📊 $description 详细信息："
    echo "----------------------------------------"
    echo "状态: $status"
    
    # 根据组件类型显示特定信息
    case $component in
        "mysql")
            if [[ "$status" == "已启动" ]]; then
                local server_ip
                server_ip=$(hostname -I | awk '{print $1}')
                echo "访问地址: mysql://$server_ip:3306"
                echo "用户名: root"
                echo "密码: $MYSQL_PASSWORD"
                echo "数据目录: $INSTALL_DIR/mysql/data"
                echo "配置目录: $INSTALL_DIR/mysql/conf"
                
                # 显示容器信息
                if command -v docker &> /dev/null; then
                    echo ""
                    echo "容器信息:"
                    docker ps --filter "name=mysql" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
                fi
            fi
            ;;
        "redis")  
            if [[ "$status" == "已启动" ]]; then
                local server_ip
                server_ip=$(hostname -I | awk '{print $1}')
                echo "访问地址: redis://$server_ip:6379"
                echo "密码: $REDIS_PASSWORD"
                echo "数据目录: $INSTALL_DIR/redis/data"
                echo "配置目录: $INSTALL_DIR/redis/conf"
                
                if command -v docker &> /dev/null; then
                    echo ""
                    echo "容器信息:"
                    docker ps --filter "name=redis" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
                fi
            fi
            ;;
        "nacos")
            if [[ "$status" == "已启动" ]]; then
                local server_ip
                server_ip=$(hostname -I | awk '{print $1}')
                echo "访问地址: http://$server_ip:8848/nacos/"
                echo "用户名: nacos"
                echo "密码: nacos"
                echo "数据目录: $INSTALL_DIR/nacos/data"
                echo "配置目录: $INSTALL_DIR/nacos/conf"
                
                if command -v docker &> /dev/null; then
                    echo ""
                    echo "容器信息:"
                    docker ps --filter "name=nacos" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
                fi
            fi
            ;;
        "docker")
            if command -v docker &> /dev/null; then
                echo "Docker版本: $(docker --version)"
                echo "Docker状态: $(systemctl is-active docker 2>/dev/null || echo '未知')"
                echo ""
                echo "容器统计:"
                echo "  运行中: $(docker ps -q | wc -l)"
                echo "  总数量: $(docker ps -a -q | wc -l)"
                echo ""
                echo "镜像统计:"
                echo "  总数量: $(docker images -q | wc -l)"
                echo ""
                echo "网络列表:"
                docker network ls --format "table {{.Name}}\t{{.Driver}}\t{{.Scope}}"
            else
                echo "Docker未安装"
            fi
            ;;
        *)
            echo "组件类型: $component"
            if command -v docker &> /dev/null && docker ps --format "table {{.Names}}" | grep -q "^$component$"; then
                echo ""
                echo "容器信息:"
                docker ps --filter "name=$component" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
            fi
            ;;
    esac
    
    echo ""
}

# 保留原有的启动/停止组合操作函数以兼容批量操作
execute_component_start_stop() {
    local component="$1"
    local description="$2"
    
    local script_path="$PACKAGES_DIR/$component/scripts/${component}-install.sh"
    
    if [[ ! -f "$script_path" ]] || [[ ! -x "$script_path" ]]; then
        log_error "组件脚本不存在或无执行权限: $script_path"
        return 1
    fi
    
    # 检查组件状态
    local status_result
    status_result=$(bash "$script_path" --check-installed 2>/dev/null)
    local exit_code=$?
    
    case $exit_code in
        0)
            # 已启动，询问是否停止
            echo ""
            echo "🟢 $description 当前状态: 已启动"
            echo -n "是否停止 $description? (y/N): "
            read -r confirm_stop
            if [[ "$confirm_stop" =~ ^[Yy]$ ]]; then
                execute_component_stop "$component" "$description"
            else
                log_info "操作取消"
            fi
            ;;
        2)
            # 待启动，询问是否启动
            echo ""
            echo "🟡 $description 当前状态: 待启动"
            echo -n "是否启动 $description? (y/N): "
            read -r confirm_start
            if [[ "$confirm_start" =~ ^[Yy]$ ]]; then
                execute_component_start "$component" "$description"
            else
                log_info "操作取消"
            fi
            ;;
        1)
            # 未安装
            echo ""
            echo "🔴 $description 当前状态: 未安装"
            log_warn "请先安装 $description"
            ;;
        *)
            echo ""
            echo "❓ $description 当前状态: 未知"
            log_error "无法确定组件状态"
            ;;
    esac
}

# 处理组件操作子菜单选择
handle_component_choice() {
    local component="$1"
    local component_desc="$2"
    local choice="$3"
    
    case $choice in
        1)
            # 部署/安装
            execute_component_install "$component" "${component_desc}安装"
            ;;
        2)
            # 启动服务
            execute_component_start "$component" "$component_desc"
            ;;
        3)
            # 停止服务
            execute_component_stop "$component" "$component_desc"
            ;;
        4)
            # 卸载组件
            execute_component_uninstall "$component" "${component_desc}卸载"
            ;;
        5)
            # 更新配置
            execute_component_update_config "$component" "$component_desc"
            ;;
        6)
            # 健康检查
            execute_component_health_check "$component" "$component_desc"
            ;;
        7)
            # 查看日志
            execute_component_view_logs "$component" "$component_desc"
            ;;
        8)
            # 诊断问题
            execute_component_diagnose "$component" "$component_desc"
            ;;
        9)
            # 服务信息
            execute_component_show_info "$component" "$component_desc"
            ;;
        0)
            # 返回主菜单
            return 0
            ;;
        *)
            log_error "无效选择: $choice"
            ;;
    esac
}

# 处理主菜单选择
handle_menu_choice() {
    local choice="$1"
    
    case $choice in
        # 组件管理 (1-8) - 进入组件子菜单
        1)
            handle_component_menu "docker" "Docker环境" "🐳"
            ;;
        2)
            handle_component_menu "docker-compose" "Docker Compose" "🔗"
            ;;
        3)
            handle_component_menu "mysql" "MySQL 8.0" "🗄️"
            ;;
        4)
            handle_component_menu "redis" "Redis 7.4" "🔧"
            ;;
        5)
            handle_component_menu "nacos" "Nacos 2.5.0" "📋"
            ;;
        6)
            handle_component_menu "rocketmq" "RocketMQ 5.3.3" "🚀"
            ;;
        7)
            handle_component_menu "onlyoffice" "OnlyOffice DocumentServer" "📄"
            ;;
        8)
            handle_component_menu "tomcat" "Tomcat 11.0.9" "🌐"
            ;;
        
        # 系统管理 (21-24)
        21)
            # 服务状态检查
            log_title "服务状态检查"
            show_service_status
            ;;
        22)
            # 健康检查
            log_title "健康检查"
            perform_health_check
            ;;
        23)
            # 显示服务信息
            show_service_info
            ;;
        24)
            # 统一部署所有服务
            execute_component_install "docker-compose" "统一部署所有服务"
            ;;
        
        # 其他选项 (30)
        30)
            show_advanced_menu
            echo -n "请选择操作 [0-7]: "
            read -r advanced_choice
            handle_advanced_choice "$advanced_choice"
            ;;
        
        # 批量操作 (98, 99)
        98)
            # 一键安装所有组件
            batch_install_all_components
            ;;
        99)
            # 一键卸载所有组件
            batch_uninstall_all_components
            ;;
        
        # 退出 (0)
        0)
            log_info "感谢使用Skyeye ERP Docker环境安装工具！"
            exit 0
            ;;
        *)
            log_error "无效选择: $choice"
            ;;
    esac
}

# 处理组件菜单循环
handle_component_menu() {
    local component="$1"
    local component_desc="$2" 
    local component_icon="$3"
    
    while true; do
        show_component_menu "$component" "$component_desc" "$component_icon"
        echo -n "请选择操作 [0-9]: "
        read -r component_choice
        
        echo ""
        handle_component_choice "$component" "$component_desc" "$component_choice"
        
        # 如果选择返回主菜单，则退出组件菜单循环
        if [[ "$component_choice" == "0" ]]; then
            break
        fi
        
        echo ""
        echo -n "按回车键继续..."
        read -r
    done
}

# 处理高级选项选择
handle_advanced_choice() {
    local choice="$1"
    
    case $choice in
        1)
            create_base_structure
            ;;
        2)
            load_all_images
            ;;
        3)
            configure_docker_mirror
            ;;
        4)
            configure_firewall
            ;;
        5)
            execute_module "backup.sh" "数据备份"
            ;;
        6)
            execute_module "cleanup.sh" "环境清理"
            ;;
        7)
            show_install_logs
            ;;
        0)
            return 0
            ;;
        *)
            log_error "无效选择: $choice"
            ;;
    esac
}

# 创建基础目录结构
create_base_structure() {
    log_title "创建基础目录结构"
    
    log_step "创建安装目录: $INSTALL_DIR"
    mkdir -p "$INSTALL_DIR"
    cd "$INSTALL_DIR"
    
    log_step "创建各服务目录"
    mkdir -p {mysql,redis,nacos,rocketmq,onlyoffice,tomcat}/{conf,data,logs}
    
    log_step "创建OnlyOffice特殊目录"
    mkdir -p /opt/onlyoffice/config
    
    log_step "设置目录权限"
    chmod -R 755 "$INSTALL_DIR"
    chown -R root:root "$INSTALL_DIR"
    
    log_step "创建Docker网络: $DOCKER_NETWORK"
    if docker network ls | grep -q "$DOCKER_NETWORK"; then
        log_info "Docker网络已存在: $DOCKER_NETWORK"
    else
        if command -v docker &> /dev/null; then
            docker network create "$DOCKER_NETWORK"
            log_success "Docker网络创建成功: $DOCKER_NETWORK"
        else
            log_warn "Docker未安装，跳过网络创建"
        fi
    fi
    
    log_success "基础目录结构创建完成"
}

# 加载所有Docker镜像
load_all_images() {
    log_title "加载所有Docker镜像"
    
    if [[ -f "$PACKAGES_DIR/load-all-images.sh" ]]; then
        cd "$PACKAGES_DIR"
        ./load-all-images.sh
        cd "$SCRIPT_DIR"
    else
        log_error "镜像加载脚本不存在"
        return 1
    fi
}

# 配置Docker镜像加速器
configure_docker_mirror() {
    log_title "配置Docker镜像加速器"
    
    if [[ ! -f /etc/docker/daemon.json ]]; then
        mkdir -p /etc/docker
        cat > /etc/docker/daemon.json << 'EOF'
{
    "registry-mirrors": [
        "https://docker.1panel.live",
        "https://docker.m.daocloud.io",
        "https://docker.cloudlayer.icu"
    ],
    "log-driver": "json-file",
    "log-opts": {
        "max-size": "100m",
        "max-file": "3"
    },
    "storage-driver": "overlay2"
}
EOF
        log_info "Docker配置文件已创建"
    else
        log_info "Docker配置文件已存在"
    fi
    
    if systemctl is-active docker &> /dev/null; then
        log_info "重启Docker服务以应用配置"
        systemctl restart docker
        log_success "Docker镜像加速器配置完成"
    else
        log_info "Docker未运行，配置将在启动时生效"
    fi
}

# 配置防火墙
configure_firewall() {
    log_title "配置防火墙端口"
    
    if systemctl is-active firewalld &> /dev/null; then
        log_info "配置防火墙端口..."
        
        local ports=(
            "3306/tcp"   # MySQL
            "6379/tcp"   # Redis
            "8848/tcp"   # Nacos HTTP
            "9848/tcp"   # Nacos gRPC
            "9849/tcp"   # Nacos gRPC
            "9876/tcp"   # RocketMQ NameServer
            "10909/tcp"  # RocketMQ Broker
            "10911/tcp"  # RocketMQ Broker
            "8000/tcp"   # OnlyOffice
            "8080/tcp"   # Tomcat
        )
        
        for port in "${ports[@]}"; do
            if firewall-cmd --permanent --add-port="$port" &> /dev/null; then
                log_info "开放端口: $port"
            fi
        done
        
        firewall-cmd --reload
        log_success "防火墙配置完成"
    else
        log_info "防火墙未运行，跳过配置"
    fi
}

# 显示服务信息
show_service_info() {
    log_title "服务信息"
    
    echo ""
    echo -e "${CYAN}╔═══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${CYAN}║                      服务访问信息                              ║${NC}"
    echo -e "${CYAN}╠═══════════════════════════════════════════════════════════════╣${NC}"
    
    local server_ip
    server_ip=$(hostname -I | awk '{print $1}')
    
    echo -e "${CYAN}║${NC} 🗄️  MySQL 8.0:       mysql://$server_ip:3306             ${CYAN}║${NC}"
    echo -e "${CYAN}║${NC}     用户名: root   密码: $MYSQL_PASSWORD        ${CYAN}║${NC}"
    echo ""
    echo -e "${CYAN}║${NC} 🔧 Redis 3.2.0:      redis://$server_ip:6379             ${CYAN}║${NC}"
    echo -e "${CYAN}║${NC}     密码: $REDIS_PASSWORD                ${CYAN}║${NC}"
    echo ""
    echo -e "${CYAN}║${NC} 📋 Nacos 2.3.0:      http://$server_ip:8848/nacos/       ${CYAN}║${NC}"
    echo -e "${CYAN}║${NC}     用户名: nacos  密码: nacos                      ${CYAN}║${NC}"
    echo ""
    echo -e "${CYAN}║${NC} 🚀 RocketMQ:         NameServer: $server_ip:9876         ${CYAN}║${NC}"
    echo ""
    echo -e "${CYAN}║${NC} 📄 OnlyOffice:       http://$server_ip:8000/              ${CYAN}║${NC}"
    echo ""
    echo -e "${CYAN}║${NC} 🌐 Tomcat:           http://$server_ip:8080/              ${CYAN}║${NC}"
    echo -e "${CYAN}╚═══════════════════════════════════════════════════════════════╝${NC}"
    echo ""
}

# 显示服务状态
show_service_status() {
    log_title "服务状态检查"
    
    local services=(
        "docker:Docker服务"
        "mysql:MySQL容器"  
        "redis:Redis容器"
        "nacos:Nacos容器"
        "rmqnamesrv:RocketMQ NameServer"
        "rmqbroker:RocketMQ Broker"
        "onlyoffice:OnlyOffice容器"
        "tomcat:Tomcat容器"
    )
    
    echo ""
    printf "%-20s %-30s %-10s\n" "服务名称" "描述" "状态"
    echo "=================================================="
    
    for service_info in "${services[@]}"; do
        local service_name="${service_info%%:*}"
        local service_desc="${service_info##*:}"
        local status="❌ 未运行"
        
        case $service_name in
            "docker")
                if systemctl is-active docker &> /dev/null; then
                    status="✅ 运行中"
                fi
                ;;
            *)
                if command -v docker &> /dev/null && docker ps --format "table {{.Names}}" | grep -q "^$service_name$"; then
                    status="✅ 运行中"
                fi
                ;;
        esac
        
        printf "%-20s %-30s %-10s\n" "$service_name" "$service_desc" "$status"
    done
    
    echo ""
}

# 执行健康检查
perform_health_check() {
    log_title "健康检查"
    
    local checks_passed=0
    local total_checks=0
    
    # Docker服务检查
    total_checks=$((total_checks + 1))
    log_step "检查Docker服务"
    if systemctl is-active docker &> /dev/null && docker info &> /dev/null; then
        log_info "✅ Docker服务正常"
        checks_passed=$((checks_passed + 1))
    else
        log_error "❌ Docker服务异常"
    fi
    
    # 网络检查
    total_checks=$((total_checks + 1))
    log_step "检查Docker网络"
    if command -v docker &> /dev/null && docker network ls | grep -q "$DOCKER_NETWORK"; then
        log_info "✅ Docker网络正常"
        checks_passed=$((checks_passed + 1))
    else
        log_error "❌ Docker网络不存在"
    fi
    
    # 磁盘空间检查
    total_checks=$((total_checks + 1))
    log_step "检查磁盘空间"
    local available_space
    available_space=$(df -BG / | awk 'NR==2 {print $4}' | sed 's/G//')
    if [[ "$available_space" -gt 5 ]]; then
        log_info "✅ 磁盘空间充足 (${available_space}GB可用)"
        checks_passed=$((checks_passed + 1))
    else
        log_warn "⚠️  磁盘空间不足 (${available_space}GB可用)"
    fi
    
    # 容器健康检查
    if command -v docker &> /dev/null; then
        local containers=("mysql" "redis" "nacos" "rmqnamesrv" "rmqbroker" "onlyoffice" "tomcat")
        for container in "${containers[@]}"; do
            total_checks=$((total_checks + 1))
            log_step "检查容器: $container"
            if docker ps --format "table {{.Names}}" | grep -q "^$container$"; then
                if docker exec "$container" echo "health check" &> /dev/null; then
                    log_info "✅ 容器 $container 健康"
                    checks_passed=$((checks_passed + 1))
                else
                    log_warn "⚠️  容器 $container 运行但响应异常"
                fi
            else
                log_info "ℹ️  容器 $container 未运行"
            fi
        done
    fi
    
    # 显示检查结果
    echo ""
    log_title "健康检查结果"
    echo "通过检查: $checks_passed/$total_checks"
    
    local health_percentage
    health_percentage=$((checks_passed * 100 / total_checks))
    if [[ $health_percentage -ge 80 ]]; then
        log_success "🎉 系统健康状况良好 ($health_percentage%)"
    elif [[ $health_percentage -ge 60 ]]; then
        log_warn "⚠️  系统健康状况一般 ($health_percentage%)"
    else
        log_error "❌ 系统健康状况较差 ($health_percentage%)"
    fi
    echo ""
}

# 显示安装日志
show_install_logs() {
    log_title "安装日志"
    
    local log_file="/var/log/skyeye-install.log"
    if [[ -f "$log_file" ]]; then
        echo "最近20行日志:"
        tail -20 "$log_file"
    else
        log_info "未找到安装日志文件"
    fi
}

# 一键安装所有组件（批量安装函数）
batch_install_all_components() {
    log_title "🚀 一键安装所有组件开始"
    
    local components=(
        "docker:Docker环境"
        "docker-compose:Docker Compose"
        "mysql:MySQL 8.0.41"
        "redis:Redis 7.4"
        "nacos:Nacos 2.5.0"
        "rocketmq:RocketMQ 5.3.3"
        "onlyoffice:OnlyOffice DocumentServer 8.2.3.1"
        "tomcat:Tomcat 11.0.9"
    )
    
    local total_components=${#components[@]}
    local current=0
    local success_count=0
    local failed_components=()
    
    echo ""
    echo "📋 安装计划："
    for component_info in "${components[@]}"; do
        local component_name="${component_info%%:*}"
        local component_desc="${component_info##*:}"
        echo "   • $component_desc"
    done
    echo ""
    
    echo -n "是否继续一键安装所有组件? (y/N): "
    read -r confirm_install
    if [[ ! "$confirm_install" =~ ^[Yy]$ ]]; then
        log_info "安装取消"
        return 0
    fi
    
    echo ""
    log_info "开始安装，预计需要10-30分钟，请耐心等待..."
    echo ""
    
    for component_info in "${components[@]}"; do
        current=$((current + 1))
        local component_name="${component_info%%:*}"
        local component_desc="${component_info##*:}"
        
        echo "[$current/$total_components] =================================================="
        log_title "安装 $component_desc"
        echo ""
        
        # 检查是否已安装
        if check_component_installed "$component_name"; then
            log_info "✅ $component_desc 已安装，跳过"
            success_count=$((success_count + 1))
        else
            # 执行安装
            if execute_component_install "$component_name" "$component_desc"; then
                log_info "✅ $component_desc 安装成功"
                success_count=$((success_count + 1))
            else
                log_error "❌ $component_desc 安装失败"
                failed_components+=("$component_desc")
            fi
        fi
        
        echo ""
        sleep 2
    done
    
    # 显示安装结果
    echo "=========================================================="
    log_title "一键安装完成 - 结果统计"
    echo ""
    echo "📊 安装统计："
    echo "   • 总组件数: $total_components"
    echo "   • 成功安装: $success_count"
    echo "   • 安装失败: $((total_components - success_count))"
    echo ""
    
    if [[ ${#failed_components[@]} -eq 0 ]]; then
        log_info "🎉 所有组件安装成功！"
        echo ""
        echo "🔧 后续操作："
        echo "   • 运行健康检查: 选择菜单 10"
        echo "   • 查看服务状态: 选择菜单 9"
        echo "   • 查看服务信息: 选择菜单 11"
    else
        log_warn "⚠️  部分组件安装失败："
        for failed in "${failed_components[@]}"; do
            echo "   • $failed"
        done
        echo ""
        echo "💡 建议："
        echo "   • 检查网络连接和磁盘空间"
        echo "   • 查看安装日志: 选择菜单 12 -> 7"
        echo "   • 单独重新安装失败的组件"
    fi
    
    echo ""
}

# 一键卸载所有组件（批量卸载函数）
batch_uninstall_all_components() {
    log_title "🗑️  一键卸载所有组件"
    
    echo ""
    echo "⚠️  警告: 此操作将卸载所有已安装的组件，包括："
    echo "   • 停止并删除所有Docker容器"
    echo "   • 删除所有Docker镜像和网络"
    echo "   • 卸载Docker环境"
    echo "   • 清理所有数据目录"
    echo ""
    echo "❗ 此操作不可逆转，所有数据将被永久删除！"
    echo ""
    
    echo -n "请输入 'YES' 确认卸载所有组件: "
    read -r confirm_uninstall
    if [[ "$confirm_uninstall" != "YES" ]]; then
        log_info "卸载取消"
        return 0
    fi
    
    echo ""
    log_info "开始卸载所有组件..."
    echo ""
    
    local components=(
        "tomcat:Tomcat容器"
        "onlyoffice:OnlyOffice容器"
        "rocketmq:RocketMQ容器"
        "nacos:Nacos容器"
        "redis:Redis容器"
        "mysql:MySQL容器"
        "docker-compose:Docker Compose"
    )
    
    # 卸载各个组件（按倒序卸载，避免依赖问题）
    log_step "卸载各个组件"
    for component_info in "${components[@]}"; do
        local component_name="${component_info%%:*}"
        local component_desc="${component_info##*:}"
        
        log_info "卸载 $component_desc"
        local script_path="$PACKAGES_DIR/$component_name/scripts/${component_name}-install.sh"
        if [[ -f "$script_path" ]]; then
            bash "$script_path" --uninstall || log_warn "$component_desc 卸载过程中出现警告"
        else
            log_warn "$component_desc 卸载脚本不存在，跳过"
        fi
    done
    
    # 卸载Docker（可选）
    echo ""
    echo -n "是否同时卸载Docker环境? (y/N): "
    read -r uninstall_docker
    if [[ "$uninstall_docker" =~ ^[Yy]$ ]]; then
        log_step "卸载Docker环境"
        local docker_script_path="$PACKAGES_DIR/docker/scripts/docker-install.sh"
        if [[ -f "$docker_script_path" ]]; then
            bash "$docker_script_path" --uninstall
        else
            log_warn "Docker卸载脚本不存在"
        fi
    fi
    
    echo ""
    log_title "🎉 所有组件卸载完成"
    echo ""
    echo "✅ 卸载统计："
    echo "   • 所有组件: 已通过各自脚本卸载"
    if [[ "$uninstall_docker" =~ ^[Yy]$ ]]; then
        echo "   • Docker环境: 已卸载"
    fi
    echo ""
}

# 检查组件是否已安装（调用组件自己的检查脚本）
check_component_installed() {
    local component="$1"
    local script_path="$PACKAGES_DIR/$component/scripts/${component}-install.sh"
    
    if [[ -f "$script_path" ]]; then
        # 调用组件脚本的检查功能
        if bash "$script_path" --check-installed 2>/dev/null; then
            return 0
        fi
    fi
    
    return 1
}

# 主循环
main_loop() {
    while true; do
        show_main_menu
        echo -n "请选择操作 [1-8,21-24,30,0,98-99]: "
        read -r choice
        
        echo ""
        handle_menu_choice "$choice"
        
        echo ""
        echo -n "按回车键继续..."
        read -r
    done
}

# 主函数
main() {
    # 显示Banner
    show_banner
    
    # 系统检查
    check_system
    echo ""
    
    # 检查安装包
    check_packages
    echo ""
    
    # 确保packages目录结构完整
    log_info "检查packages目录结构"
    
    log_success "初始化完成，进入主菜单"
    
    # 进入主循环
    main_loop
}

# 信号处理
trap 'echo -e "\n\n${YELLOW}安装被中断${NC}"; exit 1' INT TERM

# 执行主函数
main "$@"