# Skyeye ERP Shell脚本开发规范

**版本**: v4.0
**更新日期**: 2025-07-29
**适用范围**: Skyeye ERP Docker环境离线安装系统

## 目录结构

### 根目录脚本
```
scripts/
├── install.sh              # 主安装脚本（菜单交互和流程控制）
├── config.sh              # 统一配置文件（所有组件配置）
└── README.md               # 本规范文档
```

### packages目录结构
```
packages/
├── docker/                        # Docker组件目录
│   ├── docker-28.3.2.tgz         # Docker离线安装包
│   └── scripts/
│       └── docker-install.sh     # Docker安装脚本
├── mysql/                         # MySQL组件目录
│   ├── mysql-8.0.41.tar         # MySQL Docker镜像
│   └── scripts/
│       └── mysql-install.sh      # MySQL安装脚本
└── [其他组件]/                    # 按此结构扩展
    ├── [安装包文件]
    └── scripts/
        └── [component]-install.sh
```

## 📋 目录

1. [核心设计原则](#核心设计原则)
2. [编码规范](#编码规范)
3. [脚本接口规范](#脚本接口规范)
4. [开发流程](#开发流程)
5. [最佳实践](#最佳实践)

## 核心设计原则

### 1. 职责分离原则
- **install.sh**: 菜单显示、用户交互、流程控制
- **[component]-install.sh**: 组件的安装、卸载、启动、停止、健康检查
- **config.sh**: 集中管理所有配置参数

### 2. 模块化原则
- 每个组件完全独立，自包含所有功能
- 组件间不直接依赖，通过主脚本协调
- 新增组件只需创建对应目录和脚本

### 3. 统一接口原则
- 所有组件脚本支持标准参数接口
- 遵循相同的返回值规范
- 动态状态检查，基于实际运行状态

## 编码规范

### 1. ShellCheck合规性
- 变量声明和赋值分离（SC2155规则）
- 禁用`set -e`以避免状态检查时脚本中断
- 使用显式错误检查代替自动退出

### 2. 配置文件化管理
- 所有配置参数必须在config.sh中定义
- 脚本中通过`source "$ROOT_DIR/config.sh"`加载配置
- 使用配置变量而不是硬编码值

### 3. 函数命名规范
```bash
# 状态检查函数（供主脚本调用）
check_[component]_installed_status() {
    # 返回标准退出码：0=已启动, 1=未安装, 2=已安装但未启动
}

# 核心功能函数
install_[component]()              # 安装组件
uninstall_[component]()            # 卸载组件
start_[component]_service()        # 启动服务
stop_[component]_service()         # 停止服务
diagnose_[component]_issues()      # 诊断问题
```

### 4. 日志输出规范
```bash
# 统一的日志函数
log_info() { echo -e "${GREEN}[INFO]${NC} $1"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_title() { echo -e "${BLUE}[TITLE]${NC} $1"; }
```

### 5. 错误处理模式
- 使用显式的错误检查和处理
- 等待服务就绪时分阶段检查
- 容错验证，允许部分检查失败

## 脚本接口规范

### 必须支持的参数接口
```bash
./[component]-install.sh                    # 执行安装
./[component]-install.sh --check-installed  # 检查是否已安装
./[component]-install.sh --check-packages   # 检查安装包是否完整
./[component]-install.sh --start            # 启动服务
./[component]-install.sh --stop             # 停止服务
./[component]-install.sh --uninstall        # 卸载组件
./[component]-install.sh --diagnose         # 诊断问题
./[component]-install.sh --help            # 显示帮助信息
```

### 返回值规范
- `--check-installed`: 0=已启动, 1=未安装, 2=已安装但未启动
- `--check-packages`: 0=包完整, 非0=包不完整
- `--start/--stop`: 0=操作成功, 非0=操作失败
- `--uninstall`: 0=卸载成功, 非0=卸载失败

### 脚本结构模板
```bash
#!/bin/bash

# CentOS 7兼容性设置
if [[ ${BASH_VERSION%%.*} -lt 4 ]]; then
    echo "错误: 需要bash 4.0或更高版本"
    exit 1
fi

# 路径计算和配置加载
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ROOT_DIR="$(dirname "$(dirname "$(dirname "$SCRIPT_DIR")")")"
source "$ROOT_DIR/config.sh"

# 必须实现的函数
check_[component]_installed_status() { ... }
install_[component]() { ... }
uninstall_[component]() { ... }
start_[component]_service() { ... }
stop_[component]_service() { ... }
diagnose_[component]_issues() { ... }

# 参数处理
handle_arguments() {
    case "$1" in
        --check-installed) check_[component]_installed_status ;;
        --start) start_[component]_service ;;
        --stop) stop_[component]_service ;;
        --uninstall) uninstall_[component] ;;
        --diagnose) diagnose_[component]_issues ;;
        *) install_[component] ;;
    esac
}

handle_arguments "$@"
```

## 开发流程

### 1. 添加新组件的步骤
```bash
# 1. 创建目录结构
mkdir -p packages/[component]/scripts

# 2. 创建安装脚本（复制模板并修改）
cp packages/mysql/scripts/mysql-install.sh packages/[component]/scripts/[component]-install.sh

# 3. 在config.sh中添加组件配置
export COMPONENT_CONTAINER_NAME="component"
export COMPONENT_VERSION="x.y.z"
export COMPONENT_IMAGE="image:tag"
export COMPONENT_PORT="port"
export COMPONENT_DATA_DIR="${INSTALL_DIR}/component"

# 4. 测试验证
./packages/[component]/scripts/[component]-install.sh --help
./packages/[component]/scripts/[component]-install.sh --check-packages
./packages/[component]/scripts/[component]-install.sh --check-installed
```

### 2. 测试规范
必须测试的场景：
- [ ] 首次安装
- [ ] 重复安装（应该检测到已安装）
- [ ] 启动/停止服务
- [ ] 健康检查和诊断
- [ ] 卸载功能
- [ ] 各种参数调用

## 最佳实践

### 1. 开发流程
1. **配置先行**：在config.sh中定义所有配置参数
2. **参考成功案例**：以MySQL/Redis脚本为模板
3. **功能实现**：按标准函数结构实现
4. **测试验证**：测试所有参数接口
5. **文档完善**：更新README说明

### 2. 常见问题解决
- **变量未定义**：使用`${VAR:-default}`提供默认值
- **等待服务就绪**：分阶段等待（容器启动→服务初始化→验证可用）
- **密码安全**：避免在命令行中暴露密码

### 3. 代码复用
- 优先参考已有的成功实现（MySQL/Redis脚本）
- 保持函数命名的一致性
- 复用相同的错误处理逻辑

### 4. 检查清单
开发新组件时必须完成：
- [ ] 配置已添加到config.sh
- [ ] 实现所有必需的参数接口
- [ ] 通过ShellCheck检查
- [ ] 在CentOS 7环境下测试通过
- [ ] 支持完整的安装/卸载/启动/停止/诊断功能

## 总结

**核心原则**：
1. **配置文件化**：所有配置必须在config.sh中定义
2. **ShellCheck合规**：严格遵循编码规范
3. **参考成功案例**：以MySQL/Redis脚本为标准模板
4. **完整测试**：每个功能都要有对应的测试验证