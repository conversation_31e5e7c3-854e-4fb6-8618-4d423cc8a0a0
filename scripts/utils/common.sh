#!/bin/bash

#=============================================================================
# 通用组件状态检查函数库
# 版本: 1.0
# 用途: 为所有组件提供标准化的状态检查功能
# 
# 重要提醒: install.sh只需要确保整体流程OK，主要负责用户操作，
#          具体逻辑由各自组件的Install来完成！！！
#=============================================================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    if [[ -n "$DEBUG" ]]; then
        echo -e "${PURPLE}[DEBUG]${NC} $1" >&2
    fi
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_title() {
    echo -e "${BLUE}[TITLE]${NC} $1"
}

# 检查Docker是否可用
check_docker_available() {
    # 检查Docker命令是否存在
    if ! command -v docker &> /dev/null; then
        log_debug "Docker命令不存在"
        return 1  # Docker未安装
    fi
    
    # 检查Docker服务是否运行
    if ! systemctl is-active docker &> /dev/null; then
        log_debug "Docker服务未运行"
        return 2  # Docker已安装但未启动
    fi
    
    # 检查Docker守护进程是否响应
    if ! docker info &> /dev/null; then
        log_debug "Docker守护进程无响应"
        return 3  # Docker异常
    fi
    
    log_debug "Docker环境正常"
    return 0  # Docker正常运行
}

# 通用容器状态检查函数
check_container_status() {
    local container_name="$1"
    local health_check_cmd="${2:-}"
    
    # 检查参数
    if [[ -z "$container_name" ]]; then
        log_error "容器名称不能为空"
        return 3  # 参数错误
    fi
    
    # 先检查Docker依赖
    check_docker_available
    local docker_status=$?
    
    case $docker_status in
        1) 
            log_debug "Docker未安装，容器 $container_name 肯定未安装"
            return 1  # 未安装
            ;;
        2)
            log_debug "Docker未启动，容器 $container_name 肯定未启动"
            return 1  # Docker未启动时，容器状态返回未安装
            ;;
        3)
            log_debug "Docker异常，无法检查容器 $container_name 状态"
            return 3  # 异常
            ;;
        0)
            # Docker正常，继续检查容器状态
            ;;
    esac
    
    # 检查容器是否存在并运行
    if docker ps --format "{{.Names}}" 2>/dev/null | grep -q "^${container_name}$"; then
        log_debug "容器 $container_name 正在运行"
        
        # 如果提供了健康检查命令，执行健康检查
        if [[ -n "$health_check_cmd" ]]; then
            if docker exec "$container_name" bash -c "$health_check_cmd" &> /dev/null; then
                log_debug "容器 $container_name 健康检查通过"
                return 0  # 已启动且健康
            else
                log_debug "容器 $container_name 健康检查失败"
                return 3  # 运行但不健康
            fi
        else
            return 0  # 已启动
        fi
    fi
    
    # 检查容器是否存在但未运行
    if docker ps -a --format "{{.Names}}" 2>/dev/null | grep -q "^${container_name}$"; then
        log_debug "容器 $container_name 存在但未运行"
        return 2  # 待启动
    fi
    
    # 容器不存在
    log_debug "容器 $container_name 不存在"
    return 1  # 未安装
}

# 通用系统服务状态检查函数
check_service_status() {
    local service_name="$1"
    local port="${2:-}"
    
    # 检查参数
    if [[ -z "$service_name" ]]; then
        log_error "服务名称不能为空"
        return 3
    fi
    
    # 检查服务是否存在
    if ! systemctl list-unit-files 2>/dev/null | grep -q "^${service_name}.service"; then
        log_debug "服务 $service_name 不存在"
        return 1  # 未安装
    fi
    
    # 检查服务是否运行
    if systemctl is-active "$service_name" &> /dev/null; then
        log_debug "服务 $service_name 正在运行"
        
        # 如果指定了端口，检查端口监听
        if [[ -n "$port" ]]; then
            if netstat -tulpn 2>/dev/null | grep -q ":$port "; then
                log_debug "服务 $service_name 端口 $port 监听正常"
                return 0  # 已启动且端口正常
            else
                log_debug "服务 $service_name 端口 $port 监听异常"
                return 3  # 服务启动但端口异常
            fi
        else
            return 0  # 已启动
        fi
    else
        log_debug "服务 $service_name 未运行"
        return 2  # 待启动
    fi
}

# 通用工具状态检查函数
check_tool_status() {
    local tool_name="$1"
    local version_flag="${2:---version}"
    
    # 检查参数
    if [[ -z "$tool_name" ]]; then
        log_error "工具名称不能为空"
        return 3
    fi
    
    # 检查工具是否可用
    if command -v "$tool_name" &> /dev/null; then
        log_debug "工具 $tool_name 命令存在"
        
        # 尝试执行版本检查确认工具正常
        if "$tool_name" $version_flag &> /dev/null; then
            log_debug "工具 $tool_name 版本检查通过"
            return 0  # 已安装且可用
        else
            log_debug "工具 $tool_name 版本检查失败"
            return 3  # 已安装但异常
        fi
    else
        log_debug "工具 $tool_name 命令不存在"
        return 1  # 未安装
    fi
}

# 状态码转换为文本描述
status_code_to_text() {
    local exit_code="$1"
    local component_name="${2:-组件}"
    
    case $exit_code in
        0) echo "已启动" ;;
        1) echo "未安装" ;;
        2) echo "待启动" ;;
        3) echo "异常" ;;
        *) echo "未知" ;;
    esac
}

# 状态码转换为带颜色的文本描述
status_code_to_colored_text() {
    local exit_code="$1"
    local component_name="${2:-组件}"
    
    case $exit_code in
        0) echo -e "\033[32m已启动\033[0m" ;;   # 绿色
        1) echo -e "\033[31m未安装\033[0m" ;;   # 红色
        2) echo -e "\033[33m待启动\033[0m" ;;   # 黄色
        3) echo -e "\033[35m异常\033[0m" ;;     # 紫色
        *) echo -e "\033[37m未知\033[0m" ;;     # 灰色
    esac
}

# 组件类型判断
get_component_type() {
    local component="$1"
    
    case "$component" in
        "docker") echo "infrastructure" ;;
        "mysql"|"redis"|"nacos"|"rocketmq"|"onlyoffice") echo "container" ;;
        "nginx"|"apache") echo "service" ;;
        "tomcat") echo "application" ;;
        "maven"|"git"|"java") echo "tool" ;;
        *) echo "unknown" ;;
    esac
}