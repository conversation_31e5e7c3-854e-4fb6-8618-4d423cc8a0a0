#!/bin/bash

#=============================================================================
# Skyeye ERP Docker环境配置文件
# 版本: 1.0
# 用途: 集中管理所有组件的配置信息
# 说明: 此文件被所有安装脚本引用，请勿直接执行
#=============================================================================

# ============================================================================
# 全局配置
# ============================================================================

# 基础目录配置
export INSTALL_DIR="/docker/env"                    # Docker容器数据存储根目录
export DOCKER_NETWORK="skyeye-net"                  # Docker自定义网络名称，所有容器加入此网络

# 统一密码配置（所有服务使用相同密码，便于管理）
export SKYEYE_PASSWORD="skyeye123456!"              # 系统统一密码，用于所有数据库和服务认证

# 服务器信息（自动获取本机IP，用于服务间通信和外部访问）
export SERVER_IP=$(hostname -I | awk '{print $1}' 2>/dev/null || echo "localhost")

# 健康检查配置（全局配置，供各组件引用）
export HEALTH_CHECK_TIMEOUT="30"                    # 健康检查超时时间（秒）
export HEALTH_CHECK_RETRIES="3"                     # 健康检查重试次数
export HEALTH_CHECK_INTERVAL="10"                   # 健康检查间隔时间（秒）

# ============================================================================
# Docker配置
# ============================================================================

# Docker基本配置
export DOCKER_VERSION="28.3.2"                     # Docker CE版本号，用于离线安装包文件名
export DOCKER_COMPOSE_VERSION="2.29.1"             # Docker Compose版本号（预留）

# Docker daemon.json配置参数
export DOCKER_REGISTRY_MIRRORS=(                    # Docker镜像源列表（中国大陆优化）
    "https://mirror.ccs.tencentyun.com"
    "https://docker.m.daocloud.io"
    "https://registry.cn-hangzhou.aliyuncs.com"
    "https://docker.mirrors.ustc.edu.cn"
    "https://docker.1panel.live"
    "https://hub-mirror.c.163.com"
)

# Docker日志配置
export DOCKER_LOG_DRIVER="json-file"               # 日志驱动类型
export DOCKER_LOG_MAX_SIZE="100m"                  # 单个日志文件最大大小
export DOCKER_LOG_MAX_FILE="3"                     # 保留的日志文件数量
export DOCKER_LOG_LEVEL="warn"                     # 日志级别 (debug/info/warn/error/fatal)

# Docker存储配置
export DOCKER_STORAGE_DRIVER="overlay2"            # 存储驱动（推荐overlay2）
export DOCKER_DATA_ROOT="/var/lib/docker"          # Docker数据根目录

# Docker网络配置
export DOCKER_IPTABLES="true"                      # 是否启用iptables规则管理
export DOCKER_IP_FORWARD="true"                    # 是否启用IP转发
export DOCKER_IP_MASQ="true"                       # 是否启用IP伪装

# Docker安全配置
export DOCKER_LIVE_RESTORE="true"                  # 容器在Docker重启时保持运行
export DOCKER_USERLAND_PROXY="false"               # 禁用用户态代理（提升性能）

# Docker资源限制配置
export DOCKER_DEFAULT_ULIMITS_NOFILE="65536:65536" # 默认文件描述符限制
export DOCKER_DEFAULT_ULIMITS_NPROC="8192:8192"    # 默认进程数限制

# ============================================================================
# MySQL配置
# ============================================================================

# MySQL容器基本配置
export MYSQL_CONTAINER_NAME="mysql"                 # MySQL容器名称
export MYSQL_VERSION="8.0.41"                      # MySQL版本号，用于镜像标签和安装包文件名
export MYSQL_IMAGE="mysql:${MYSQL_VERSION}"        # MySQL Docker镜像完整名称
export MYSQL_PASSWORD="${SKYEYE_PASSWORD}"         # MySQL root用户密码
export MYSQL_PORT="3306"                           # MySQL服务端口号（标准端口）
export MYSQL_DATA_DIR="${INSTALL_DIR}/mysql"       # MySQL数据文件存储目录

# MySQL数据库配置
export MYSQL_ROOT_PASSWORD="${MYSQL_PASSWORD}"     # MySQL root用户密码（与MYSQL_PASSWORD一致）
export MYSQL_DATABASE="skyeye"                     # 默认创建的应用数据库名
export MYSQL_USER="skyeye"                         # 应用专用数据库用户名（预留）
export MYSQL_USER_PASSWORD="${MYSQL_PASSWORD}"     # 应用专用用户密码（预留）

# MySQL服务器配置参数（根据业务需求优化）
export MYSQL_CHARACTER_SET="utf8mb4"               # 默认字符集，支持完整的UTF-8字符
export MYSQL_COLLATION="utf8mb4_unicode_ci"        # 默认排序规则，支持多语言排序
export MYSQL_LOWER_CASE_TABLE_NAMES="1"            # 表名不区分大小写（兼容性设置）
export MYSQL_MAX_CONNECTIONS="1000"                # 最大连接数，根据业务负载调整
export MYSQL_SQL_MODE="NO_ENGINE_SUBSTITUTION,STRICT_TRANS_TABLES"  # SQL模式，平衡兼容性和严格性
export MYSQL_INNODB_BUFFER_POOL_SIZE="256M"        # InnoDB缓冲池大小，影响查询性能
export MYSQL_INNODB_LOG_FILE_SIZE="256M"           # InnoDB日志文件大小，影响写入性能

# ============================================================================
# Redis配置
# ============================================================================

# Redis容器基本配置
export REDIS_CONTAINER_NAME="redis"                 # Redis容器名称
export REDIS_VERSION="7.4"                         # Redis版本号，用于镜像标签和安装包文件名
export REDIS_IMAGE="redis:${REDIS_VERSION}"        # Redis Docker镜像完整名称
export REDIS_PASSWORD="${SKYEYE_PASSWORD}"         # Redis访问密码，统一使用系统密码
export REDIS_PORT="6379"                           # Redis服务端口号（标准端口）
export REDIS_DATA_DIR="${INSTALL_DIR}/redis"       # Redis数据文件存储目录

# Redis服务器配置参数（性能和持久化优化）
export REDIS_MAX_MEMORY="256mb"                    # Redis最大内存使用限制
export REDIS_MAX_MEMORY_POLICY="allkeys-lru"       # 内存满时的淘汰策略：删除最少使用的键
export REDIS_SAVE_CONFIG="900 1 300 10 60 10000"  # RDB持久化规则：时间窗口内变更次数触发保存
export REDIS_APPENDONLY="yes"                      # 启用AOF持久化，保证数据安全
export REDIS_APPENDFSYNC="everysec"                # AOF同步频率：每秒同步一次，平衡性能和安全
export REDIS_TCP_KEEPALIVE="300"                   # TCP连接保活时间（秒）
export REDIS_TIMEOUT="0"                           # 客户端空闲超时时间，0表示不超时
export REDIS_DATABASES="16"                        # 数据库数量，默认16个库
export REDIS_MAXCLIENTS="10000"                    # 最大客户端连接数

# ============================================================================
# Nacos配置
# ============================================================================

# Nacos容器基本配置
export NACOS_CONTAINER_NAME="nacos"                 # Nacos容器名称
export NACOS_VERSION="2.5.0"                       # Nacos版本号，用于镜像标签和安装包文件名
export NACOS_IMAGE="nacos/nacos-server:v${NACOS_VERSION}"  # Nacos Docker镜像完整名称
export NACOS_HTTP_PORT="8848"                      # Nacos HTTP API端口，用于配置管理和服务发现
export NACOS_GRPC_PORT="9848"                      # Nacos gRPC端口，用于客户端与服务端通信
export NACOS_GRPC_PORT2="9849"                     # Nacos gRPC端口2，用于集群间通信
export NACOS_DATA_DIR="${INSTALL_DIR}/nacos"       # Nacos数据文件存储目录

# Nacos数据持久化配置（使用MySQL存储数据）
export NACOS_DB_HOST="${MYSQL_CONTAINER_NAME}"     # 数据库主机名，引用MySQL容器名
export NACOS_DB_PORT="${MYSQL_PORT}"               # 数据库端口，引用MySQL端口配置
export NACOS_DB_NAME="nacos"                       # Nacos专用数据库名，存储配置和服务注册信息
export NACOS_DB_USER="root"                        # 数据库连接用户名，使用MySQL root用户
export NACOS_DB_PASSWORD="${MYSQL_PASSWORD}"       # 数据库连接密码，引用MySQL密码

# Nacos安全认证配置（启用认证增强安全性）
export NACOS_AUTH_ENABLE="false"                   # 关闭Nacos认证功能（开发环境可关闭）
export NACOS_AUTH_TOKEN="SecretKey012345678901234567890123456789012345678901234567890123456789"  # JWT Token密钥，64位
export NACOS_AUTH_IDENTITY_KEY="serverIdentity"    # 服务器身份标识键名
export NACOS_AUTH_IDENTITY_VALUE="security"        # 服务器身份标识值

# ============================================================================
# RocketMQ配置
# ============================================================================

# RocketMQ容器基本配置
export ROCKETMQ_NAMESERVER_CONTAINER_NAME="rmqnamesrv"      # NameServer容器名称
export ROCKETMQ_BROKER_CONTAINER_NAME="rmqbroker"          # Broker容器名称
export ROCKETMQ_VERSION="5.3.3"                           # RocketMQ版本号，用于镜像标签和安装包文件名
export ROCKETMQ_IMAGE="apache/rocketmq:${ROCKETMQ_VERSION}"  # RocketMQ Docker镜像完整名称

# RocketMQ网络端口配置
export ROCKETMQ_NAMESERVER_PORT="9876"                    # NameServer端口，客户端连接入口
export ROCKETMQ_BROKER_PORT="10909"                       # Broker主端口，用于客户端连接
export ROCKETMQ_BROKER_HA_PORT="10912"                    # Broker高可用端口，用于主从同步
export ROCKETMQ_BROKER_FAST_PORT="10911"                  # Broker快速端口，用于消息发送

# RocketMQ数据存储配置
export ROCKETMQ_DATA_DIR="${INSTALL_DIR}/rocketmq"        # RocketMQ数据文件存储目录

# RocketMQ JVM内存配置（根据系统资源调整）
export ROCKETMQ_NAMESERVER_MAX_HEAP="256m"                # NameServer最大堆内存
export ROCKETMQ_NAMESERVER_HEAP_NEWSIZE="128m"            # NameServer新生代内存
export ROCKETMQ_BROKER_MAX_HEAP="1g"                      # Broker最大堆内存，增加以提升性能
export ROCKETMQ_BROKER_HEAP_NEWSIZE="512m"                # Broker新生代内存，配合堆内存调整

# RocketMQ集群配置
export ROCKETMQ_BROKER_IP="${SERVER_IP}"                  # Broker对外广播IP，Docker环境必须配置为宿主机IP
export ROCKETMQ_CLUSTER_NAME="DefaultCluster"             # 集群名称，多个Broker可组成集群
export ROCKETMQ_BROKER_NAME="broker-a"                    # Broker实例名称，集群内唯一标识

# RocketMQ容器内路径配置（容器内部固定路径）
export ROCKETMQ_CONTAINER_STORE_PATH="/home/<USER>/store"  # 容器内消息存储路径
export ROCKETMQ_CONTAINER_LOG_PATH="/home/<USER>/logs"     # 容器内应用日志路径
export ROCKETMQ_CONTAINER_ROCKETMQLOGS_PATH="/home/<USER>/logs/rocketmqlogs"  # 容器内RocketMQ日志路径
export ROCKETMQ_CONTAINER_CONF_PATH="/home/<USER>/rocketmq-${ROCKETMQ_VERSION}/conf/broker.conf"  # 容器内配置文件路径

# ============================================================================
# OnlyOffice配置
# ============================================================================

# OnlyOffice容器基本配置
export ONLYOFFICE_CONTAINER_NAME="onlyoffice"             # OnlyOffice容器名称
export ONLYOFFICE_VERSION="8.2.3.1"                      # OnlyOffice版本号，用于镜像标签和安装包文件名
export ONLYOFFICE_IMAGE="onlyoffice/documentserver:${ONLYOFFICE_VERSION}"  # OnlyOffice Docker镜像完整名称
export ONLYOFFICE_PORT="8000"                            # OnlyOffice HTTP服务端口，用于文档编辑服务
export ONLYOFFICE_DATA_DIR="${INSTALL_DIR}/onlyoffice"   # OnlyOffice数据文件存储目录

# OnlyOffice安全配置（JWT认证，根据业务需求可启用）
export ONLYOFFICE_JWT_ENABLED="false"                    # JWT认证开关，false=禁用（便于集成测试）
export ONLYOFFICE_JWT_SECRET=""                          # JWT密钥，启用认证时填写
export ONLYOFFICE_JWT_HEADER="Authorization"             # JWT头部字段名，标准HTTP认证头

# OnlyOffice容器内路径配置（容器内部固定路径）
export ONLYOFFICE_CONTAINER_CONFIG_PATH="/etc/onlyoffice/documentserver/default.json"  # 容器内配置文件路径
export ONLYOFFICE_CONTAINER_DATA_PATH="/var/www/onlyoffice/Data"     # 容器内数据存储路径
export ONLYOFFICE_CONTAINER_LOG_PATH="/var/log/onlyoffice"           # 容器内日志路径
export ONLYOFFICE_CONTAINER_LIB_PATH="/var/lib/onlyoffice"           # 容器内库文件路径
export ONLYOFFICE_CONTAINER_DB_PATH="/var/lib/postgresql"            # 容器内PostgreSQL数据路径

# OnlyOffice健康检查配置
export ONLYOFFICE_HEALTHCHECK_URL="/healthcheck"                     # 健康检查API端点
export ONLYOFFICE_HEALTHCHECK_TIMEOUT="${HEALTH_CHECK_TIMEOUT}"      # 健康检查超时时间，引用全局配置

# ============================================================================
# Tomcat配置
# ============================================================================

# Tomcat容器基本配置
export TOMCAT_CONTAINER_NAME="tomcat"                     # Tomcat容器名称
export TOMCAT_VERSION="11.0.9"                           # Tomcat版本号，用于镜像标签和安装包文件名
export TOMCAT_IMAGE="tomcat:${TOMCAT_VERSION}"           # Tomcat Docker镜像完整名称
export TOMCAT_PORT="8080"                                # Tomcat HTTP服务端口，Web应用访问端口
export TOMCAT_AJP_PORT="8009"                            # Tomcat AJP协议端口，用于与Apache等Web服务器集成
export TOMCAT_DATA_DIR="${INSTALL_DIR}/tomcat"           # Tomcat数据文件存储目录

# Tomcat JVM性能配置
export TOMCAT_JVM_XMS="512m"                             # JVM初始堆内存大小
export TOMCAT_JVM_XMX="1024m"                            # JVM最大堆内存大小
export TOMCAT_JVM_OPTS="-Dfile.encoding=UTF-8 -Duser.timezone=Asia/Shanghai"  # JVM启动参数：字符编码和时区

# ============================================================================
# 日志配置
# ============================================================================

export LOG_DIR="/var/log/skyeye-install"            # 安装日志存储目录
export LOG_FILE="${LOG_DIR}/install.log"            # 安装日志文件完整路径
export LOG_LEVEL="INFO"                             # 日志级别：DEBUG/INFO/WARN/ERROR

# ============================================================================
# 备份配置
# ============================================================================

export BACKUP_DIR="/docker/backup"                  # 数据备份存储目录
export BACKUP_RETENTION_DAYS="7"                    # 备份文件保留天数


# ============================================================================
# 容器资源限制配置（根据服务器资源合理分配）
# ============================================================================

# MySQL资源限制
export MYSQL_MEMORY_LIMIT="1g"                      # MySQL容器内存限制，数据库需要足够内存缓存
export MYSQL_CPU_LIMIT="1.0"                        # MySQL容器CPU限制，1.0表示1个CPU核心

# Redis资源限制
export REDIS_MEMORY_LIMIT="512m"                    # Redis容器内存限制，内存数据库适中即可
export REDIS_CPU_LIMIT="0.5"                        # Redis容器CPU限制，0.5个CPU核心

# Nacos资源限制
export NACOS_MEMORY_LIMIT="1g"                      # Nacos容器内存限制，配置中心需要适当内存
export NACOS_CPU_LIMIT="1.0"                        # Nacos容器CPU限制，1个CPU核心

# RocketMQ资源限制
export ROCKETMQ_NAMESERVER_MEMORY_LIMIT="512m"      # NameServer内存限制，注册中心内存需求较小
export ROCKETMQ_BROKER_MEMORY_LIMIT="4g"            # Broker内存限制，增加到4GB确保消息处理性能
export ROCKETMQ_CPU_LIMIT="1.0"                     # RocketMQ容器CPU限制，1个CPU核心

# OnlyOffice资源限制
export ONLYOFFICE_MEMORY_LIMIT="2g"                 # OnlyOffice内存限制，文档处理需要较多内存
export ONLYOFFICE_CPU_LIMIT="2.0"                   # OnlyOffice CPU限制，文档转换需要较多CPU资源

# Tomcat资源限制
export TOMCAT_MEMORY_LIMIT="1g"                     # Tomcat容器内存限制，Web应用服务器标准配置
export TOMCAT_CPU_LIMIT="1.0"                       # Tomcat容器CPU限制，1个CPU核心

# ============================================================================
# 函数：显示配置信息（用于配置验证和故障排查）
# ============================================================================

show_config_info() {
    echo "=========================================="
    echo "Skyeye ERP Docker环境配置信息"
    echo "=========================================="
    echo ""
    echo "🐳 Docker配置："
    echo "   版本: $DOCKER_VERSION"
    echo "   网络: $DOCKER_NETWORK"
    echo "   安装目录: $INSTALL_DIR"
    echo ""
    echo "🗄️  MySQL配置："
    echo "   容器名: $MYSQL_CONTAINER_NAME"
    echo "   版本: $MYSQL_VERSION"
    echo "   端口: $MYSQL_PORT"
    echo "   密码: $MYSQL_PASSWORD"
    echo "   数据目录: $MYSQL_DATA_DIR"
    echo ""
    echo "🔧 Redis配置："
    echo "   容器名: $REDIS_CONTAINER_NAME"
    echo "   版本: $REDIS_VERSION"
    echo "   端口: $REDIS_PORT"
    echo "   密码: $REDIS_PASSWORD"
    echo "   数据目录: $REDIS_DATA_DIR"
    echo ""
    echo "📋 Nacos配置："
    echo "   容器名: $NACOS_CONTAINER_NAME"
    echo "   版本: $NACOS_VERSION"
    echo "   HTTP端口: $NACOS_HTTP_PORT"
    echo "   gRPC端口: $NACOS_GRPC_PORT, $NACOS_GRPC_PORT2"
    echo "   数据目录: $NACOS_DATA_DIR"
    echo ""
    echo "🚀 RocketMQ配置："
    echo "   NameServer容器: $ROCKETMQ_NAMESERVER_CONTAINER_NAME"
    echo "   Broker容器: $ROCKETMQ_BROKER_CONTAINER_NAME"
    echo "   版本: $ROCKETMQ_VERSION"
    echo "   NameServer端口: $ROCKETMQ_NAMESERVER_PORT"
    echo "   Broker IP: $ROCKETMQ_BROKER_IP"
    echo "   NameServer内存: ${ROCKETMQ_NAMESERVER_MAX_HEAP} (新生代: ${ROCKETMQ_NAMESERVER_HEAP_NEWSIZE})"
    echo "   Broker内存: ${ROCKETMQ_BROKER_MAX_HEAP} (新生代: ${ROCKETMQ_BROKER_HEAP_NEWSIZE})"
    echo "   数据目录: $ROCKETMQ_DATA_DIR"
    echo ""
    echo "📄 OnlyOffice配置："
    echo "   容器名: $ONLYOFFICE_CONTAINER_NAME"
    echo "   版本: $ONLYOFFICE_VERSION"
    echo "   端口: $ONLYOFFICE_PORT"
    echo "   数据目录: $ONLYOFFICE_DATA_DIR"
    echo ""
    echo "🌐 Tomcat配置："
    echo "   容器名: $TOMCAT_CONTAINER_NAME"
    echo "   版本: $TOMCAT_VERSION"
    echo "   端口: $TOMCAT_PORT"
    echo "   数据目录: $TOMCAT_DATA_DIR"
    echo ""
    echo "🔗 访问地址："
    echo "   MySQL: mysql://root:$MYSQL_PASSWORD@$SERVER_IP:$MYSQL_PORT/"
    echo "   Redis: redis://$SERVER_IP:$REDIS_PORT (密码: $REDIS_PASSWORD)"
    echo "   Nacos: http://$SERVER_IP:$NACOS_HTTP_PORT/nacos/"
    echo "   RocketMQ: $SERVER_IP:$ROCKETMQ_NAMESERVER_PORT"
    echo "   OnlyOffice: http://$SERVER_IP:$ONLYOFFICE_PORT/"
    echo "   Tomcat: http://$SERVER_IP:$TOMCAT_PORT/"
    echo ""
}

# ============================================================================
# 函数：验证配置
# ============================================================================

validate_config() {
    local errors=0
    
    # 检查必要的环境变量
    if [[ -z "$INSTALL_DIR" ]]; then
        echo "错误: INSTALL_DIR 未设置"
        errors=$((errors + 1))
    fi
    
    if [[ -z "$DOCKER_NETWORK" ]]; then
        echo "错误: DOCKER_NETWORK 未设置"
        errors=$((errors + 1))
    fi
    
    # 检查密码强度
    if [[ ${#MYSQL_PASSWORD} -lt 8 ]]; then
        echo "警告: MySQL密码长度少于8位"
    fi
    
    if [[ ${#REDIS_PASSWORD} -lt 8 ]]; then
        echo "警告: Redis密码长度少于8位"
    fi
    
    return $errors
}

# ============================================================================
# 当脚本被直接执行时显示配置信息
# ============================================================================

if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    show_config_info
    echo ""
    echo "💡 提示: 此文件是配置文件，请勿直接执行"
    echo "    如需修改配置，请编辑此文件然后重新运行安装脚本"
fi