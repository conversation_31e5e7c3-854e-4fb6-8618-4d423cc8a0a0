${AnsiColor.BRIGHT_GREEN}
>>=========================================================================================
>> :: Project Name ::          ${spring.application.name}
>> :: Project AppId ::         ${skyeye.appid}
>> :: Project alias ::         ${skyeye.serviceName}
>> :: Project Api Version ::   ${skyeye.load.api}
>> :: XXL JOB Executor Port :: ${xxl.job.executor.port}
>> :: Host ::                  ${spring.cloud.client.hostname}(${spring.cloud.client.ip-address})
>> :: Port ::                  ${server.port}
>> :: Discovery server ::      ${spring.cloud.nacos.discovery.server-addr}
>> :: Config server ::         ${spring.cloud.nacos.config.server-addr}?namespace=${spring.cloud.nacos.config.namespace}
>> :: Spring Boot ::           ${spring-boot.formatted-version}
>>=========================================================================================
***********  Copyright © 2018 - ${skyeye.year} skyeye 卫志强(weizhiqiang) Software. All Rights Reserved  **************