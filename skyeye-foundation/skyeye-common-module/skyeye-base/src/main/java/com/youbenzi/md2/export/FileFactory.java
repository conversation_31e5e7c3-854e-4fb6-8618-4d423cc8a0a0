/*******************************************************************************
 * Copyright 卫志强 QQ：<EMAIL> Inc. All rights reserved. 开源地址：https://gitee.com/doc_wei01/skyeye
 ******************************************************************************/

package com.youbenzi.md2.export;

import com.youbenzi.md2.markdown.Block;
import com.youbenzi.md2.markdown.MDAnalyzer;

import java.io.*;
import java.nio.charset.Charset;
import java.util.List;

/**
 * @ClassName: FileFactory
 * @Description: 文档生成工厂
 * @author: skyeye云系列--卫志强
 * @date: 2022/5/26 12:21
 * @Copyright: 2021 https://gitee.com/doc_wei01/skyeye Inc. All rights reserved.
 * 注意：本内容仅限购买后使用.禁止私自外泄以及用于其他的商业目的
 */
public class FileFactory {

    public static String Encoding = "UTF-8";

    public static void produce(File file, String outputFilePath, String webRootNdc, String sysWaterMark) {
        BufferedReader reader = null;
        try {
            reader = new BufferedReader(new InputStreamReader(new FileInputStream(file), Encoding));
        } catch (Exception ex) {
            throw new RuntimeException(ex);
        }
        produce(reader, outputFilePath, webRootNdc, sysWaterMark);
    }

    public static void produce(InputStream is, String outputFilePath, String webRootNdc, String sysWaterMark) {
        BufferedReader reader = new BufferedReader(new InputStreamReader(is, Charset.forName(Encoding)));
        produce(reader, outputFilePath, webRootNdc, sysWaterMark);
    }

    public static void produce(String mdText, String outputFilePath, String webRootNdc, String sysWaterMark) {
        BufferedReader reader = new BufferedReader(new StringReader(mdText));
        produce(reader, outputFilePath, webRootNdc, sysWaterMark);
    }

    public static void produce(BufferedReader reader, String outputFilePath, String webRootNdc, String sysWaterMark) {
        List<Block> list = MDAnalyzer.analyze(reader);
        produce(list, outputFilePath, webRootNdc, sysWaterMark);
    }

    public static void produce(List<Block> list, String outputFilePath, String webRootNdc, String sysWaterMark) {
        try {
            String ext = getExtOfFile(outputFilePath);
            Decorator decorator = BuilderFactory.build(ext);
            decorator.beginWork(outputFilePath, sysWaterMark);
            decorator.decorate(list, webRootNdc);
            decorator.afterWork(outputFilePath);
        } catch (Exception ee) {
            throw new RuntimeException(ee);
        }
    }

    private static String getExtOfFile(String outputFilePath) {
        if (outputFilePath == null) {
            return "";
        }
        int i = outputFilePath.lastIndexOf(".");
        if (i < 0) {
            return "";
        }
        return outputFilePath.substring(i + 1);
    }

}
