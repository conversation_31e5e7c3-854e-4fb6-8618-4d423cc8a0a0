/*******************************************************************************
 * Copyright 卫志强 QQ：<EMAIL> Inc. All rights reserved. 开源地址：https://gitee.com/doc_wei01/skyeye
 ******************************************************************************/

package com.youbenzi.md2.markdown.builder;

import com.youbenzi.md2.markdown.Block;

/**
 * @ClassName: BlockBuilder
 * @Description: markdown语法块
 * @author: skyeye云系列--卫志强
 * @date: 2022/5/26 12:21
 * @Copyright: 2021 https://gitee.com/doc_wei01/skyeye Inc. All rights reserved.
 * 注意：本内容仅限购买后使用.禁止私自外泄以及用于其他的商业目的
 */
public interface BlockBuilder {

    /**
     * 创建语法块
     *
     * @return 结果
     */
    Block bulid();

    /**
     * 检查内容是否属于当前语法块
     *
     * @return 结果
     */
    boolean isRightType();
}
