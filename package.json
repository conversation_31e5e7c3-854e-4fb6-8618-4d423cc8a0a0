{"name": "vite-uniapp-vue3", "type": "module", "version": "1.0.0", "private": true, "packageManager": "pnpm@10.12.4", "author": "lincenying <<EMAIL>>", "scripts": {"dev:app": "uni -p app", "dev:app-android": "uni -p app-android", "dev:app-ios": "uni -p app-ios", "dev:custom": "uni -p", "serve": "uni", "dev:h5": "uni", "dev:h5:ssr": "uni --ssr", "dev:mp-weixin": "uni -p mp-weixin", "build:app": "uni build -p app", "build:app-android": "uni build -p app-android", "build:app-ios": "uni build -p app-ios", "build:custom": "uni build -p", "build:h5": "uni build", "build:h5:ssr": "uni build --ssr", "build:mp-weixin": "uni build -p mp-weixin", "up": "pnpm up --latest --interactive \"!@dcloudio*\"", "lint": "eslint .", "lint:fix": "eslint . --fix", "lint:ts": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "lint:css": "stylelint '**/*.{css,scss}' --fix", "preinstall": "npx only-allow pnpm", "prepare": "npx simple-git-hooks"}, "dependencies": {"@dcloudio/uni-app": "3.0.0-4060620250520001", "@dcloudio/uni-app-plus": "3.0.0-4060620250520001", "@dcloudio/uni-components": "3.0.0-4060620250520001", "@dcloudio/uni-h5": "3.0.0-4060620250520001", "@dcloudio/uni-mp-weixin": "3.0.0-4060620250520001", "@dcloudio/uni-ui": "^1.5.8", "@lincy/async-validation": "^1.3.0", "@lincy/tnui-vue3-uniapp": "^1.0.21", "@lincy/utils": "^0.6.3", "@tuniao/tn-icon": "^1.11.0", "@tuniao/tn-style": "^1.0.20", "@tuniao/tnui-vue3-uniapp": "^1.0.23", "@uni-helper/uni-use": "^0.19.14", "@vue/compiler-sfc": "^3.5.17", "@vueuse/core": "^13.5.0", "axios": "^1.7.9", "fant-axios-adapter": "^0.0.6", "pinia": "^2.3.0", "unhead": "^2.0.12", "vue": "^3.5.17", "we-cropper": "^1.4.0", "wot-design-uni": "^1.10.0"}, "devDependencies": {"@dcloudio/types": "^3.4.16", "@dcloudio/uni-cli-shared": "3.0.0-4060620250520001", "@dcloudio/uni-stacktracey": "3.0.0-4060620250520001", "@dcloudio/vite-plugin-uni": "3.0.0-4060620250520001", "@iconify-json/carbon": "^1.2.10", "@iconify-json/line-md": "^1.2.8", "@iconify-json/mdi": "^1.2.3", "@iconify-json/svg-spinners": "^1.2.2", "@lincy/eslint-config": "^5.7.0", "@lincy/stylelint-config": "^2.2.0", "@lincy/uniapp-ui-resolver": "^1.2.1", "@lincy/unocss-base-config": "^2.3.0", "@types/node": "^24.0.10", "@unhead/vue": "^2.0.12", "@uni-helper/uni-app-types": "1.0.0-alpha.6", "@uni-helper/vite-plugin-uni-components": "^0.2.0", "@uni-helper/vite-plugin-uni-layouts": "^0.1.10", "@uni-helper/vite-plugin-uni-pages": "^0.2.28", "@unocss/eslint-plugin": "^66.3.3", "@vue-macros/volar": "^3.0.0-beta.15", "eslint": "^9.30.1", "eslint-plugin-format": "^1.0.1", "lint-staged": "^16.1.2", "miniprogram-api-typings": "^4.0.7", "pnpm": "^10.12.4", "postcss": "^8.5.6", "postcss-px-to-viewport-8-plugin": "^1.2.5", "sass": "^1.89.2", "simple-git-hooks": "^2.13.0", "stylelint": "^16.21.1", "typescript": "^5.9.2", "unocss": "66.0.0", "unocss-applet": "^0.10.0", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.8.0", "unplugin-vue-macros": "^2.14.5", "vite": "^7.0.0", "vue-tsc": "^2.2.12"}, "pnpm": {"onlyBuiltDependencies": ["core-js", "esbuild", "simple-git-hooks", "unrs-resolver", "vue-demi", "we-cropper"]}, "simple-git-hooks": {"pre-commit": "pnpm lint-staged"}, "lint-staged": {"*.{ts,tsx,vue}": ["eslint --fix"], "*.{css,scss}": "stylelint --fix --allow-empty-input"}}