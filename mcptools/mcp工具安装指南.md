

# 安装playwright相关包
npm install -g @playwright/test
npm install -g @executeautomation/playwright-mcp-server

## 安装浏览器（会下载Chrome、Firefox等，比较大，需要等一会）
npx playwright install
第二步：移除旧配置（如果有的话）
claude mcp remove playwright -s local

第三步：用正确的方式配置（关键步骤！）
claude mcp add playwright cmd /c npx @executeautomation/playwright-mcp-server 

这一步的意思是：告诉Claude Code要先启动Windows命令行(cmd)，然后在命令行里执行npx命令。

第四步：验证是否成功
## 查看配置是否正确
claude mcp list

## 测试连接（会看到很多调试信息，找到这一行就是成功了）
claude --print "测试playwright" --debug | findstr "connected"




# context7 
claude mcp add context7 -- cmd /c npx @upstash/context7-mcp

## 1. 删除错误的配置
claude mcp remove playwright -s local
claude mcp remove context7 -s local
