<?xml version="1.0" encoding="UTF-8"?>

<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 http://maven.apache.org/xsd/settings-1.0.0.xsd">
  
  <!-- 本地仓库路径 -->
  <localRepository>Z:\maven_repository</localRepository>

  <!-- 插件组 -->
  <pluginGroups>
    <pluginGroup>org.apache.maven.plugins</pluginGroup>
    <pluginGroup>org.codehaus.mojo</pluginGroup>
  </pluginGroups>

  <!-- 代理配置（如果需要） -->
  <proxies>
    <!-- 如果在公司内网需要代理，可以在这里配置 -->
  </proxies>

  <!-- 服务器认证配置 -->
  <servers>
    <!-- 如果需要发布到私有仓库，可以在这里配置认证信息 -->
  </servers>

  <!-- 镜像配置 - 使用国内镜像加速下载 -->
  <mirrors>
    <!-- 阿里云Maven中央仓库镜像 -->
    <mirror>
      <id>aliyun-central</id>
      <mirrorOf>central</mirrorOf>
      <name>阿里云Maven中央仓库</name>
      <url>https://maven.aliyun.com/repository/central</url>
    </mirror>
    
    <!-- 阿里云Maven公共仓库镜像 -->
    <mirror>
      <id>aliyun-public</id>
      <mirrorOf>*</mirrorOf>
      <name>阿里云Maven公共仓库</name>
      <url>https://maven.aliyun.com/repository/public</url>
    </mirror>
  </mirrors>

  <!-- 配置文件 -->
  <profiles>
    <!-- 默认配置 -->
    <profile>
      <id>default</id>
      
      <!-- 仓库配置 -->
      <repositories>
        <!-- 阿里云Maven中央仓库 -->
        <repository>
          <id>aliyun-central</id>
          <name>阿里云Maven中央仓库</name>
          <url>https://maven.aliyun.com/repository/central</url>
          <releases>
            <enabled>true</enabled>
          </releases>
          <snapshots>
            <enabled>false</enabled>
          </snapshots>
        </repository>
        
        <!-- 阿里云Maven公共仓库 -->
        <repository>
          <id>aliyun-public</id>
          <name>阿里云Maven公共仓库</name>
          <url>https://maven.aliyun.com/repository/public</url>
          <releases>
            <enabled>true</enabled>
          </releases>
          <snapshots>
            <enabled>false</enabled>
          </snapshots>
        </repository>
        
        <!-- 阿里云Spring仓库 -->
        <repository>
          <id>aliyun-spring</id>
          <name>阿里云Spring仓库</name>
          <url>https://maven.aliyun.com/repository/spring</url>
          <releases>
            <enabled>true</enabled>
          </releases>
          <snapshots>
            <enabled>false</enabled>
          </snapshots>
        </repository>
        
        <!-- 阿里云Spring插件仓库 -->
        <repository>
          <id>aliyun-spring-plugin</id>
          <name>阿里云Spring插件仓库</name>
          <url>https://maven.aliyun.com/repository/spring-plugin</url>
          <releases>
            <enabled>true</enabled>
          </releases>
          <snapshots>
            <enabled>false</enabled>
          </snapshots>
        </repository>
        
        <!-- 腾讯云Maven仓库（备用） -->
        <repository>
          <id>tencent-central</id>
          <name>腾讯云Maven中央仓库</name>
          <url>https://mirrors.cloud.tencent.com/nexus/repository/maven-public/</url>
          <releases>
            <enabled>true</enabled>
          </releases>
          <snapshots>
            <enabled>false</enabled>
          </snapshots>
        </repository>
        
        <!-- 华为云Maven仓库（备用） -->
        <repository>
          <id>huawei-central</id>
          <name>华为云Maven中央仓库</name>
          <url>https://repo.huaweicloud.com/repository/maven/</url>
          <releases>
            <enabled>true</enabled>
          </releases>
          <snapshots>
            <enabled>false</enabled>
          </snapshots>
        </repository>
      </repositories>

      <!-- 插件仓库配置 -->
      <pluginRepositories>
        <!-- 阿里云Maven插件仓库 -->
        <pluginRepository>
          <id>aliyun-plugin</id>
          <name>阿里云Maven插件仓库</name>
          <url>https://maven.aliyun.com/repository/central</url>
          <releases>
            <enabled>true</enabled>
          </releases>
          <snapshots>
            <enabled>false</enabled>
          </snapshots>
        </pluginRepository>
        
        <!-- 阿里云Spring插件仓库 -->
        <pluginRepository>
          <id>aliyun-spring-plugin</id>
          <name>阿里云Spring插件仓库</name>
          <url>https://maven.aliyun.com/repository/spring-plugin</url>
          <releases>
            <enabled>true</enabled>
          </releases>
          <snapshots>
            <enabled>false</enabled>
          </snapshots>
        </pluginRepository>
      </pluginRepositories>
      
      <!-- 属性配置 -->
      <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
      </properties>
    </profile>
  </profiles>

  <!-- 激活的配置文件 -->
  <activeProfiles>
    <activeProfile>default</activeProfile>
  </activeProfiles>
</settings>
