# ERP系统多租户架构设计文档

## 目录

1. [概述](#1-概述)
2. [设计理念与原则](#2-设计理念与原则)
3. [现状分析](#3-现状分析)
4. [总体架构设计](#4-总体架构设计)
5. [模块设计](#5-模块设计)
6. [数据隔离策略](#6-数据隔离策略)
7. [用户身份管理与租户切换](#7-用户身份管理与租户切换)
8. [租户套餐收费模式](#8-租户套餐收费模式)
9. [安全策略](#9-安全策略)
10. [实施方案](#10-实施方案)
11. [附录](#11-附录)

---

## 1. 概述

### 1.1 项目背景

本ERP系统目前采用微服务架构，包含多个业务模块（skyeye-business）、基础设施模块（skyeye-infrastructure）、前端模块（skyeye-frontend）等。项目中已有基础的租户实体和注解，但缺乏完整的多租户实现。

### 1.2 设计目标

- **低侵入性**：不修改现有业务代码，通过独立模块实现多租户功能
- **灵活性**：支持多种数据隔离级别和收费模式
- **可扩展性**：模块化设计，便于后续功能扩展
- **安全性**：确保租户数据完全隔离，防止数据泄露
- **用户体验**：简化登录流程，支持租户无缝切换

### 1.3 业务需求

1. 用户登录时无需选择公司，登录后可选择租户
2. 支持用户在多个租户间切换，无需重新登录
3. 支持多种收费模式：买断制、员工数量制、企业套餐制
4. 确保不同租户数据完全隔离
5. 对现有项目代码零侵入或最小侵入

---

## 2. 设计理念与原则

### 2.1 核心设计原则

#### 2.1.1 简单至上 (KISS)
- 架构设计追求简洁清晰，避免过度复杂的设计
- 接口设计简单易用，降低开发人员使用门槛
- 配置项精简，默认值合理

#### 2.1.2 精益求精 (YAGNI)
- 只实现当前明确需要的功能
- 避免预设过多未来可能需要的特性
- 采用增量式开发，逐步完善功能

#### 2.1.3 坚实基础 (SOLID)
- **S (单一职责)**：每个组件只负责一个明确的功能
- **O (开放/封闭)**：通过接口和插件机制支持功能扩展
- **L (里氏替换)**：所有实现类可无缝替换其接口
- **I (接口隔离)**：接口设计专一，避免臃肿接口
- **D (依赖倒置)**：依赖抽象接口而非具体实现

#### 2.1.4 杜绝重复 (DRY)
- 多租户通用逻辑抽象为公共组件
- 配置信息集中管理，避免重复配置
- 代码模板化，减少重复开发

### 2.2 架构风格

采用**微内核架构**模式：
- **内核**：提供多租户核心能力（上下文管理、数据隔离、身份认证）
- **插件**：业务特定的租户逻辑（收费模式、权限控制、数据同步）

---

## 3. 现状分析

### 3.1 项目架构现状

```
erp/
├── skyeye-business/           # 业务模块
│   ├── skyeye-promote/       # 核心推广模块
│   │   ├── skyeye-userauth/  # 用户认证模块（已含基础租户功能）
│   │   └── ...
│   ├── skyeye-erp/           # ERP业务模块
│   ├── skyeye-crm/           # CRM模块
│   └── ...
├── skyeye-foundation/         # 基础模块
│   ├── skyeye-common-module/ # 公共模块（已含租户注解）
│   └── skyeye-common-rest/   # 公共REST模块
├── skyeye-frontend/          # 前端模块
└── skyeye-infrastructure/    # 基础设施模块
    └── skyeye-zuul/          # 网关模块
```

### 3.2 现有多租户实现分析

#### 3.2.1 已有组件
- `Tenant` 实体类：基础租户信息
- `TenantEnum` 枚举：四种隔离级别（强隔离、弱隔离、无隔离、平台模式）
- `@TenantIsolation` 注解：方法级别租户隔离声明
- `@IgnoreTenant` 注解：忽略租户过滤声明
- `CommonConstants`：租户相关常量定义

#### 3.2.2 缺失组件
- 租户上下文管理（TenantContext）
- SQL拦截器（TenantSqlInterceptor）
- AOP切面（TenantAspect）
- Web拦截器（TenantInterceptor）
- 用户-租户关系管理
- 租户切换机制
- 收费模式管理

### 3.3 技术栈分析

- **后端框架**：Spring Boot + Spring Cloud
- **数据访问**：MyBatis + MyBatis-Plus
- **安全框架**：基于Token的身份认证
- **数据库**：MySQL
- **前端框架**：Vue.js + Layui
- **网关**：Zuul

---

## 4. 总体架构设计

### 4.1 架构概览

```mermaid
graph TB
    subgraph "前端层"
        FE[Vue前端] --> GW[Zuul网关]
        LAY[Layui前端] --> GW
    end
    
    subgraph "网关层"
        GW --> TI[租户拦截器]
        TI --> AUTH[身份认证服务]
    end
    
    subgraph "业务层"
        AUTH --> BIZ[业务服务]
        BIZ --> TA[租户切面AOP]
        TA --> SVC[Service层]
    end
    
    subgraph "数据层"
        SVC --> SI[SQL拦截器]
        SI --> DB[(数据库)]
    end
    
    subgraph "多租户模块"
        TC[租户上下文]
        TM[租户管理]
        UM[用户租户关系]
        PM[套餐管理]
        TC -.-> TI
        TC -.-> TA
        TC -.-> SI
        TM --> PM
        UM --> TC
    end
```

### 4.2 核心组件

| 组件 | 职责 | 位置 |
|------|------|------|
| 租户上下文 (TenantContext) | 管理当前线程的租户信息 | 公共模块 |
| 租户拦截器 (TenantInterceptor) | 处理HTTP请求中的租户信息 | 网关层 |
| 租户切面 (TenantAspect) | 管理服务层租户隔离级别 | 公共模块 |
| SQL拦截器 (TenantSqlInterceptor) | 自动添加租户过滤条件 | 公共模块 |
| 用户租户管理 (UserTenantService) | 管理用户与租户的关系 | 新增模块 |
| 套餐管理 (PackageService) | 管理租户套餐和计费 | 新增模块 |

### 4.3 数据流

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant G as 网关
    participant A as 认证服务
    participant B as 业务服务
    participant D as 数据库

    U->>F: 登录请求
    F->>G: 转发请求
    G->>A: 身份认证
    A->>A: 验证用户凭证
    A->>A: 查询用户租户关系
    A-->>F: 返回租户列表
    
    U->>F: 选择租户
    F->>G: 带租户ID的业务请求
    G->>G: 设置租户上下文
    G->>B: 转发业务请求
    B->>B: AOP处理租户隔离
    B->>D: SQL自动添加租户条件
    D-->>B: 返回租户数据
    B-->>F: 返回业务结果
```

---

## 5. 模块设计

### 5.1 新增模块：skyeye-tenant

#### 5.1.1 模块结构

```
skyeye-tenant/
├── tenant-core/              # 核心模块
│   ├── src/main/java/
│   │   └── com/skyeye/tenant/core/
│   │       ├── context/      # 租户上下文
│   │       ├── interceptor/  # 拦截器
│   │       ├── aspect/       # AOP切面
│   │       └── config/       # 配置类
│   └── pom.xml
├── tenant-manager/           # 管理模块
│   ├── src/main/java/
│   │   └── com/skyeye/tenant/manager/
│   │       ├── entity/       # 实体类
│   │       ├── service/      # 服务类
│   │       ├── controller/   # 控制器
│   │       └── dao/          # 数据访问
│   └── pom.xml
├── tenant-package/           # 套餐模块
│   ├── src/main/java/
│   │   └── com/skyeye/tenant/package/
│   │       ├── entity/       # 套餐实体
│   │       ├── service/      # 套餐服务
│   │       └── billing/      # 计费逻辑
│   └── pom.xml
└── pom.xml
```

#### 5.1.2 依赖关系

```xml
<!-- 父pom.xml -->
<modules>
    <module>tenant-core</module>
    <module>tenant-manager</module>
    <module>tenant-package</module>
</modules>

<!-- tenant-manager依赖tenant-core -->
<dependency>
    <groupId>com.skyeye</groupId>
    <artifactId>tenant-core</artifactId>
</dependency>

<!-- tenant-package依赖tenant-core -->
<dependency>
    <groupId>com.skyeye</groupId>
    <artifactId>tenant-core</artifactId>
</dependency>
```

### 5.2 核心组件设计

#### 5.2.1 租户上下文 (TenantContext)

```java
/**
 * 租户上下文管理器
 * 使用ThreadLocal确保线程安全
 */
public class TenantContext {
    
    private static final ThreadLocal<String> TENANT_ID = new ThreadLocal<>();
    private static final ThreadLocal<TenantEnum> ISOLATION_TYPE = new ThreadLocal<>();
    private static final ThreadLocal<Stack<TenantEnum>> ISOLATION_STACK = 
        ThreadLocal.withInitial(Stack::new);
    
    // 设置当前租户ID
    public static void setTenantId(String tenantId);
    
    // 获取当前租户ID
    public static String getTenantId();
    
    // 设置隔离级别
    public static void setIsolationType(TenantEnum isolationType);
    
    // 获取隔离级别
    public static TenantEnum getIsolationType();
    
    // 隔离级别栈操作（支持嵌套调用）
    public static void pushIsolationType(TenantEnum isolationType);
    public static TenantEnum popIsolationType();
    
    // 清理上下文
    public static void clear();
}
```

#### 5.2.2 SQL拦截器 (TenantSqlInterceptor)

```java
/**
 * MyBatis SQL拦截器
 * 自动为SQL语句添加租户过滤条件
 */
@Intercepts({
    @Signature(type = Executor.class, method = "update", args = {MappedStatement.class, Object.class}),
    @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class})
})
public class TenantSqlInterceptor implements Interceptor {
    
    // 需要忽略的表列表
    private static final Set<String> IGNORE_TABLES = Set.of(
        "tenant", "tenant_user", "sys_user", "sys_menu", "sys_role"
    );
    
    // 租户字段候选名称（按优先级排序）
    private static final List<String> TENANT_FIELD_CANDIDATES = List.of(
        "tenant_id", "tenant_code", "org_id", "organization_id"
    );
    
    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        // 1. 获取当前租户信息
        String tenantId = TenantContext.getTenantId();
        TenantEnum isolationType = TenantContext.getIsolationType();
        
        if (tenantId == null || isolationType == TenantEnum.NO_ISOLATION) {
            return invocation.proceed();
        }
        
        // 2. 解析SQL语句
        MappedStatement mappedStatement = (MappedStatement) invocation.getArgs()[0];
        String sql = getSql(mappedStatement, invocation.getArgs()[1]);
        
        // 3. 添加租户过滤条件
        String modifiedSql = addTenantFilter(sql, tenantId, isolationType);
        
        // 4. 执行修改后的SQL
        return executeWithModifiedSql(invocation, modifiedSql);
    }
    
    private String addTenantFilter(String sql, String tenantId, TenantEnum isolationType) {
        // 根据隔离级别构造不同的WHERE条件
        String tenantCondition = buildTenantCondition(tenantId, isolationType);
        
        // 解析SQL并添加条件
        return SqlParserUtils.addCondition(sql, tenantCondition);
    }
    
    private String buildTenantCondition(String tenantId, TenantEnum isolationType) {
        switch (isolationType) {
            case STRONG_ISOLATION:
                return "tenant_id = '" + tenantId + "'";
            case WEAK_ISOLATION:
                return "(tenant_id = '" + tenantId + "' OR tenant_id = '10000')";
            case PLATE:
                return "tenant_id = '10000'";
            default:
                return null;
        }
    }
}
```

#### 5.2.3 租户切面 (TenantAspect)

```java
/**
 * 租户AOP切面
 * 处理服务层方法的租户隔离级别
 */
@Aspect
@Component
public class TenantAspect {
    
    @Before("execution(* com.skyeye.*.service.impl.*.*(..)) || " +
            "execution(* com.skyeye.service.impl.*.*(..))")
    public void beforeServiceMethod(JoinPoint joinPoint) {
        // 1. 获取方法签名
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        Class<?> targetClass = joinPoint.getTarget().getClass();
        
        // 2. 确定租户隔离级别
        TenantEnum isolationType = determineIsolationType(method, targetClass);
        
        // 3. 设置上下文
        if (isolationType != null) {
            TenantContext.pushIsolationType(isolationType);
            TenantContext.setIsolationType(isolationType);
        }
    }
    
    @After("execution(* com.skyeye.*.service.impl.*.*(..)) || " +
           "execution(* com.skyeye.service.impl.*.*(..))")
    public void afterServiceMethod() {
        // 恢复上一级隔离级别
        TenantEnum previousType = TenantContext.popIsolationType();
        TenantContext.setIsolationType(previousType);
    }
    
    private TenantEnum determineIsolationType(Method method, Class<?> targetClass) {
        // 1. 方法级@IgnoreTenant
        if (method.isAnnotationPresent(IgnoreTenant.class)) {
            return TenantEnum.NO_ISOLATION;
        }
        
        // 2. 方法级@TenantIsolation
        TenantIsolation methodAnnotation = method.getAnnotation(TenantIsolation.class);
        if (methodAnnotation != null) {
            return methodAnnotation.value();
        }
        
        // 3. 类级注解
        TenantIsolation classAnnotation = targetClass.getAnnotation(TenantIsolation.class);
        if (classAnnotation != null) {
            return classAnnotation.value();
        }
        
        // 4. @SkyeyeService注解
        SkyeyeService serviceAnnotation = targetClass.getAnnotation(SkyeyeService.class);
        if (serviceAnnotation != null) {
            return serviceAnnotation.tenant();
        }
        
        // 5. 默认弱隔离
        return TenantEnum.WEAK_ISOLATION;
    }
}
```

#### 5.2.4 Web拦截器 (TenantInterceptor)

```java
/**
 * Web请求拦截器
 * 处理HTTP请求中的租户信息
 */
@Component
public class TenantInterceptor implements HandlerInterceptor {
    
    @Autowired
    private TenantService tenantService;
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, 
                           Object handler) throws Exception {
        
        // 1. 检查是否启用多租户
        if (!isTenantEnabled()) {
            return true;
        }
        
        // 2. 获取租户ID
        String tenantId = extractTenantId(request);
        if (tenantId == null) {
            return true;
        }
        
        // 3. 验证租户有效性
        if (!tenantService.isValidTenant(tenantId)) {
            throw new TenantException("无效的租户ID: " + tenantId);
        }
        
        // 4. 设置租户上下文
        TenantContext.setTenantId(tenantId);
        TenantContext.setIsolationType(TenantEnum.WEAK_ISOLATION);
        
        return true;
    }
    
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, 
                              Object handler, Exception ex) throws Exception {
        // 清理租户上下文
        TenantContext.clear();
    }
    
    private String extractTenantId(HttpServletRequest request) {
        // 1. 从Header中获取
        String tenantId = request.getHeader("X-Tenant-Id");
        if (tenantId != null) {
            return tenantId;
        }
        
        // 2. 从参数中获取
        tenantId = request.getParameter("tenantId");
        if (tenantId != null) {
            return tenantId;
        }
        
        // 3. 从Token中解析
        String token = request.getHeader("Authorization");
        if (token != null) {
            return parseTenan<|fim_suffix|>dFromToken(token);
        }
        
        return null;
    }
}
```

---

## 6. 数据隔离策略

### 6.1 隔离级别定义

| 隔离级别 | 描述 | SQL条件 | 使用场景 |
|----------|------|---------|----------|
| 强隔离 (STRONG_ISOLATION) | 只能访问当前租户数据 | `tenant_id = 'current_tenant'` | 业务数据、用户数据 |
| 弱隔离 (WEAK_ISOLATION) | 可访问当前租户和平台数据 | `tenant_id IN ('current_tenant', '10000')` | 基础数据、字典数据 |
| 无隔离 (NO_ISOLATION) | 不添加租户过滤条件 | 无 | 系统管理、跨租户功能 |
| 平台模式 (PLATE) | 只能访问平台数据 | `tenant_id = '10000'` | 平台管理功能 |

### 6.2 表分类策略

#### 6.2.1 租户表
包含租户字段的业务表，需要进行数据隔离：
- 用户表 (sys_user)
- 角色表 (sys_role) 
- 菜单权限表 (sys_menu_role)
- 业务数据表 (erp_*, crm_*, 等)

#### 6.2.2 共享表
不包含租户字段的系统表，所有租户共享：
- 系统菜单表 (sys_menu)
- 数据字典表 (sys_dict)
- 系统配置表 (sys_config)

#### 6.2.3 租户管理表
多租户功能专用表：
- 租户信息表 (tenant)
- 用户租户关系表 (tenant_user)
- 租户套餐表 (tenant_package)

### 6.3 租户字段策略

#### 6.3.1 字段命名规范
- 主要字段：`tenant_id` (VARCHAR(32))
- 备选字段：`tenant_code`, `org_id`, `organization_id`
- 索引策略：为租户字段创建联合索引，提升查询性能

#### 6.3.2 字段值规范
- 租户ID：32位UUID字符串
- 平台租户：固定值 "10000"
- 默认值：新增记录时自动设置为当前租户ID

### 6.4 SQL处理规则

#### 6.4.1 SELECT语句处理
```sql
-- 原始SQL
SELECT * FROM user WHERE status = 1

-- 强隔离处理后
SELECT * FROM user WHERE status = 1 AND tenant_id = 'tenant_123'

-- 弱隔离处理后  
SELECT * FROM user WHERE status = 1 AND tenant_id IN ('tenant_123', '10000')
```

#### 6.4.2 INSERT语句处理
```sql
-- 原始SQL
INSERT INTO user (name, email) VALUES ('张三', '<EMAIL>')

-- 处理后
INSERT INTO user (name, email, tenant_id) VALUES ('张三', '<EMAIL>', 'tenant_123')
```

#### 6.4.3 UPDATE语句处理
```sql
-- 原始SQL
UPDATE user SET email = '<EMAIL>' WHERE id = 1

-- 处理后
UPDATE user SET email = '<EMAIL>' WHERE id = 1 AND tenant_id = 'tenant_123'
```

#### 6.4.4 DELETE语句处理
```sql
-- 原始SQL
DELETE FROM user WHERE id = 1

-- 处理后
DELETE FROM user WHERE id = 1 AND tenant_id = 'tenant_123'
```

---

## 7. 用户身份管理与租户切换

### 7.1 用户-租户关系模型

#### 7.1.1 数据模型设计

```sql
-- 用户租户关系表
CREATE TABLE tenant_user (
    id VARCHAR(32) PRIMARY KEY,
    user_id VARCHAR(32) NOT NULL COMMENT '用户ID',
    tenant_id VARCHAR(32) NOT NULL COMMENT '租户ID', 
    role_type TINYINT DEFAULT 1 COMMENT '角色类型：1-普通用户 2-管理员 3-超级管理员',
    status TINYINT DEFAULT 1 COMMENT '状态：1-正常 2-禁用',
    join_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '加入时间',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_user_tenant (user_id, tenant_id),
    INDEX idx_user_id (user_id),
    INDEX idx_tenant_id (tenant_id)
) COMMENT '用户租户关系表';

-- 租户信息扩展表
CREATE TABLE tenant_info (
    id VARCHAR(32) PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '租户名称',
    code VARCHAR(50) UNIQUE NOT NULL COMMENT '租户编码',
    logo VARCHAR(200) COMMENT '租户LOGO',
    description TEXT COMMENT '租户描述',
    contact_name VARCHAR(50) COMMENT '联系人姓名',
    contact_phone VARCHAR(20) COMMENT '联系人电话',
    contact_email VARCHAR(100) COMMENT '联系人邮箱',
    max_users INT DEFAULT 100 COMMENT '最大用户数',
    expire_time DATETIME COMMENT '到期时间',
    status TINYINT DEFAULT 1 COMMENT '状态：1-正常 2-禁用 3-过期',
    package_id VARCHAR(32) COMMENT '套餐ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) COMMENT '租户信息表';
```

#### 7.1.2 关系模型

```mermaid
erDiagram
    USER ||--o{ TENANT_USER : has
    TENANT ||--o{ TENANT_USER : contains
    TENANT ||--|| TENANT_PACKAGE : subscribes
    
    USER {
        string id PK
        string username
        string email
        string password
    }
    
    TENANT_USER {
        string id PK
        string user_id FK
        string tenant_id FK
        int role_type
        int status
        datetime join_time
    }
    
    TENANT {
        string id PK
        string name
        string code
        string logo
        int max_users
        datetime expire_time
        int status
    }
    
    TENANT_PACKAGE {
        string id PK
        string name
        int type
        decimal price
        int max_users
        int duration
    }
```

### 7.2 身份认证流程

#### 7.2.1 登录认证流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant A as 认证服务
    participant T as 租户服务
    participant D as 数据库

    U->>F: 1. 输入用户名密码
    F->>A: 2. 提交登录请求
    A->>D: 3. 验证用户凭证
    D-->>A: 4. 返回用户信息
    
    alt 认证成功
        A->>T: 5. 查询用户租户列表
        T->>D: 6. 查询tenant_user表
        D-->>T: 7. 返回租户关系列表
        T-->>A: 8. 返回租户信息
        A-->>F: 9. 返回认证token + 租户列表
        
        alt 用户只有一个租户
            F->>F: 10a. 自动选择租户
            F->>A: 11a. 请求业务token
        else 用户有多个租户
            F->>U: 10b. 显示租户选择界面
            U->>F: 11b. 选择目标租户
            F->>A: 12b. 请求业务token
        end
        
        A->>A: 13. 生成包含租户信息的业务token
        A-->>F: 14. 返回业务token
        F->>F: 15. 存储token，进入系统
        
    else 认证失败
        A-->>F: 返回错误信息
        F-->>U: 显示登录失败
    end
```

#### 7.2.2 租户切换流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant A as 认证服务
    participant T as 租户服务

    U->>F: 1. 点击切换租户
    F->>F: 2. 显示可用租户列表
    U->>F: 3. 选择目标租户
    F->>A: 4. 请求切换租户(旧token + 新租户ID)
    
    A->>A: 5. 验证token有效性
    A->>T: 6. 验证用户对目标租户的权限
    T-->>A: 7. 返回权限验证结果
    
    alt 权限验证通过
        A->>A: 8. 生成新的业务token
        A-->>F: 9. 返回新token
        F->>F: 10. 更新本地token
        F->>F: 11. 刷新页面数据
        F-->>U: 12. 切换成功
    else 权限验证失败
        A-->>F: 返回权限错误
        F-->>U: 显示切换失败信息
    end
```

### 7.3 Token设计

#### 7.3.1 Token结构

```json
{
  "header": {
    "typ": "JWT",
    "alg": "HS256"
  },
  "payload": {
    "sub": "用户ID",
    "username": "用户名",
    "tenant_id": "当前租户ID",
    "tenant_name": "租户名称",
    "tenant_role": "租户内角色",
    "permissions": ["权限列表"],
    "iat": 1640995200,
    "exp": 1641081600
  },
  "signature": "签名"
}
```

#### 7.3.2 Token管理策略

- **短期Token**：2小时有效期，包含具体租户信息
- **刷新Token**：30天有效期，用于刷新短期Token
- **租户切换**：生成新的短期Token，保持刷新Token不变
- **安全策略**：Token包含租户ID，服务端验证租户权限

### 7.4 前端实现

#### 7.4.1 租户选择组件

```vue
<template>
  <div class="tenant-selector">
    <!-- 登录后租户选择 -->
    <div v-if="showTenantSelection" class="tenant-selection">
      <h3>请选择要进入的企业</h3>
      <div class="tenant-list">
        <div 
          v-for="tenant in availableTenants" 
          :key="tenant.id"
          class="tenant-item"
          @click="selectTenant(tenant)"
        >
          <img :src="tenant.logo" :alt="tenant.name" class="tenant-logo">
          <div class="tenant-info">
            <h4>{{ tenant.name }}</h4>
            <p>{{ tenant.description }}</p>
            <span class="tenant-role">{{ tenant.role }}</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 顶部租户切换 -->
    <div class="tenant-switcher">
      <el-dropdown @command="switchTenant">
        <span class="tenant-name">
          {{ currentTenant.name }}
          <i class="el-icon-arrow-down"></i>
        </span>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item 
            v-for="tenant in availableTenants"
            :key="tenant.id"
            :command="tenant.id"
          >
            {{ tenant.name }}
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TenantSelector',
  data() {
    return {
      showTenantSelection: false,
      availableTenants: [],
      currentTenant: {}
    }
  },
  methods: {
    async selectTenant(tenant) {
      const response = await this.$api.auth.selectTenant(tenant.id)
      if (response.success) {
        this.$store.dispatch('auth/setToken', response.data.token)
        this.$store.dispatch('tenant/setCurrentTenant', tenant)
        this.$router.push('/dashboard')
      }
    },
    
    async switchTenant(tenantId) {
      const response = await this.$api.auth.switchTenant(tenantId)
      if (response.success) {
        this.$store.dispatch('auth/setToken', response.data.token)
        const tenant = this.availableTenants.find(t => t.id === tenantId)
        this.$store.dispatch('tenant/setCurrentTenant', tenant)
        // 刷新当前页面数据
        this.$router.go(0)
      }
    }
  }
}
</script>
```

#### 7.4.2 状态管理

```javascript
// store/modules/tenant.js
export default {
  namespaced: true,
  state: {
    currentTenant: null,
    availableTenants: [],
    tenantPermissions: []
  },
  
  mutations: {
    SET_CURRENT_TENANT(state, tenant) {
      state.currentTenant = tenant
      localStorage.setItem('currentTenant', JSON.stringify(tenant))
    },
    
    SET_AVAILABLE_TENANTS(state, tenants) {
      state.availableTenants = tenants
    },
    
    SET_TENANT_PERMISSIONS(state, permissions) {
      state.tenantPermissions = permissions
    }
  },
  
  actions: {
    setCurrentTenant({ commit }, tenant) {
      commit('SET_CURRENT_TENANT', tenant)
    },
    
    async loadAvailableTenants({ commit }) {
      const response = await api.tenant.getAvailableTenants()
      if (response.success) {
        commit('SET_AVAILABLE_TENANTS', response.data)
      }
    },
    
    async switchTenant({ commit }, tenantId) {
      const response = await api.auth.switchTenant(tenantId)
      if (response.success) {
        // 更新token
        commit('auth/SET_TOKEN', response.data.token, { root: true })
        
        // 更新当前租户
        const tenant = this.state.availableTenants.find(t => t.id === tenantId)
        commit('SET_CURRENT_TENANT', tenant)
        
        return response.data.token
      }
      throw new Error(response.message)
    }
  }
}
```

---

## 8. 租户套餐收费模式

### 8.1 套餐模式定义

#### 8.1.1 三种收费模式

| 收费模式 | 描述 | 计费规则 | 适用场景 |
|----------|------|----------|----------|
| 买断制 | 一次性付费，永久使用 | 固定价格 + 用户数上限 | 中大型企业，稳定用户规模 |
| 员工数量制 | 按实际用户数量月租 | 用户数 × 单价 × 时长 | 用户数量变化较大的企业 |
| 企业套餐制 | 固定套餐月租 | 套餐价格 × 时长 | 中小型企业，用户数量相对稳定 |

#### 8.1.2 数据模型设计

```sql
-- 套餐定义表
CREATE TABLE tenant_package (
    id VARCHAR(32) PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '套餐名称',
    code VARCHAR(50) UNIQUE NOT NULL COMMENT '套餐编码',
    type TINYINT NOT NULL COMMENT '套餐类型：1-买断制 2-员工数量制 3-企业套餐制',
    description TEXT COMMENT '套餐描述',
    
    -- 买断制字段
    buyout_price DECIMAL(10,2) COMMENT '买断价格',
    max_users INT COMMENT '最大用户数',
    
    -- 员工数量制字段
    price_per_user DECIMAL(8,2) COMMENT '每用户每月价格',
    min_users INT DEFAULT 1 COMMENT '最小用户数',
    
    -- 企业套餐制字段
    monthly_price DECIMAL(10,2) COMMENT '月费价格',
    included_users INT COMMENT '包含用户数',
    extra_user_price DECIMAL(8,2) COMMENT '超出用户单价',
    
    -- 通用字段
    features JSON COMMENT '功能特性配置',
    status TINYINT DEFAULT 1 COMMENT '状态：1-启用 2-禁用',
    sort_order INT DEFAULT 0 COMMENT '排序',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) COMMENT '租户套餐表';

-- 租户订阅表
CREATE TABLE tenant_subscription (
    id VARCHAR(32) PRIMARY KEY,
    tenant_id VARCHAR(32) NOT NULL COMMENT '租户ID',
    package_id VARCHAR(32) NOT NULL COMMENT '套餐ID',
    start_time DATETIME NOT NULL COMMENT '开始时间',
    end_time DATETIME COMMENT '结束时间',
    status TINYINT DEFAULT 1 COMMENT '状态：1-正常 2-过期 3-暂停',
    
    -- 计费信息
    current_users INT DEFAULT 0 COMMENT '当前用户数',
    max_users INT COMMENT '最大用户数限制',
    last_bill_time DATETIME COMMENT '上次计费时间',
    next_bill_time DATETIME COMMENT '下次计费时间',
    
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_package_id (package_id),
    INDEX idx_status (status)
) COMMENT '租户订阅表';

-- 计费记录表
CREATE TABLE tenant_billing (
    id VARCHAR(32) PRIMARY KEY,
    tenant_id VARCHAR(32) NOT NULL COMMENT '租户ID',
    subscription_id VARCHAR(32) NOT NULL COMMENT '订阅ID',
    bill_period_start DATETIME NOT NULL COMMENT '计费周期开始',
    bill_period_end DATETIME NOT NULL COMMENT '计费周期结束',
    
    -- 计费详情
    user_count INT NOT NULL COMMENT '计费用户数',
    unit_price DECIMAL(8,2) NOT NULL COMMENT '单价',
    total_amount DECIMAL(10,2) NOT NULL COMMENT '总金额',
    discount_amount DECIMAL(10,2) DEFAULT 0 COMMENT '优惠金额',
    final_amount DECIMAL(10,2) NOT NULL COMMENT '最终金额',
    
    billing_type TINYINT NOT NULL COMMENT '计费类型：1-月租 2-年费 3-买断',
    status TINYINT DEFAULT 1 COMMENT '状态：1-待支付 2-已支付 3-已取消',
    pay_time DATETIME COMMENT '支付时间',
    
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_subscription_id (subscription_id),
    INDEX idx_bill_period (bill_period_start, bill_period_end)
) COMMENT '租户计费记录表';
```

### 8.2 套餐配置示例

#### 8.2.1 买断制套餐

```json
{
  "id": "pkg_buyout_001",
  "name": "企业买断版",
  "code": "ENTERPRISE_BUYOUT",
  "type": 1,
  "description": "一次性付费，永久使用，适合大型企业",
  "buyout_price": 59999.00,
  "max_users": 9999,
  "features": {
    "modules": ["erp", "crm", "hrm", "finance"],
    "storage": "unlimited",
    "api_calls": "unlimited",
    "support": "premium"
  }
}
```

#### 8.2.2 员工数量制套餐

```json
{
  "id": "pkg_peruser_001", 
  "name": "按人按月计费",
  "code": "PER_USER_MONTHLY",
  "type": 2,
  "description": "每个用户每月100元，灵活计费",
  "price_per_user": 100.00,
  "min_users": 1,
  "features": {
    "modules": ["erp", "crm"],
    "storage": "100GB",
    "api_calls": "10000/month",
    "support": "standard"
  }
}
```

#### 8.2.3 企业套餐制

```json
{
  "id": "pkg_enterprise_001",
  "name": "标准企业版",
  "code": "STANDARD_ENTERPRISE", 
  "type": 3,
  "description": "月费599元，包含10个用户，超出按50元/人计费",
  "monthly_price": 599.00,
  "included_users": 10,
  "extra_user_price": 50.00,
  "features": {
    "modules": ["erp", "crm"],
    "storage": "500GB", 
    "api_calls": "50000/month",
    "support": "standard"
  }
}
```

### 8.3 计费逻辑实现

#### 8.3.1 计费服务接口

```java
/**
 * 租户计费服务
 */
public interface TenantBillingService {
    
    /**
     * 创建订阅
     */
    TenantSubscription createSubscription(String tenantId, String packageId, 
                                        SubscriptionRequest request);
    
    /**
     * 计算月度费用
     */
    BillingAmount calculateMonthlyBilling(String tenantId, YearMonth billingMonth);
    
    /**
     * 生成账单
     */
    TenantBilling generateBill(String tenantId, YearMonth billingMonth);
    
    /**
     * 处理用户数量变化
     */
    void handleUserCountChange(String tenantId, int newUserCount);
    
    /**
     * 检查租户状态
     */
    TenantStatus checkTenantStatus(String tenantId);
}

/**
 * 计费金额计算结果
 */
@Data
public class BillingAmount {
    private String tenantId;
    private YearMonth billingMonth;
    private int userCount;
    private BigDecimal unitPrice;
    private BigDecimal totalAmount;
    private BigDecimal discountAmount;
    private BigDecimal finalAmount;
    private String packageType;
    private Map<String, Object> details;
}
```

#### 8.3.2 计费策略实现

```java
/**
 * 计费策略接口
 */
public interface BillingStrategy {
    BillingAmount calculate(TenantSubscription subscription, YearMonth billingMonth);
    boolean supports(int packageType);
}

/**
 * 买断制计费策略
 */
@Component
public class BuyoutBillingStrategy implements BillingStrategy {
    
    @Override
    public BillingAmount calculate(TenantSubscription subscription, YearMonth billingMonth) {
        BillingAmount amount = new BillingAmount();
        amount.setTenantId(subscription.getTenantId());
        amount.setBillingMonth(billingMonth);
        
        // 买断制不产生月度费用
        amount.setUserCount(subscription.getCurrentUsers());
        amount.setUnitPrice(BigDecimal.ZERO);
        amount.setTotalAmount(BigDecimal.ZERO);
        amount.setFinalAmount(BigDecimal.ZERO);
        amount.setPackageType("BUYOUT");
        
        return amount;
    }
    
    @Override
    public boolean supports(int packageType) {
        return packageType == 1; // 买断制
    }
}

/**
 * 员工数量制计费策略
 */
@Component
public class PerUserBillingStrategy implements BillingStrategy {
    
    @Override
    public BillingAmount calculate(TenantSubscription subscription, YearMonth billingMonth) {
        TenantPackage tenantPackage = packageService.getById(subscription.getPackageId());
        
        BillingAmount amount = new BillingAmount();
        amount.setTenantId(subscription.getTenantId());
        amount.setBillingMonth(billingMonth);
        amount.setUserCount(subscription.getCurrentUsers());
        amount.setUnitPrice(tenantPackage.getPricePerUser());
        
        // 计算总金额 = 用户数 × 单价
        BigDecimal totalAmount = tenantPackage.getPricePerUser()
            .multiply(new BigDecimal(subscription.getCurrentUsers()));
        amount.setTotalAmount(totalAmount);
        
        // 应用折扣
        BigDecimal discountAmount = calculateDiscount(subscription, totalAmount);
        amount.setDiscountAmount(discountAmount);
        amount.setFinalAmount(totalAmount.subtract(discountAmount));
        amount.setPackageType("PER_USER");
        
        return amount;
    }
    
    @Override
    public boolean supports(int packageType) {
        return packageType == 2; // 员工数量制
    }
}

/**
 * 企业套餐制计费策略
 */
@Component
public class EnterpriseBillingStrategy implements BillingStrategy {
    
    @Override
    public BillingAmount calculate(TenantSubscription subscription, YearMonth billingMonth) {
        TenantPackage tenantPackage = packageService.getById(subscription.getPackageId());
        
        BillingAmount amount = new BillingAmount();
        amount.setTenantId(subscription.getTenantId());
        amount.setBillingMonth(billingMonth);
        amount.setUserCount(subscription.getCurrentUsers());
        
        // 基础套餐费用
        BigDecimal baseAmount = tenantPackage.getMonthlyPrice();
        
        // 超出用户费用
        int extraUsers = Math.max(0, subscription.getCurrentUsers() - tenantPackage.getIncludedUsers());
        BigDecimal extraAmount = tenantPackage.getExtraUserPrice()
            .multiply(new BigDecimal(extraUsers));
        
        BigDecimal totalAmount = baseAmount.add(extraAmount);
        amount.setTotalAmount(totalAmount);
        
        // 应用折扣
        BigDecimal discountAmount = calculateDiscount(subscription, totalAmount);
        amount.setDiscountAmount(discountAmount);
        amount.setFinalAmount(totalAmount.subtract(discountAmount));
        amount.setPackageType("ENTERPRISE");
        
        // 记录详细信息
        Map<String, Object> details = new HashMap<>();
        details.put("baseAmount", baseAmount);
        details.put("extraUsers", extraUsers);
        details.put("extraAmount", extraAmount);
        details.put("includedUsers", tenantPackage.getIncludedUsers());
        amount.setDetails(details);
        
        return amount;
    }
    
    @Override
    public boolean supports(int packageType) {
        return packageType == 3; // 企业套餐制
    }
}
```

#### 8.3.3 计费服务实现

```java
/**
 * 租户计费服务实现
 */
@Service
public class TenantBillingServiceImpl implements TenantBillingService {
    
    @Autowired
    private List<BillingStrategy> billingStrategies;
    
    @Autowired
    private TenantSubscriptionService subscriptionService;
    
    @Autowired
    private TenantBillingDao billingDao;
    
    @Override
    public BillingAmount calculateMonthlyBilling(String tenantId, YearMonth billingMonth) {
        // 获取租户订阅信息
        TenantSubscription subscription = subscriptionService.getActiveSubscription(tenantId);
        if (subscription == null) {
            throw new BillingException("租户没有有效订阅");
        }
        
        // 获取套餐信息
        TenantPackage tenantPackage = packageService.getById(subscription.getPackageId());
        
        // 选择计费策略
        BillingStrategy strategy = billingStrategies.stream()
            .filter(s -> s.supports(tenantPackage.getType()))
            .findFirst()
            .orElseThrow(() -> new BillingException("不支持的套餐类型"));
        
        // 计算费用
        return strategy.calculate(subscription, billingMonth);
    }
    
    @Override
    public TenantBilling generateBill(String tenantId, YearMonth billingMonth) {
        // 检查是否已生成账单
        TenantBilling existingBill = billingDao.getBillByTenantAndMonth(tenantId, billingMonth);
        if (existingBill != null) {
            return existingBill;
        }
        
        // 计算费用
        BillingAmount amount = calculateMonthlyBilling(tenantId, billingMonth);
        
        // 创建账单
        TenantBilling bill = new TenantBilling();
        bill.setId(IdUtil.fastSimpleUUID());
        bill.setTenantId(tenantId);
        bill.setBillPeriodStart(billingMonth.atDay(1).atStartOfDay());
        bill.setBillPeriodEnd(billingMonth.atEndOfMonth().atTime(23, 59, 59));
        bill.setUserCount(amount.getUserCount());
        bill.setUnitPrice(amount.getUnitPrice());
        bill.setTotalAmount(amount.getTotalAmount());
        bill.setDiscountAmount(amount.getDiscountAmount());
        bill.setFinalAmount(amount.getFinalAmount());
        bill.setBillingType(1); // 月租
        bill.setStatus(1); // 待支付
        
        billingDao.insert(bill);
        return bill;
    }
    
    @Override
    public void handleUserCountChange(String tenantId, int newUserCount) {
        TenantSubscription subscription = subscriptionService.getActiveSubscription(tenantId);
        if (subscription == null) {
            return;
        }
        
        // 检查用户数限制
        if (subscription.getMaxUsers() != null && newUserCount > subscription.getMaxUsers()) {
            throw new BillingException("用户数超出套餐限制");
        }
        
        // 更新用户数
        subscription.setCurrentUsers(newUserCount);
        subscriptionService.updateById(subscription);
        
        // 如果是按用户计费，可能需要生成补差账单
        TenantPackage tenantPackage = packageService.getById(subscription.getPackageId());
        if (tenantPackage.getType() == 2) { // 员工数量制
            handleUserCountChangeForPerUserBilling(subscription, newUserCount);
        }
    }
    
    @Scheduled(cron = "0 0 1 * * ?") // 每月1号执行
    public void generateMonthlyBills() {
        YearMonth lastMonth = YearMonth.now().minusMonths(1);
        
        List<TenantSubscription> activeSubscriptions = subscriptionService.getActiveSubscriptions();
        for (TenantSubscription subscription : activeSubscriptions) {
            try {
                generateBill(subscription.getTenantId(), lastMonth);
            } catch (Exception e) {
                log.error("生成租户账单失败: {}", subscription.getTenantId(), e);
            }
        }
    }
}
```

### 8.4 套餐管理界面

#### 8.4.1 套餐配置界面

```vue
<template>
  <div class="package-management">
    <el-card>
      <div slot="header">
        <span>套餐管理</span>
        <el-button style="float: right;" type="primary" @click="addPackage">
          新增套餐
        </el-button>
      </div>
      
      <el-table :data="packages" v-loading="loading">
        <el-table-column prop="name" label="套餐名称" width="150"/>
        <el-table-column prop="type" label="计费模式" width="120">
          <template slot-scope="scope">
            <el-tag :type="getPackageTypeTag(scope.row.type)">
              {{ getPackageTypeName(scope.row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="价格配置" width="200">
          <template slot-scope="scope">
            <div v-if="scope.row.type === 1">
              买断价：¥{{ scope.row.buyout_price }}
            </div>
            <div v-else-if="scope.row.type === 2">
              ¥{{ scope.row.price_per_user }}/人/月
            </div>
            <div v-else-if="scope.row.type === 3">
              ¥{{ scope.row.monthly_price }}/月
              (含{{ scope.row.included_users }}人)
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="max_users" label="用户限制" width="100"/>
        <el-table-column label="功能特性" width="200">
          <template slot-scope="scope">
            <el-tag 
              v-for="module in scope.row.features.modules" 
              :key="module"
              size="mini"
              style="margin-right: 5px;"
            >
              {{ module }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template slot-scope="scope">
            <el-button size="mini" @click="editPackage(scope.row)">编辑</el-button>
            <el-button size="mini" type="danger" @click="deletePackage(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    
    <!-- 套餐编辑对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="600px">
      <el-form :model="packageForm" :rules="packageRules" ref="packageForm">
        <el-form-item label="套餐名称" prop="name">
          <el-input v-model="packageForm.name"/>
        </el-form-item>
        
        <el-form-item label="计费模式" prop="type">
          <el-select v-model="packageForm.type" @change="onPackageTypeChange">
            <el-option label="买断制" :value="1"/>
            <el-option label="员工数量制" :value="2"/>
            <el-option label="企业套餐制" :value="3"/>
          </el-select>
        </el-form-item>
        
        <!-- 买断制配置 -->
        <div v-if="packageForm.type === 1">
          <el-form-item label="买断价格" prop="buyout_price">
            <el-input-number v-model="packageForm.buyout_price" :precision="2"/>
          </el-form-item>
          <el-form-item label="最大用户数" prop="max_users">
            <el-input-number v-model="packageForm.max_users"/>
          </el-form-item>
        </div>
        
        <!-- 员工数量制配置 -->
        <div v-if="packageForm.type === 2">
          <el-form-item label="每人每月价格" prop="price_per_user">
            <el-input-number v-model="packageForm.price_per_user" :precision="2"/>
          </el-form-item>
          <el-form-item label="最小用户数" prop="min_users">
            <el-input-number v-model="packageForm.min_users"/>
          </el-form-item>
        </div>
        
        <!-- 企业套餐制配置 -->
        <div v-if="packageForm.type === 3">
          <el-form-item label="月费价格" prop="monthly_price">
            <el-input-number v-model="packageForm.monthly_price" :precision="2"/>
          </el-form-item>
          <el-form-item label="包含用户数" prop="included_users">
            <el-input-number v-model="packageForm.included_users"/>
          </el-form-item>
          <el-form-item label="超出用户单价" prop="extra_user_price">
            <el-input-number v-model="packageForm.extra_user_price" :precision="2"/>
          </el-form-item>
        </div>
        
        <el-form-item label="功能模块">
          <el-checkbox-group v-model="packageForm.features.modules">
            <el-checkbox label="erp">ERP系统</el-checkbox>
            <el-checkbox label="crm">CRM系统</el-checkbox>
            <el-checkbox label="hrm">人力资源</el-checkbox>
            <el-checkbox label="finance">财务管理</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      
      <div slot="footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="savePackage">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'PackageManagement',
  data() {
    return {
      packages: [],
      loading: false,
      dialogVisible: false,
      dialogTitle: '新增套餐',
      packageForm: {
        name: '',
        type: 1,
        features: {
          modules: []
        }
      },
      packageRules: {
        name: [{ required: true, message: '请输入套餐名称', trigger: 'blur' }],
        type: [{ required: true, message: '请选择计费模式', trigger: 'change' }]
      }
    }
  },
  
  methods: {
    getPackageTypeName(type) {
      const names = { 1: '买断制', 2: '员工数量制', 3: '企业套餐制' }
      return names[type] || '未知'
    },
    
    getPackageTypeTag(type) {
      const tags = { 1: 'success', 2: 'warning', 3: 'info' }
      return tags[type] || 'info'
    },
    
    async loadPackages() {
      this.loading = true
      try {
        const response = await this.$api.tenant.getPackages()
        this.packages = response.data
      } finally {
        this.loading = false
      }
    },
    
    addPackage() {
      this.dialogTitle = '新增套餐'
      this.packageForm = {
        name: '',
        type: 1,
        features: { modules: [] }
      }
      this.dialogVisible = true
    },
    
    editPackage(row) {
      this.dialogTitle = '编辑套餐'
      this.packageForm = { ...row }
      this.dialogVisible = true
    },
    
    async savePackage() {
      this.$refs.packageForm.validate(async (valid) => {
        if (valid) {
          if (this.packageForm.id) {
            await this.$api.tenant.updatePackage(this.packageForm)
          } else {
            await this.$api.tenant.createPackage(this.packageForm)
          }
          this.dialogVisible = false
          this.loadPackages()
        }
      })
    },
    
    onPackageTypeChange() {
      // 清空类型相关的字段
      this.packageForm.buyout_price = null
      this.packageForm.max_users = null
      this.packageForm.price_per_user = null
      this.packageForm.min_users = null
      this.packageForm.monthly_price = null
      this.packageForm.included_users = null
      this.packageForm.extra_user_price = null
    }
  },
  
  mounted() {
    this.loadPackages()
  }
}
</script>
```

---

## 9. 安全策略

### 9.1 数据安全

#### 9.1.1 租户数据隔离
- **强制隔离**：每个SQL查询自动添加租户过滤条件
- **双重验证**：服务层和数据层都进行租户权限检查
- **防止越权**：严格验证用户对租户的访问权限

#### 9.1.2 敏感数据保护
- **数据脱敏**：在日志和错误信息中脱敏租户标识
- **加密存储**：敏感配置信息加密存储
- **审计日志**：记录所有租户相关操作

### 9.2 身份认证安全

#### 9.2.1 Token安全
- **短生命周期**：Token有效期限制在2小时内
- **刷新机制**：使用刷新Token延长会话
- **签名验证**：使用强签名算法防止Token伪造

#### 9.2.2 租户切换安全
- **权限验证**：切换前验证用户对目标租户的权限
- **会话隔离**：切换租户后清空前一个租户的会话数据
- **操作审计**：记录所有租户切换操作

### 9.3 访问控制

#### 9.3.1 接口级权限控制
```java
@RestController
@RequestMapping("/api/tenant")
public class TenantController {
    
    @PreAuthorize("@tenantSecurityService.hasPermission('TENANT_SWITCH')")
    @PostMapping("/switch")
    public ResponseEntity<SwitchResult> switchTenant(@RequestBody SwitchRequest request) {
        // 租户切换逻辑
    }
    
    @PreAuthorize("@tenantSecurityService.isTenantAdmin()")
    @PostMapping("/users")
    public ResponseEntity<List<User>> getTenantUsers() {
        // 获取租户用户列表
    }
}

@Service
public class TenantSecurityService {
    
    public boolean hasPermission(String permission) {
        // 检查当前用户是否有指定权限
        UserDetails user = getCurrentUser();
        return user.getAuthorities().stream()
            .anyMatch(auth -> auth.getAuthority().equals(permission));
    }
    
    public boolean isTenantAdmin() {
        // 检查当前用户是否为租户管理员
        String tenantId = TenantContext.getTenantId();
        String userId = getCurrentUserId();
        
        TenantUser tenantUser = tenantUserService.getTenantUser(userId, tenantId);
        return tenantUser != null && tenantUser.getRoleType() >= 2;
    }
    
    public boolean canAccessTenant(String tenantId) {
        // 检查用户是否可以访问指定租户
        String userId = getCurrentUserId();
        return tenantUserService.hasAccess(userId, tenantId);
    }
}
```

#### 9.3.2 数据级权限控制
```java
@Service
public class TenantDataPermissionService {
    
    /**
     * 检查用户对数据的访问权限
     */
    public boolean checkDataAccess(String userId, String tenantId, String dataId, String operation) {
        // 1. 检查用户租户权限
        if (!hasUserTenantAccess(userId, tenantId)) {
            return false;
        }
        
        // 2. 检查数据所属租户
        if (!isDataBelongsToTenant(dataId, tenantId)) {
            return false;
        }
        
        // 3. 检查操作权限
        return hasOperationPermission(userId, tenantId, operation);
    }
    
    /**
     * 过滤用户可访问的数据
     */
    public <T> List<T> filterAccessibleData(String userId, String tenantId, List<T> dataList) {
        return dataList.stream()
            .filter(data -> checkDataAccess(userId, tenantId, getDataId(data), "read"))
            .collect(Collectors.toList());
    }
}
```

### 9.4 监控与审计

#### 9.4.1 操作审计
```java
@Component
public class TenantAuditLogger {
    
    private final Logger auditLogger = LoggerFactory.getLogger("TENANT_AUDIT");
    
    public void logTenantOperation(String operation, String tenantId, String userId, 
                                 String details, boolean success) {
        AuditLog log = AuditLog.builder()
            .timestamp(LocalDateTime.now())
            .operation(operation)
            .tenantId(tenantId)
            .userId(userId)
            .details(details)
            .success(success)
            .ipAddress(getClientIpAddress())
            .userAgent(getUserAgent())
            .build();
            
        auditLogger.info(JsonUtils.toJson(log));
    }
    
    @EventListener
    public void handleTenantSwitchEvent(TenantSwitchEvent event) {
        logTenantOperation("TENANT_SWITCH", 
            event.getNewTenantId(), 
            event.getUserId(),
            String.format("从租户%s切换到租户%s", event.getOldTenantId(), event.getNewTenantId()),
            event.isSuccess());
    }
}
```

#### 9.4.2 安全监控
```java
@Component
public class TenantSecurityMonitor {
    
    private final AtomicLong failedLoginAttempts = new AtomicLong(0);
    private final Map<String, AtomicLong> tenantAccessCounts = new ConcurrentHashMap<>();
    
    @EventListener
    public void handleFailedLogin(FailedLoginEvent event) {
        failedLoginAttempts.incrementAndGet();
        
        // 如果失败次数过多，触发安全告警
        if (failedLoginAttempts.get() > 100) {
            securityAlertService.sendAlert("频繁登录失败", 
                "检测到大量登录失败，可能存在暴力破解攻击");
        }
    }
    
    @EventListener  
    public void handleTenantAccess(TenantAccessEvent event) {
        String tenantId = event.getTenantId();
        tenantAccessCounts.computeIfAbsent(tenantId, k -> new AtomicLong(0))
            .incrementAndGet();
            
        // 检查异常访问模式
        checkAbnormalAccessPattern(tenantId);
    }
    
    @Scheduled(fixedRate = 60000) // 每分钟检查一次
    public void checkSecurityMetrics() {
        // 检查各种安全指标
        checkUnauthorizedAccess();
        checkSuspiciousDataAccess();
        checkAbnormalTenantSwitching();
    }
}
```

---

## 10. 实施方案

### 10.1 实施阶段

#### 10.1.1 第一阶段：核心组件开发（2周）
- [ ] 创建 skyeye-tenant 模块
- [ ] 实现 TenantContext 租户上下文
- [ ] 实现 TenantSqlInterceptor SQL拦截器
- [ ] 实现 TenantAspect AOP切面
- [ ] 实现 TenantInterceptor Web拦截器
- [ ] 单元测试和集成测试

#### 10.1.2 第二阶段：用户租户管理（1周）  
- [ ] 设计并创建租户相关数据表
- [ ] 实现用户租户关系管理
- [ ] 实现租户切换功能
- [ ] 前端租户选择和切换界面
- [ ] 身份认证流程改造

#### 10.1.3 第三阶段：套餐计费系统（2周）
- [ ] 设计套餐和计费数据模型
- [ ] 实现三种计费策略
- [ ] 实现套餐管理功能
- [ ] 实现计费任务和账单生成
- [ ] 套餐管理后台界面

#### 10.1.4 第四阶段：安全和监控（1周）
- [ ] 实现安全权限控制
- [ ] 实现操作审计日志
- [ ] 实现安全监控告警
- [ ] 性能优化和压力测试

#### 10.1.5 第五阶段：系统集成和测试（1周）
- [ ] 与现有模块集成测试
- [ ] 端到端功能测试
- [ ] 性能测试和优化
- [ ] 文档完善和培训

### 10.2 技术风险与应对

#### 10.2.1 主要技术风险

| 风险 | 影响 | 概率 | 应对措施 |
|------|------|------|----------|
| SQL拦截器性能问题 | 系统响应变慢 | 中 | 优化SQL解析逻辑，增加缓存机制 |
| 现有代码兼容性 | 功能异常 | 低 | 充分测试，制定回滚方案 |
| 数据迁移复杂 | 数据丢失风险 | 中 | 制定详细迁移方案，备份数据 |
| 权限控制复杂 | 安全漏洞 | 中 | 严格测试权限边界，代码审查 |

#### 10.2.2 性能优化策略

- **SQL缓存**：缓存解析后的SQL语句模板
- **租户字段缓存**：缓存表的租户字段信息  
- **上下文优化**：减少ThreadLocal的使用开销
- **索引优化**：为租户字段创建合适的索引
- **分页优化**：大数据量查询的分页处理

### 10.3 数据迁移方案

#### 10.3.1 现有数据处理

1. **添加租户字段**
```sql
-- 为现有业务表添加租户字段
ALTER TABLE user ADD COLUMN tenant_id VARCHAR(32) DEFAULT '10000' COMMENT '租户ID';
ALTER TABLE role ADD COLUMN tenant_id VARCHAR(32) DEFAULT '10000' COMMENT '租户ID';
-- 为租户字段添加索引
CREATE INDEX idx_user_tenant_id ON user(tenant_id);
CREATE INDEX idx_role_tenant_id ON role(tenant_id);
```

2. **数据迁移脚本**
```sql
-- 将现有数据分配给默认租户(平台租户)
UPDATE user SET tenant_id = '10000' WHERE tenant_id IS NULL;
UPDATE role SET tenant_id = '10000' WHERE tenant_id IS NULL;

-- 为特定用户创建专属租户
INSERT INTO tenant_info (id, name, code, status) VALUES ('tenant_001', '测试企业', 'TEST_COMPANY', 1);
UPDATE user SET tenant_id = 'tenant_001' WHERE id IN ('user1', 'user2');
```

#### 10.3.2 数据验证脚本

```sql
-- 检查租户字段完整性
SELECT table_name, 
       COUNT(*) as total_records,
       COUNT(tenant_id) as with_tenant_id,
       COUNT(*) - COUNT(tenant_id) as missing_tenant_id
FROM (
    SELECT 'user' as table_name, tenant_id FROM user
    UNION ALL
    SELECT 'role' as table_name, tenant_id FROM role
) t
GROUP BY table_name;

-- 检查孤立数据
SELECT 'user' as table_name, COUNT(*) as orphan_count
FROM user u 
LEFT JOIN tenant_info t ON u.tenant_id = t.id 
WHERE t.id IS NULL AND u.tenant_id IS NOT NULL
UNION ALL
SELECT 'role' as table_name, COUNT(*) as orphan_count  
FROM role r
LEFT JOIN tenant_info t ON r.tenant_id = t.id
WHERE t.id IS NULL AND r.tenant_id IS NOT NULL;
```

### 10.4 部署策略

#### 10.4.1 灰度发布方案

1. **第一阶段**：部署到测试环境，功能验证
2. **第二阶段**：小规模灰度（5%流量），监控关键指标
3. **第三阶段**：扩大灰度（20%流量），性能验证
4. **第四阶段**：全量发布，监控系统稳定性

#### 10.4.2 回滚方案

- **代码回滚**：保留上一版本代码，快速回滚
- **数据库回滚**：备份关键数据，准备回滚脚本
- **配置回滚**：关闭多租户功能开关，恢复单租户模式

---

## 11. 附录

### 11.1 核心类图

```mermaid
classDiagram
    class TenantContext {
        -ThreadLocal~String~ TENANT_ID
        -ThreadLocal~TenantEnum~ ISOLATION_TYPE
        +setTenantId(String)
        +getTenantId() String
        +setIsolationType(TenantEnum)
        +getIsolationType() TenantEnum
        +clear()
    }
    
    class TenantSqlInterceptor {
        -Set~String~ IGNORE_TABLES
        -Map~String,String~ FIELD_CACHE
        +intercept(Invocation) Object
        -addTenantFilter(String, String, TenantEnum) String
        -buildTenantCondition(String, TenantEnum) String
    }
    
    class TenantAspect {
        +beforeServiceMethod(JoinPoint)
        +afterServiceMethod()
        -determineIsolationType(Method, Class) TenantEnum
    }
    
    class TenantInterceptor {
        +preHandle(HttpServletRequest, HttpServletResponse, Object) boolean
        +afterCompletion(HttpServletRequest, HttpServletResponse, Object, Exception)
        -extractTenantId(HttpServletRequest) String
    }
    
    class TenantInfo {
        -String id
        -String name
        -String code
        -String logo
        -Integer maxUsers
        -Date expireTime
        -Integer status
    }
    
    class TenantUser {
        -String id
        -String userId
        -String tenantId
        -Integer roleType
        -Integer status
        -Date joinTime
    }
    
    class TenantPackage {
        -String id
        -String name
        -Integer type
        -BigDecimal buyoutPrice
        -BigDecimal pricePerUser
        -BigDecimal monthlyPrice
        -Integer maxUsers
    }
    
    TenantContext --> TenantSqlInterceptor
    TenantContext --> TenantAspect
    TenantContext --> TenantInterceptor
    TenantInfo ||--o{ TenantUser
    TenantInfo ||--|| TenantPackage
```

### 11.2 时序图

#### 11.2.1 用户登录时序图

```mermaid
sequenceDiagram
    participant User as 用户
    participant Frontend as 前端
    participant Gateway as 网关
    participant Auth as 认证服务
    participant Tenant as 租户服务
    participant DB as 数据库

    User->>Frontend: 1. 输入用户名密码
    Frontend->>Gateway: 2. 登录请求
    Gateway->>Auth: 3. 转发认证请求
    Auth->>DB: 4. 验证用户凭证
    DB-->>Auth: 5. 返回用户信息
    
    Auth->>Tenant: 6. 查询用户租户关系
    Tenant->>DB: 7. 查询tenant_user表
    DB-->>Tenant: 8. 返回租户列表
    Tenant-->>Auth: 9. 返回租户信息
    
    Auth-->>Gateway: 10. 返回登录结果+租户列表
    Gateway-->>Frontend: 11. 转发响应
    
    alt 多个租户
        Frontend->>User: 12a. 显示租户选择页面
        User->>Frontend: 13a. 选择租户
        Frontend->>Gateway: 14a. 提交租户选择
        Gateway->>Auth: 15a. 生成业务Token
        Auth-->>Gateway: 16a. 返回Token
        Gateway-->>Frontend: 17a. 返回Token
    else 单个租户
        Frontend->>Frontend: 12b. 自动选择唯一租户
        Frontend->>Gateway: 13b. 请求业务Token
        Gateway->>Auth: 14b. 生成业务Token
        Auth-->>Gateway: 15b. 返回Token
        Gateway-->>Frontend: 16b. 返回Token
    end
    
    Frontend->>User: 18. 登录成功，进入系统
```

#### 11.2.2 租户切换时序图

```mermaid
sequenceDiagram
    participant User as 用户
    participant Frontend as 前端
    participant Gateway as 网关
    participant Auth as 认证服务
    participant Tenant as 租户服务

    User->>Frontend: 1. 点击切换租户
    Frontend->>User: 2. 显示可用租户列表
    User->>Frontend: 3. 选择目标租户
    
    Frontend->>Gateway: 4. 切换租户请求(Token + 租户ID)
    Gateway->>Auth: 5. 验证Token有效性
    Auth->>Tenant: 6. 验证用户租户权限
    Tenant-->>Auth: 7. 返回权限验证结果
    
    alt 权限验证成功
        Auth->>Auth: 8. 生成新业务Token
        Auth-->>Gateway: 9. 返回新Token
        Gateway-->>Frontend: 10. 返回新Token
        Frontend->>Frontend: 11. 更新本地Token
        Frontend->>Gateway: 12. 使用新Token请求数据
        Gateway->>Gateway: 13. 设置租户上下文
        Gateway-->>Frontend: 14. 返回租户数据
        Frontend-->>User: 15. 显示新租户界面
    else 权限验证失败
        Auth-->>Gateway: 8. 返回权限错误
        Gateway-->>Frontend: 9. 返回错误信息
        Frontend-->>User: 10. 显示切换失败提示
    end
```

### 11.3 配置示例

#### 11.3.1 应用配置

```yaml
# application.yml
skyeye:
  tenant:
    enabled: true  # 启用多租户功能
    default-tenant-id: "10000"  # 默认租户ID(平台租户)
    sql-interceptor:
      enabled: true  # 启用SQL拦截器
      ignore-tables:  # 忽略租户过滤的表
        - sys_menu
        - sys_dict
        - tenant_info
        - tenant_package
    context:
      thread-local-timeout: 300000  # ThreadLocal超时时间(5分钟)
    security:
      audit-enabled: true  # 启用审计日志
      monitor-enabled: true  # 启用安全监控
```

#### 11.3.2 数据源配置

```yaml
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***********************************************************************************************************************
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:password}
    
  # MyBatis配置
  mybatis-plus:
    configuration:
      log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    global-config:
      db-config:
        logic-delete-field: deleteFlag
        logic-delete-value: 1
        logic-not-delete-value: 0
```

### 11.4 SQL脚本

#### 11.4.1 初始化脚本

```sql
-- 租户信息表
CREATE TABLE tenant_info (
    id VARCHAR(32) NOT NULL PRIMARY KEY COMMENT '租户ID',
    name VARCHAR(100) NOT NULL COMMENT '租户名称',
    code VARCHAR(50) NOT NULL UNIQUE COMMENT '租户编码',
    logo VARCHAR(200) COMMENT '租户LOGO地址',
    description TEXT COMMENT '租户描述',
    contact_name VARCHAR(50) COMMENT '联系人姓名',
    contact_phone VARCHAR(20) COMMENT '联系人电话',
    contact_email VARCHAR(100) COMMENT '联系人邮箱',
    max_users INT DEFAULT 100 COMMENT '最大用户数限制',
    expire_time DATETIME COMMENT '到期时间',
    status TINYINT DEFAULT 1 COMMENT '状态：1-正常 2-禁用 3-过期',
    package_id VARCHAR(32) COMMENT '关联套餐ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_code (code),
    INDEX idx_status (status),
    INDEX idx_package_id (package_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='租户信息表';

-- 用户租户关系表
CREATE TABLE tenant_user (
    id VARCHAR(32) NOT NULL PRIMARY KEY COMMENT '主键ID',
    user_id VARCHAR(32) NOT NULL COMMENT '用户ID',
    tenant_id VARCHAR(32) NOT NULL COMMENT '租户ID',
    role_type TINYINT DEFAULT 1 COMMENT '角色类型：1-普通用户 2-管理员 3-超级管理员',
    status TINYINT DEFAULT 1 COMMENT '状态：1-正常 2-禁用',
    join_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '加入时间',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    UNIQUE KEY uk_user_tenant (user_id, tenant_id),
    INDEX idx_user_id (user_id),
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户租户关系表';

-- 租户套餐表
CREATE TABLE tenant_package (
    id VARCHAR(32) NOT NULL PRIMARY KEY COMMENT '套餐ID',
    name VARCHAR(100) NOT NULL COMMENT '套餐名称',
    code VARCHAR(50) NOT NULL UNIQUE COMMENT '套餐编码',
    type TINYINT NOT NULL COMMENT '套餐类型：1-买断制 2-员工数量制 3-企业套餐制',
    description TEXT COMMENT '套餐描述',
    
    -- 买断制字段
    buyout_price DECIMAL(10,2) COMMENT '买断价格',
    max_users INT COMMENT '最大用户数',
    
    -- 员工数量制字段  
    price_per_user DECIMAL(8,2) COMMENT '每用户每月价格',
    min_users INT DEFAULT 1 COMMENT '最小用户数',
    
    -- 企业套餐制字段
    monthly_price DECIMAL(10,2) COMMENT '月费价格',
    included_users INT COMMENT '包含用户数',
    extra_user_price DECIMAL(8,2) COMMENT '超出用户单价',
    
    -- 通用字段
    features JSON COMMENT '功能特性配置',
    status TINYINT DEFAULT 1 COMMENT '状态：1-启用 2-禁用',
    sort_order INT DEFAULT 0 COMMENT '排序权重',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_type (type),
    INDEX idx_status (status),
    INDEX idx_sort_order (sort_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='租户套餐表';

-- 租户订阅表
CREATE TABLE tenant_subscription (
    id VARCHAR(32) NOT NULL PRIMARY KEY COMMENT '订阅ID',
    tenant_id VARCHAR(32) NOT NULL COMMENT '租户ID',
    package_id VARCHAR(32) NOT NULL COMMENT '套餐ID',
    start_time DATETIME NOT NULL COMMENT '开始时间',
    end_time DATETIME COMMENT '结束时间',
    status TINYINT DEFAULT 1 COMMENT '状态：1-正常 2-过期 3-暂停',
    
    -- 计费相关
    current_users INT DEFAULT 0 COMMENT '当前用户数',
    max_users INT COMMENT '最大用户数限制',
    last_bill_time DATETIME COMMENT '上次计费时间',
    next_bill_time DATETIME COMMENT '下次计费时间',
    
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_package_id (package_id),
    INDEX idx_status (status),
    INDEX idx_end_time (end_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='租户订阅表';

-- 租户计费记录表
CREATE TABLE tenant_billing (
    id VARCHAR(32) NOT NULL PRIMARY KEY COMMENT '账单ID',
    tenant_id VARCHAR(32) NOT NULL COMMENT '租户ID',
    subscription_id VARCHAR(32) NOT NULL COMMENT '订阅ID',
    bill_period_start DATETIME NOT NULL COMMENT '计费周期开始',
    bill_period_end DATETIME NOT NULL COMMENT '计费周期结束',
    
    -- 计费详情
    user_count INT NOT NULL COMMENT '计费用户数',
    unit_price DECIMAL(8,2) NOT NULL COMMENT '单价',
    total_amount DECIMAL(10,2) NOT NULL COMMENT '总金额',
    discount_amount DECIMAL(10,2) DEFAULT 0 COMMENT '优惠金额',
    final_amount DECIMAL(10,2) NOT NULL COMMENT '最终金额',
    
    billing_type TINYINT NOT NULL COMMENT '计费类型：1-月租 2-年费 3-买断',
    status TINYINT DEFAULT 1 COMMENT '状态：1-待支付 2-已支付 3-已取消',
    pay_time DATETIME COMMENT '支付时间',
    
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_subscription_id (subscription_id),
    INDEX idx_bill_period (bill_period_start, bill_period_end),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='租户计费记录表';
```

#### 11.4.2 初始数据

```sql
-- 插入平台租户
INSERT INTO tenant_info (id, name, code, description, max_users, status) 
VALUES ('10000', '平台管理', 'PLATFORM', '系统平台管理租户', 999999, 1);

-- 插入默认套餐
INSERT INTO tenant_package (id, name, code, type, description, buyout_price, max_users, features, status)
VALUES ('pkg_platform', '平台版', 'PLATFORM', 1, '平台管理专用版本', 0, 999999, 
        '{"modules":["all"],"storage":"unlimited","api_calls":"unlimited","support":"premium"}', 1);

-- 插入标准套餐
INSERT INTO tenant_package (id, name, code, type, description, monthly_price, included_users, extra_user_price, features, status)
VALUES ('pkg_standard', '标准版', 'STANDARD', 3, '适合中小企业的标准版本', 599.00, 10, 50.00,
        '{"modules":["erp","crm"],"storage":"100GB","api_calls":"10000","support":"standard"}', 1);

-- 插入按人计费套餐  
INSERT INTO tenant_package (id, name, code, type, description, price_per_user, min_users, features, status)
VALUES ('pkg_peruser', '按人计费版', 'PER_USER', 2, '按实际使用人数灵活计费', 100.00, 1,
        '{"modules":["erp","crm"],"storage":"50GB","api_calls":"5000","support":"basic"}', 1);
```

---

## 12. 开发任务清单 (TodoList)

### 阶段一：核心组件开发 (优先级：高，预计2周)

#### 12.1 创建多租户模块结构
- [ ] **TASK-001**: 创建skyeye-tenant父模块
  - 位置：`skyeye-business/skyeye-tenant/`
  - 创建父pom.xml，定义子模块依赖关系
  - 配置Maven多模块结构

- [ ] **TASK-002**: 创建tenant-core核心模块
  - 位置：`skyeye-business/skyeye-tenant/tenant-core/`
  - 创建基础包结构：context、interceptor、aspect、config
  - 配置基础依赖：Spring Boot、MyBatis、AOP

- [ ] **TASK-003**: 创建tenant-manager管理模块
  - 位置：`skyeye-business/skyeye-tenant/tenant-manager/`
  - 创建包结构：entity、service、controller、dao
  - 依赖tenant-core模块

- [ ] **TASK-004**: 创建tenant-package套餐模块
  - 位置：`skyeye-business/skyeye-tenant/tenant-package/`
  - 创建包结构：entity、service、billing
  - 依赖tenant-core模块

#### 12.2 实现租户上下文管理
- [ ] **TASK-005**: 实现TenantContext类
  - 文件：`tenant-core/src/main/java/com/skyeye/tenant/core/context/TenantContext.java`
  - 实现ThreadLocal租户ID存储
  - 实现隔离级别堆栈管理
  - 添加清理方法确保内存不泄露

- [ ] **TASK-006**: 实现TenantContextHolder辅助类
  - 文件：`tenant-core/src/main/java/com/skyeye/tenant/core/context/TenantContextHolder.java`
  - 提供租户信息的快速访问方法
  - 实现租户权限验证辅助方法

#### 12.3 实现SQL拦截器
- [ ] **TASK-007**: 实现TenantSqlInterceptor类
  - 文件：`tenant-core/src/main/java/com/skyeye/tenant/core/interceptor/TenantSqlInterceptor.java`
  - 实现MyBatis Interceptor接口
  - 支持SELECT、INSERT、UPDATE、DELETE语句的租户过滤
  - 实现SQL解析和条件注入逻辑

- [ ] **TASK-008**: 实现SqlParserUtils工具类
  - 文件：`tenant-core/src/main/java/com/skyeye/tenant/core/interceptor/SqlParserUtils.java`
  - 实现SQL语句解析功能
  - 支持复杂SQL的租户条件注入
  - 处理子查询和JOIN语句

- [ ] **TASK-009**: 实现租户字段检测缓存
  - 文件：`tenant-core/src/main/java/com/skyeye/tenant/core/interceptor/TenantFieldCache.java`
  - 实现表结构缓存机制
  - 动态检测表的租户字段
  - 提供缓存清理接口

#### 12.4 实现AOP切面
- [ ] **TASK-010**: 实现TenantAspect类
  - 文件：`tenant-core/src/main/java/com/skyeye/tenant/core/aspect/TenantAspect.java`
  - 拦截Service层方法调用
  - 实现隔离级别的动态确定逻辑
  - 支持注解优先级处理

- [ ] **TASK-011**: 实现IsolationLevelResolver类
  - 文件：`tenant-core/src/main/java/com/skyeye/tenant/core/aspect/IsolationLevelResolver.java`
  - 解析方法和类上的租户注解
  - 实现注解优先级算法
  - 支持继承关系的注解解析

#### 12.5 实现Web拦截器
- [ ] **TASK-012**: 实现TenantInterceptor类
  - 文件：`tenant-core/src/main/java/com/skyeye/tenant/core/interceptor/TenantInterceptor.java`
  - 实现HandlerInterceptor接口
  - 从HTTP请求中提取租户信息
  - 设置和清理租户上下文

- [ ] **TASK-013**: 实现TenantTokenParser类
  - 文件：`tenant-core/src/main/java/com/skyeye/tenant/core/interceptor/TenantTokenParser.java`
  - 从JWT Token中解析租户信息
  - 验证租户访问权限
  - 处理Token刷新逻辑

#### 12.6 配置和自动装配
- [ ] **TASK-014**: 实现TenantAutoConfiguration类
  - 文件：`tenant-core/src/main/java/com/skyeye/tenant/core/config/TenantAutoConfiguration.java`
  - 自动配置多租户组件
  - 注册拦截器和切面
  - 配置条件装配

- [ ] **TASK-015**: 创建配置属性类
  - 文件：`tenant-core/src/main/java/com/skyeye/tenant/core/config/TenantProperties.java`
  - 定义多租户配置项
  - 支持application.yml配置
  - 提供默认配置值

### 阶段二：用户租户管理 (优先级：高，预计1周)

#### 12.7 数据库设计和创建
- [ ] **TASK-016**: 创建数据库迁移脚本
  - 文件：`sql/tenant/V1.0.0__create_tenant_tables.sql`
  - 创建tenant_info、tenant_user、tenant_package等表
  - 添加必要的索引和约束
  - 插入初始数据

- [ ] **TASK-017**: 为现有表添加租户字段
  - 文件：`sql/tenant/V1.0.1__add_tenant_fields.sql`
  - 为业务表添加tenant_id字段
  - 创建相应索引
  - 更新现有数据为平台租户

#### 12.8 实体类和数据访问层
- [ ] **TASK-018**: 实现TenantInfo实体类
  - 文件：`tenant-manager/src/main/java/com/skyeye/tenant/manager/entity/TenantInfo.java`
  - 定义租户基本信息字段
  - 配置MyBatis-Plus注解
  - 实现序列化接口

- [ ] **TASK-019**: 实现TenantUser实体类
  - 文件：`tenant-manager/src/main/java/com/skyeye/tenant/manager/entity/TenantUser.java`
  - 定义用户租户关系字段
  - 添加关联查询配置
  - 实现角色权限字段

- [ ] **TASK-020**: 实现租户相关Dao接口
  - 文件：`tenant-manager/src/main/java/com/skyeye/tenant/manager/dao/TenantInfoDao.java`
  - 文件：`tenant-manager/src/main/java/com/skyeye/tenant/manager/dao/TenantUserDao.java`
  - 实现基础CRUD操作
  - 添加自定义查询方法

#### 12.9 业务服务层
- [ ] **TASK-021**: 实现TenantInfoService类
  - 文件：`tenant-manager/src/main/java/com/skyeye/tenant/manager/service/impl/TenantInfoServiceImpl.java`
  - 实现租户信息管理功能
  - 添加租户状态控制
  - 实现租户有效性验证

- [ ] **TASK-022**: 实现TenantUserService类
  - 文件：`tenant-manager/src/main/java/com/skyeye/tenant/manager/service/impl/TenantUserServiceImpl.java`
  - 实现用户租户关系管理
  - 添加权限验证功能
  - 支持批量操作

- [ ] **TASK-023**: 实现TenantSwitchService类
  - 文件：`tenant-manager/src/main/java/com/skyeye/tenant/manager/service/impl/TenantSwitchServiceImpl.java`
  - 实现租户切换核心逻辑
  - 验证切换权限
  - 处理Token更新

#### 12.10 控制器层
- [ ] **TASK-024**: 实现TenantController类
  - 文件：`tenant-manager/src/main/java/com/skyeye/tenant/manager/controller/TenantController.java`
  - 提供租户管理API接口
  - 实现租户切换接口
  - 添加权限控制注解

- [ ] **TASK-025**: 实现TenantUserController类
  - 文件：`tenant-manager/src/main/java/com/skyeye/tenant/manager/controller/TenantUserController.java`
  - 提供用户租户关系管理接口
  - 实现用户邀请功能
  - 添加角色权限管理

### 阶段三：套餐计费系统 (优先级：高，预计2周)

#### 12.11 套餐实体和数据访问
- [ ] **TASK-026**: 实现TenantPackage实体类
  - 文件：`tenant-package/src/main/java/com/skyeye/tenant/package/entity/TenantPackage.java`
  - 定义套餐基本信息
  - 支持多种计费模式配置
  - 添加功能特性JSON字段

- [ ] **TASK-027**: 实现TenantSubscription实体类
  - 文件：`tenant-package/src/main/java/com/skyeye/tenant/package/entity/TenantSubscription.java`
  - 定义租户订阅信息
  - 添加计费相关字段
  - 实现状态管理

- [ ] **TASK-028**: 实现TenantBilling实体类
  - 文件：`tenant-package/src/main/java/com/skyeye/tenant/package/entity/TenantBilling.java`
  - 定义计费记录结构
  - 支持详细计费信息
  - 添加支付状态管理

#### 12.12 计费策略实现
- [ ] **TASK-029**: 实现BillingStrategy接口
  - 文件：`tenant-package/src/main/java/com/skyeye/tenant/package/billing/BillingStrategy.java`
  - 定义计费策略标准接口
  - 支持多种计费模式
  - 提供扩展点

- [ ] **TASK-030**: 实现BuyoutBillingStrategy类
  - 文件：`tenant-package/src/main/java/com/skyeye/tenant/package/billing/BuyoutBillingStrategy.java`
  - 实现买断制计费逻辑
  - 处理一次性付费场景
  - 支持用户数限制

- [ ] **TASK-031**: 实现PerUserBillingStrategy类
  - 文件：`tenant-package/src/main/java/com/skyeye/tenant/package/billing/PerUserBillingStrategy.java`
  - 实现按人按月计费逻辑
  - 支持用户数动态变化
  - 处理月度账单生成

- [ ] **TASK-032**: 实现EnterpriseBillingStrategy类
  - 文件：`tenant-package/src/main/java/com/skyeye/tenant/package/billing/EnterpriseBillingStrategy.java`
  - 实现企业套餐计费逻辑
  - 处理基础套餐+超出用户计费
  - 支持阶梯定价

#### 12.13 套餐服务实现
- [ ] **TASK-033**: 实现TenantPackageService类
  - 文件：`tenant-package/src/main/java/com/skyeye/tenant/package/service/impl/TenantPackageServiceImpl.java`
  - 实现套餐管理功能
  - 支持套餐配置和启停
  - 添加套餐升级降级逻辑

- [ ] **TASK-034**: 实现TenantBillingService类
  - 文件：`tenant-package/src/main/java/com/skyeye/tenant/package/service/impl/TenantBillingServiceImpl.java`
  - 实现账单生成和管理
  - 支持定时计费任务
  - 处理用户数变化事件

- [ ] **TASK-035**: 实现BillingCalculatorService类
  - 文件：`tenant-package/src/main/java/com/skyeye/tenant/package/service/impl/BillingCalculatorServiceImpl.java`
  - 封装计费策略调用
  - 实现费用计算逻辑
  - 支持优惠折扣处理

#### 12.14 定时任务和事件处理
- [ ] **TASK-036**: 实现BillingScheduleTask类
  - 文件：`tenant-package/src/main/java/com/skyeye/tenant/package/task/BillingScheduleTask.java`
  - 实现月度账单生成任务
  - 支持租户到期检查
  - 添加异常处理和重试机制

- [ ] **TASK-037**: 实现UserCountChangeHandler类
  - 文件：`tenant-package/src/main/java/com/skyeye/tenant/package/handler/UserCountChangeHandler.java`
  - 监听用户数量变化事件
  - 触发计费调整
  - 检查套餐限制

### 阶段四：前端界面开发 (优先级：中，预计1.5周)

#### 12.15 租户选择和切换组件
- [ ] **TASK-038**: 创建TenantSelector Vue组件
  - 文件：`skyeye-frontend/skyeye-web/src/components/tenant/TenantSelector.vue`
  - 实现登录后租户选择界面
  - 支持租户列表展示
  - 添加租户信息显示

- [ ] **TASK-039**: 创建TenantSwitcher Vue组件
  - 文件：`skyeye-frontend/skyeye-web/src/components/tenant/TenantSwitcher.vue`
  - 实现顶部租户切换下拉菜单
  - 支持当前租户显示
  - 添加切换确认机制

- [ ] **TASK-040**: 实现租户状态管理
  - 文件：`skyeye-frontend/skyeye-web/src/store/modules/tenant.js`
  - 管理当前租户信息
  - 缓存可用租户列表
  - 处理租户切换状态

#### 12.16 套餐管理界面
- [ ] **TASK-041**: 创建PackageManagement Vue页面
  - 文件：`skyeye-frontend/skyeye-web/src/views/tenant/PackageManagement.vue`
  - 实现套餐列表展示
  - 支持套餐创建和编辑
  - 添加套餐状态管理

- [ ] **TASK-042**: 创建PackageConfigForm Vue组件
  - 文件：`skyeye-frontend/skyeye-web/src/components/tenant/PackageConfigForm.vue`
  - 实现套餐配置表单
  - 支持三种计费模式配置
  - 添加表单验证逻辑

- [ ] **TASK-043**: 创建BillingManagement Vue页面
  - 文件：`skyeye-frontend/skyeye-web/src/views/tenant/BillingManagement.vue`
  - 实现账单列表展示
  - 支持账单查询和导出
  - 添加支付状态管理

#### 12.17 租户管理界面
- [ ] **TASK-044**: 创建TenantManagement Vue页面
  - 文件：`skyeye-frontend/skyeye-web/src/views/tenant/TenantManagement.vue`
  - 实现租户信息管理
  - 支持租户创建和编辑
  - 添加租户状态控制

- [ ] **TASK-045**: 创建TenantUserManagement Vue页面
  - 文件：`skyeye-frontend/skyeye-web/src/views/tenant/TenantUserManagement.vue`
  - 实现租户用户管理
  - 支持用户邀请和移除
  - 添加角色权限管理

### 阶段五：安全和监控 (优先级：中，预计1周)

#### 12.18 安全控制实现
- [ ] **TASK-046**: 实现TenantSecurityService类
  - 文件：`tenant-core/src/main/java/com/skyeye/tenant/core/security/TenantSecurityService.java`
  - 实现租户权限验证
  - 添加接口级权限控制
  - 支持角色权限检查

- [ ] **TASK-047**: 实现TenantDataPermissionService类
  - 文件：`tenant-core/src/main/java/com/skyeye/tenant/core/security/TenantDataPermissionService.java`
  - 实现数据级权限控制
  - 支持数据访问过滤
  - 添加权限边界检查

#### 12.19 审计和监控
- [ ] **TASK-048**: 实现TenantAuditLogger类
  - 文件：`tenant-core/src/main/java/com/skyeye/tenant/core/audit/TenantAuditLogger.java`
  - 实现操作审计日志
  - 记录租户切换操作
  - 支持日志格式化输出

- [ ] **TASK-049**: 实现TenantSecurityMonitor类
  - 文件：`tenant-core/src/main/java/com/skyeye/tenant/core/monitor/TenantSecurityMonitor.java`
  - 实现安全监控功能
  - 检测异常访问模式
  - 支持安全告警

#### 12.20 异常处理和工具类
- [ ] **TASK-050**: 实现多租户异常类
  - 文件：`tenant-core/src/main/java/com/skyeye/tenant/core/exception/`
  - 定义TenantException、BillingException等
  - 实现统一异常处理
  - 添加错误码定义

- [ ] **TASK-051**: 实现TenantUtils工具类
  - 文件：`tenant-core/src/main/java/com/skyeye/tenant/core/util/TenantUtils.java`
  - 提供租户相关工具方法
  - 实现ID生成和验证
  - 添加常用转换方法

### 阶段六：集成测试和优化 (优先级：中，预计1周)

#### 12.21 单元测试
- [ ] **TASK-052**: 编写TenantContext单元测试
  - 文件：`tenant-core/src/test/java/com/skyeye/tenant/core/context/TenantContextTest.java`
  - 测试线程安全性
  - 验证上下文隔离
  - 测试内存泄露防护

- [ ] **TASK-053**: 编写SQL拦截器单元测试
  - 文件：`tenant-core/src/test/java/com/skyeye/tenant/core/interceptor/TenantSqlInterceptorTest.java`
  - 测试各种SQL语句处理
  - 验证租户条件注入
  - 测试性能影响

- [ ] **TASK-054**: 编写计费策略单元测试
  - 文件：`tenant-package/src/test/java/com/skyeye/tenant/package/billing/`
  - 测试三种计费策略
  - 验证计费准确性
  - 测试边界条件

#### 12.22 集成测试
- [ ] **TASK-055**: 编写端到端功能测试
  - 文件：`tenant-test/src/test/java/com/skyeye/tenant/integration/`
  - 测试完整租户流程
  - 验证数据隔离效果
  - 测试租户切换功能

- [ ] **TASK-056**: 编写性能测试
  - 文件：`tenant-test/src/test/java/com/skyeye/tenant/performance/`
  - 测试SQL拦截器性能
  - 验证高并发场景
  - 优化缓存策略

#### 12.23 文档和部署
- [ ] **TASK-057**: 完善API文档
  - 文件：各Controller类添加Swagger注解
  - 生成完整API文档
  - 添加使用示例
  - 更新接口说明

- [ ] **TASK-058**: 编写部署脚本
  - 文件：`scripts/tenant/deploy.sh`
  - 自动化部署流程
  - 数据库迁移脚本
  - 配置检查和验证

- [ ] **TASK-059**: 编写用户手册
  - 文件：`docs/tenant/user-manual.md`
  - 租户管理员使用指南
  - 常见问题解答
  - 故障排查手册

### 阶段七：系统集成 (优先级：低，预计0.5周)

#### 12.24 与现有模块集成
- [ ] **TASK-060**: 集成到主启动类
  - 修改各微服务的启动类
  - 添加多租户模块依赖
  - 配置自动装配

- [ ] **TASK-061**: 更新网关配置
  - 修改Zuul网关配置
  - 添加租户信息转发
  - 配置路由规则

- [ ] **TASK-062**: 最终验收测试
  - 完整功能验收
  - 性能基准测试
  - 安全漏洞扫描
  - 用户体验测试

---

### 任务执行说明

#### 优先级说明
- **高优先级**：核心功能，必须优先完成
- **中优先级**：重要功能，在核心功能完成后实现
- **低优先级**：辅助功能，最后完成

#### 任务依赖关系
- TASK-001 到 TASK-004：模块创建，必须最先完成
- TASK-005 到 TASK-015：核心组件，按顺序实现
- TASK-016 到 TASK-025：依赖核心组件完成
- TASK-026 到 TASK-037：依赖租户管理完成
- TASK-038 到 TASK-045：可与后端并行开发
- TASK-046 到 TASK-062：在主要功能完成后实现

#### 质量要求
- 每个任务完成后必须进行代码审查
- 关键组件必须有对应的单元测试
- 重要接口必须有集成测试
- 所有代码必须符合项目编码规范

#### 验收标准
- 功能完整性：实现设计文档中的所有功能
- 性能要求：SQL拦截器延迟<5ms，系统整体性能下降<10%
- 安全要求：通过安全漏洞扫描，无高危漏洞
- 兼容性要求：与现有功能100%兼容，不影响现有业务

---

**多租户架构开发任务清单编制完成**

本任务清单基于前述设计文档，将整个多租户系统的开发工作分解为62个具体任务，涵盖了从基础架构到前端界面的完整开发流程。每个任务都有明确的文件路径、实现要求和验收标准，便于后续AI开发时能够准确理解和执行。